# 如何使用 JSON Log Parser 扩展

## 📍 查看解析结果的位置

当您使用 "Parse Selected JSON" 功能后，解析结果会显示在 **两个位置**，您可以选择最方便的方式：

### 🎯 方法1：专用标签页（推荐）

1. **自动打开**：
   - 解析JSON后，会自动打开一个专用的 **"📋 JSON Parser"** 标签页
   - 这个标签页会出现在VS Code底部面板区域，与Terminal、Output、Problems等标签页并列
   - 提供美观的界面和完整的功能

2. **手动打开**：
   - 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
   - 输入 **"JSON Log Parser: Open Dedicated Tab"**
   - 或者在通知消息中点击 **"Show Dedicated Tab"**

### 🎯 方法2：输出面板（备用）

1. **通过通知消息**：
   - 在通知消息中点击 **"Show Output Panel"**

2. **手动打开**：
   - 点击底部面板的 **"OUTPUT"** 按钮
   - 在输出面板的下拉菜单中选择 **"JSON Parser Results"**
   - 或者使用命令面板：**"JSON Log Parser: Show Parsed JSON Results"**

### 🔔 智能通知

- 解析成功后会显示通知，提供两个选项：
  - **"Show Dedicated Tab"** - 打开专用标签页（推荐）
  - **"Show Output Panel"** - 打开输出面板

## 🔧 使用步骤

### 手动解析选中文本

1. **选择包含JSON的文本**：
   ```
   API Response: "{\"status\":\"success\",\"data\":{\"id\":123}}"
   ```

2. **执行解析命令**：
   - **方法A**: 右键点击选中文本 → 选择 "Parse Selected JSON"
   - **方法B**: 按 `Ctrl+Shift+P` → 输入 "Parse Selected JSON"

3. **查看结果**：
   - 会显示通知消息告诉您找到了多少个JSON对象
   - 专用的 "📋 JSON Parser" 标签页会自动打开
   - 在这里可以看到美观格式化的JSON和原始文本
   - 也可以选择在输出面板中查看

### 自动检测功能

扩展会自动监控多个来源，当检测到转义JSON时会自动显示：

#### 📁 文件自动检测
- **支持的文件类型**：.log, .txt, .json, .js, .ts, .py 等
- **自动监控**：当您打开或编辑包含JSON的文件时
- **智能过滤**：只处理可能包含JSON的文件

#### 💻 终端自动检测
- **实时监控**：VS Code集成终端的输出
- **示例命令**：
   ```bash
   echo "API Response: \"{\\\"status\\\":\\\"success\\\"}\""
   echo "Error: \"{\\\"code\\\":400,\\\"message\\\":\\\"Bad Request\\\"}\""
   ```

#### 📤 输出面板检测
- **监控范围**：各种扩展的输出面板
- **日志文件**：自动检测日志输出中的JSON

#### 🔔 智能通知
- 检测到2个或更多JSON对象时显示通知
- 提供两个选项：专用标签页或输出面板
- 自动打开专用标签页显示结果

## 🎯 测试示例

使用以下示例测试扩展功能：

### 示例1: 双转义JSON
```
Log: "{\"user\":{\"id\":123,\"name\":\"John\"},\"action\":\"login\"}"
```

### 示例2: OpenAI API日志
```
Request: "{\"model\":\"gpt-4\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}]}"
```

### 示例3: 简单JSON
```
Data: {"timestamp":"2025-06-20","level":"info","message":"Success"}
```

## 🔍 专用标签页功能（推荐）

在 "📋 JSON Parser" 专用标签页中，您可以享受：

1. **美观的界面设计**：
   - 📋 清晰的标题和统计信息
   - 每个JSON对象都有独立的卡片显示
   - 时间戳显示解析时间
   - 来源信息（手动选择、终端等）

2. **完整的交互功能**：
   - **"Copy Formatted"** 按钮 - 复制格式化的JSON
   - **"Copy Original"** 按钮 - 复制原始转义字符串
   - **"Clear All"** 按钮 - 清除所有结果

3. **专用显示空间**：
   - 独立的标签页，不与其他输出混合
   - 与Terminal、Output、Problems等标签页并列显示
   - 易于发现和访问

## 🔍 输出面板功能（备用）

在 "JSON Parser Results" 输出面板中，您可以看到：

1. **简洁的文本展示**：
   - 📋 每个JSON对象都有编号
   - 原始转义字符串
   - 格式化的JSON（带缩进）
   - 分隔线便于区分不同的JSON对象

2. **基本复制功能**：
   - 可以直接选择和复制任何部分
   - 原始文本和格式化JSON都可复制

## ⚙️ 配置选项

在VS Code设置中搜索 "JSON Log Parser" 来配置：

- `jsonLogParser.enabled` - 启用/禁用扩展
- `jsonLogParser.autoDetect` - 自动检测终端输出
- `jsonLogParser.autoDetectInFiles` - 自动检测文件中的JSON
- `jsonLogParser.highlightColor` - 高亮颜色
- `jsonLogParser.maxLogSize` - 最大处理大小
- `jsonLogParser.showInSidebar` - 显示侧边栏面板

## 🚨 故障排除

### 如果看不到结果：

1. **检查扩展是否启用**：
   - 在扩展面板中确认 "JSON Log Parser" 已启用

2. **检查配置**：
   - 确保 `jsonLogParser.enabled` 设置为 `true`
   - 确保 `jsonLogParser.showInSidebar` 设置为 `true`

3. **重新加载窗口**：
   - 按 `Ctrl+Shift+P` → 输入 "Developer: Reload Window"

4. **查看开发者工具**：
   - 按 `Ctrl+Shift+I` 查看控制台是否有错误信息

### 如果解析失败：

1. **检查JSON格式** - 确保JSON语法正确
2. **检查大小限制** - 默认限制为10000字符
3. **尝试不同的选择范围** - 确保选中了完整的JSON字符串

## 📞 获取帮助

如果遇到问题：

1. 查看 `DEVELOPMENT.md` 了解技术细节
2. 运行 `npm test` 确保核心功能正常
3. 在开发者工具中查看错误信息
4. 尝试重新编译和重启扩展
