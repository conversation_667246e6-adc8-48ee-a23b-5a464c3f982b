2025-06-21 14:00:00 [INFO] Starting automated JSON detection test
2025-06-21 14:00:01 [DEBUG] API Request: "{\\"method\\":\\"GET\\",\\"url\\":\\"https://api.example.com/users\\",\\"headers\\":{\\"Authorization\\":\\"Bearer token123\\"}}"
2025-06-21 14:00:02 [INFO] Response received: {"status":200,"data":[{"id":1,"name":"<PERSON>","email":"<EMAIL>"},{"id":2,"name":"<PERSON>","email":"<EMAIL>"}],"meta":{"total":2,"page":1}}
2025-06-21 14:00:03 [ERROR] Validation failed: '{"error":"invalid_input","field":"email","message":"Email format is invalid","code":400}'
2025-06-21 14:00:04 [DEBUG] Retry attempt: [{"attempt":1,"max_retries":3,"delay_ms":1000,"timestamp":"2025-06-21T14:00:04.123Z"}]
2025-06-21 14:00:05 [INFO] Database query: "{\\"query\\":\\"SELECT * FROM users WHERE active = true\\",\\"params\\":[],\\"execution_time_ms\\":45}"
2025-06-21 14:00:06 [WARN] Rate limit warning: {"remaining_requests":10,"reset_time":"2025-06-21T15:00:00Z","current_usage":90}
2025-06-21 14:00:07 [INFO] Cache operation: '{"operation":"SET","key":"user:123","value":{"name":"Charlie","last_login":"2025-06-21T14:00:07Z"},"ttl":3600}'
2025-06-21 14:00:08 [DEBUG] WebSocket message: "{\\"type\\":\\"notification\\",\\"payload\\":{\\"user_id\\":123,\\"message\\":\\"Welcome back!\\",\\"timestamp\\":\\"2025-06-21T14:00:08.456Z\\"}}"
2025-06-21 14:00:09 [INFO] Test completed successfully
