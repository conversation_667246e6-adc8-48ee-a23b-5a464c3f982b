# Change Log

All notable changes to the "JSON Log Parser" extension will be documented in this file.

## [1.0.0] - 2025-06-21

### Added
- **Automatic JSON Detection**: Auto-detects escaped JSON in terminal output, log files, and clipboard
- **Dedicated Tab Display**: Clean, focused display in a dedicated "JSON Parser" tab
- **Smart Clipboard Monitoring**: Automatically detects JSON when copying terminal output
- **File System Monitoring**: Watches log files (*.log, *.out, *.err) for JSON content changes
- **Multiple JSON Format Support**:
  - Double-escaped JSON: `"{\\"key\\":\\"value\\"}"`
  - Single-escaped JSON: `{"key":"value"}`
  - JSON arrays: `[{"item1": "value1"}]`
  - Single-quoted JSON: `'{"key":"value"}'`
- **Manual Parsing Commands**:
  - Parse Selected JSON
  - Parse JSON from Clipboard
  - Simulate Terminal JSON Output
- **Copy Functionality**: Easy copying of both original and formatted JSON
- **Configurable Settings**: Enable/disable auto-detection features
- **Real-time Processing**: Processes JSON as it appears with intelligent debouncing

### Features
- **Zero Configuration**: Works out of the box with sensible defaults
- **Performance Optimized**: Efficient monitoring with minimal resource usage
- **Developer Friendly**: Perfect for API development, debugging, and log analysis
- **Cross-platform**: Works on Windows, macOS, and Linux

### Technical Details
- Built with TypeScript for reliability and maintainability
- Comprehensive error handling and fallback mechanisms
- Intelligent pattern matching for various JSON formats
- Debounced processing to prevent performance issues
- Memory-efficient with automatic cleanup
