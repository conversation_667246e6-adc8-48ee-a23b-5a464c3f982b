2025-06-20 20:04:05.397 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:04:05.397 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:04:05.398 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:04:05.599 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-06-20 20:04:05.651 [info] Started initializing default profile extensions in extensions installation folder. file:///Users/<USER>/Projects/ai/rag/mlflow/json-log-parser/.vscode-test/extensions
2025-06-20 20:04:05.681 [info] ComputeTargetPlatform: darwin-arm64
2025-06-20 20:04:05.750 [info] Completed initializing default profile extensions in extensions installation folder. file:///Users/<USER>/Projects/ai/rag/mlflow/json-log-parser/.vscode-test/extensions
2025-06-20 20:04:05.775 [info] Loading development extension at /Users/<USER>/Projects/ai/rag/mlflow/json-log-parser
2025-06-20 20:04:05.910 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-06-20 20:04:06.870 [info] Started local extension host with pid 82173.
2025-06-20 20:04:07.280 [info] [0m%s%s[0m  
2025-06-20 20:04:07.300 [info] [0m%s%s[0m    JsonParser Test Suite
2025-06-20 20:04:07.308 [info] JSON Log Parser extension is now active!
2025-06-20 20:04:07.309 [info]   [32m  ✔[0m[90m %s[0m should parse double-escaped JSON
2025-06-20 20:04:07.311 [info]   [31m  %d) %s[0m 1 should parse single-escaped JSON
2025-06-20 20:04:07.311 [info]   [32m  ✔[0m[90m %s[0m should parse JSON array
2025-06-20 20:04:07.311 [info]   [32m  ✔[0m[90m %s[0m should handle invalid JSON gracefully
2025-06-20 20:04:07.312 [info]   [32m  ✔[0m[90m %s[0m should detect JSON presence
2025-06-20 20:04:07.312 [info]   [32m  ✔[0m[90m %s[0m should handle multiple JSON objects
2025-06-20 20:04:07.315 [info]   [32m  ✔[0m[90m %s[0m should parse OpenAI API log example
2025-06-20 20:04:07.315 [info] [92m [0m[32m %d passing[0m[90m (%s)[0m 6 36ms
2025-06-20 20:04:07.315 [info] [31m  %d failing[0m 1
2025-06-20 20:04:07.316 [info] [0m  %s) %s:
%s[0m[90m
%s
[0m 1 JsonParser Test Suite
       should parse single-escaped JSON 
      [31mAssertionError [ERR_ASSERTION]: Expected values to be strictly equal:

0 !== 1
[0m
      [32m+ expected[0m [31m- actual[0m

      [31m-0[0m
      [32m+1[0m
         	at Context.<anonymous> (out/test/jsonParser.test.js:64:16)
  	at process.processImmediate (node:internal/timers:485:21)
2025-06-20 20:04:07.317 [error] Error: 1 tests failed.
	at /Users/<USER>/Projects/ai/rag/mlflow/json-log-parser/out/test/suite/index.js:62:23
	at done (/Users/<USER>/Projects/ai/rag/mlflow/json-log-parser/node_modules/mocha/lib/mocha.js:1028:7)
