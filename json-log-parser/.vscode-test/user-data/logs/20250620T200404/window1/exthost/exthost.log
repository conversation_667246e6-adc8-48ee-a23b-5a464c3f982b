2025-06-20 20:04:07.122 [info] Extension host with pid 82173 started
2025-06-20 20:04:07.130 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-06-20 20:04:07.153 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-20 20:04:07.155 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-20 20:04:07.173 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-20 20:04:07.234 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-06-20 20:04:07.234 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-06-20 20:04:07.240 [info] Eager extensions activated
2025-06-20 20:04:07.281 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:04:07.282 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:04:07.293 [info] ExtensionService#_doActivateExtension undefined_publisher.json-log-parser, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:04:07.317 [info] Extension host terminating: renderer closed the MessagePort
2025-06-20 20:04:07.321 [info] Extension host with pid 82173 exiting with code 0
