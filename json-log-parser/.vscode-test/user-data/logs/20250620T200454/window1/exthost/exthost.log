2025-06-20 20:04:56.564 [info] Extension host with pid 83393 started
2025-06-20 20:04:56.575 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-06-20 20:04:56.596 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-20 20:04:56.599 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-20 20:04:56.617 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-20 20:04:56.652 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-06-20 20:04:56.652 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-06-20 20:04:56.668 [info] Eager extensions activated
2025-06-20 20:04:56.697 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:04:56.698 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:04:56.705 [info] ExtensionService#_doActivateExtension undefined_publisher.json-log-parser, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:04:56.723 [info] Extension host terminating: renderer closed the MessagePort
2025-06-20 20:04:56.729 [info] Extension host with pid 83393 exiting with code 0
