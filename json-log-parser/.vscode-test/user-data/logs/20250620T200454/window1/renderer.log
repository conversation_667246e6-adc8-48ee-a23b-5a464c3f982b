2025-06-20 20:04:55.232 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:04:55.233 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:04:55.233 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:04:55.450 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-06-20 20:04:55.572 [info] ComputeTargetPlatform: darwin-arm64
2025-06-20 20:04:55.633 [info] Loading development extension at /Users/<USER>/Projects/ai/rag/mlflow/json-log-parser
2025-06-20 20:04:55.725 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-06-20 20:04:56.308 [info] Started local extension host with pid 83393.
2025-06-20 20:04:56.697 [info] [0m%s%s[0m  
2025-06-20 20:04:56.707 [info] [0m%s%s[0m    JsonParser Test Suite
2025-06-20 20:04:56.714 [info] JSON Log Parser extension is now active!
2025-06-20 20:04:56.717 [info]   [32m  ✔[0m[90m %s[0m should parse double-escaped JSON
2025-06-20 20:04:56.718 [info]   [32m  ✔[0m[90m %s[0m should parse single-escaped JSON
2025-06-20 20:04:56.718 [info]   [32m  ✔[0m[90m %s[0m should parse JSON array
2025-06-20 20:04:56.719 [info]   [32m  ✔[0m[90m %s[0m should handle invalid JSON gracefully
2025-06-20 20:04:56.720 [info]   [32m  ✔[0m[90m %s[0m should detect JSON presence
2025-06-20 20:04:56.720 [info]   [32m  ✔[0m[90m %s[0m should handle multiple JSON objects
2025-06-20 20:04:56.721 [info]   [32m  ✔[0m[90m %s[0m should parse OpenAI API log example
2025-06-20 20:04:56.721 [info] [92m [0m[32m %d passing[0m[90m (%s)[0m 7 25ms
