2025-06-21 09:45:29.546 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-21 09:45:29.546 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-21 09:45:29.547 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-21 09:45:29.869 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-06-21 09:45:29.872 [info] ComputeTargetPlatform: darwin-arm64
2025-06-21 09:45:30.141 [info] Loading development extension at /Users/<USER>/Projects/ai/rag/mlflow/json-log-parser
2025-06-21 09:45:30.162 [warning] [undefined_publisher.json-log-parser]: View container 'panel' does not exist and all views registered to it will be added to 'Explorer'.
2025-06-21 09:45:30.226 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-06-21 09:45:30.557 [info] Started local extension host with pid 69936.
2025-06-21 09:45:31.107 [info] [0m%s%s[0m  
2025-06-21 09:45:31.127 [info] [0m%s%s[0m    JsonParser Test Suite
2025-06-21 09:45:31.134 [info] JSON Log Parser extension is now active!
2025-06-21 09:45:31.138 [info]   [32m  ✔[0m[90m %s[0m should parse double-escaped JSON
2025-06-21 09:45:31.138 [info]   [32m  ✔[0m[90m %s[0m should parse single-escaped JSON
2025-06-21 09:45:31.138 [info]   [32m  ✔[0m[90m %s[0m should parse JSON array
2025-06-21 09:45:31.139 [info]   [32m  ✔[0m[90m %s[0m should handle invalid JSON gracefully
2025-06-21 09:45:31.139 [info]   [32m  ✔[0m[90m %s[0m should detect JSON presence
2025-06-21 09:45:31.139 [info]   [32m  ✔[0m[90m %s[0m should handle multiple JSON objects
2025-06-21 09:45:31.139 [info]   [32m  ✔[0m[90m %s[0m should parse OpenAI API log example
2025-06-21 09:45:31.140 [info] [92m [0m[32m %d passing[0m[90m (%s)[0m 7 33ms
