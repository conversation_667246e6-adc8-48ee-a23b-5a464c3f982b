2025-06-21 09:45:30.946 [info] Extension host with pid 69936 started
2025-06-21 09:45:30.959 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-06-21 09:45:30.982 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-21 09:45:30.984 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-21 09:45:31.004 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-21 09:45:31.070 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-06-21 09:45:31.070 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-06-21 09:45:31.076 [info] Eager extensions activated
2025-06-21 09:45:31.109 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 09:45:31.110 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 09:45:31.119 [info] ExtensionService#_doActivateExtension undefined_publisher.json-log-parser, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 09:45:31.140 [info] Extension host terminating: renderer closed the MessagePort
2025-06-21 09:45:31.145 [info] Extension host with pid 69936 exiting with code 0
