2025-06-20 20:08:58.598 [info] Extension host with pid 87317 started
2025-06-20 20:08:58.612 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-06-20 20:08:58.633 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-20 20:08:58.634 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-20 20:08:58.655 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-20 20:08:58.696 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-06-20 20:08:58.696 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-06-20 20:08:58.713 [info] Eager extensions activated
2025-06-20 20:08:58.751 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:08:58.752 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:08:58.761 [info] ExtensionService#_doActivateExtension undefined_publisher.json-log-parser, startup: false, activationEvent: 'onStartupFinished'
2025-06-20 20:08:58.773 [info] Extension host terminating: renderer closed the MessagePort
2025-06-20 20:08:58.778 [info] Extension host with pid 87317 exiting with code 0
