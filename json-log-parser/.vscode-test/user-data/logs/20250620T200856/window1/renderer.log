2025-06-20 20:08:57.660 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:08:57.661 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:08:57.661 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-06-20 20:08:58.025 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-06-20 20:08:58.065 [info] ComputeTargetPlatform: darwin-arm64
2025-06-20 20:08:58.221 [info] Started local extension host with pid 87317.
2025-06-20 20:08:58.306 [info] Loading development extension at /Users/<USER>/Projects/ai/rag/mlflow/json-log-parser
2025-06-20 20:08:58.354 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-06-20 20:08:58.749 [info] [0m%s%s[0m  
2025-06-20 20:08:58.749 [info] [0m%s%s[0m    JsonParser Test Suite
2025-06-20 20:08:58.769 [info] JSON Log Parser extension is now active!
2025-06-20 20:08:58.771 [info]   [32m  ✔[0m[90m %s[0m should parse double-escaped JSON
2025-06-20 20:08:58.772 [info]   [32m  ✔[0m[90m %s[0m should parse single-escaped JSON
2025-06-20 20:08:58.772 [info]   [32m  ✔[0m[90m %s[0m should parse JSON array
2025-06-20 20:08:58.772 [info]   [32m  ✔[0m[90m %s[0m should handle invalid JSON gracefully
2025-06-20 20:08:58.773 [info]   [32m  ✔[0m[90m %s[0m should detect JSON presence
2025-06-20 20:08:58.773 [info]   [32m  ✔[0m[90m %s[0m should handle multiple JSON objects
2025-06-20 20:08:58.773 [info]   [32m  ✔[0m[90m %s[0m should parse OpenAI API log example
2025-06-20 20:08:58.773 [info] [92m [0m[32m %d passing[0m[90m (%s)[0m 7 25ms
