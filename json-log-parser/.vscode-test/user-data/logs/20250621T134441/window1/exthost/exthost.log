2025-06-21 13:44:44.516 [info] Extension host with pid 75956 started
2025-06-21 13:44:44.528 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-06-21 13:44:44.551 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-06-21 13:44:44.553 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-06-21 13:44:44.574 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-06-21 13:44:44.635 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-06-21 13:44:44.635 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-06-21 13:44:44.642 [info] Eager extensions activated
2025-06-21 13:44:44.682 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 13:44:44.683 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 13:44:44.693 [info] ExtensionService#_doActivateExtension undefined_publisher.json-log-parser, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 13:44:44.718 [info] Extension host terminating: renderer closed the MessagePort
2025-06-21 13:44:44.722 [info] Extension host with pid 75956 exiting with code 0
