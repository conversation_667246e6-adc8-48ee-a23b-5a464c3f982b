<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>Code</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>h</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>c.icns</string>
			<key>CFBundleTypeName</key>
			<string>C header file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>c</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>c.icns</string>
			<key>CFBundleTypeName</key>
			<string>C source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>gitattributes</string>
				<string>gitconfig</string>
				<string>gitignore</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>config.icns</string>
			<key>CFBundleTypeName</key>
			<string>Git configuration file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>asp</string>
				<string>aspx</string>
				<string>cshtml</string>
				<string>jshtm</string>
				<string>jsp</string>
				<string>phtml</string>
				<string>shtml</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>html.icns</string>
			<key>CFBundleTypeName</key>
			<string>HTML template document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>bat</string>
				<string>cmd</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>bat.icns</string>
			<key>CFBundleTypeName</key>
			<string>Windows command script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>bowerrc</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>bower.icns</string>
			<key>CFBundleTypeName</key>
			<string>Bower document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>config</string>
				<string>editorconfig</string>
				<string>ini</string>
				<string>cfg</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>config.icns</string>
			<key>CFBundleTypeName</key>
			<string>Configuration file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>hh</string>
				<string>hpp</string>
				<string>hxx</string>
				<string>h++</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>cpp.icns</string>
			<key>CFBundleTypeName</key>
			<string>C++ header file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>cc</string>
				<string>cpp</string>
				<string>cxx</string>
				<string>c++</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>cpp.icns</string>
			<key>CFBundleTypeName</key>
			<string>C++ source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>m</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Objective-C source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>mm</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>cpp.icns</string>
			<key>CFBundleTypeName</key>
			<string>Objective-C++ source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>cs</string>
				<string>csx</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>csharp.icns</string>
			<key>CFBundleTypeName</key>
			<string>C# source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>css</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>css.icns</string>
			<key>CFBundleTypeName</key>
			<string>CSS</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>go</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>go.icns</string>
			<key>CFBundleTypeName</key>
			<string>Go source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>htm</string>
				<string>html</string>
				<string>xhtml</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>html.icns</string>
			<key>CFBundleTypeName</key>
			<string>HTML document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>jade</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>jade.icns</string>
			<key>CFBundleTypeName</key>
			<string>Jade document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>jav</string>
				<string>java</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>java.icns</string>
			<key>CFBundleTypeName</key>
			<string>Java document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>js</string>
				<string>jscsrc</string>
				<string>jshintrc</string>
				<string>mjs</string>
				<string>cjs</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>javascript.icns</string>
			<key>CFBundleTypeName</key>
			<string>Javascript file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>json</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>json.icns</string>
			<key>CFBundleTypeName</key>
			<string>JSON document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>less</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>less.icns</string>
			<key>CFBundleTypeName</key>
			<string>Less document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>markdown</string>
				<string>md</string>
				<string>mdoc</string>
				<string>mdown</string>
				<string>mdtext</string>
				<string>mdtxt</string>
				<string>mdwn</string>
				<string>mkd</string>
				<string>mkdn</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>markdown.icns</string>
			<key>CFBundleTypeName</key>
			<string>Markdown document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>php</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>php.icns</string>
			<key>CFBundleTypeName</key>
			<string>PHP source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>ps1</string>
				<string>psd1</string>
				<string>psm1</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>powershell.icns</string>
			<key>CFBundleTypeName</key>
			<string>Powershell script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>py</string>
				<string>pyi</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>python.icns</string>
			<key>CFBundleTypeName</key>
			<string>Python script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>gemspec</string>
				<string>rb</string>
				<string>erb</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>ruby.icns</string>
			<key>CFBundleTypeName</key>
			<string>Ruby source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>scss</string>
				<string>sass</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>sass.icns</string>
			<key>CFBundleTypeName</key>
			<string>SASS file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>sql</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>sql.icns</string>
			<key>CFBundleTypeName</key>
			<string>SQL script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>ts</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>typescript.icns</string>
			<key>CFBundleTypeName</key>
			<string>TypeScript file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>tsx</string>
				<string>jsx</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>react.icns</string>
			<key>CFBundleTypeName</key>
			<string>React source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>vue</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>vue.icns</string>
			<key>CFBundleTypeName</key>
			<string>Vue source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>ascx</string>
				<string>csproj</string>
				<string>dtd</string>
				<string>plist</string>
				<string>wxi</string>
				<string>wxl</string>
				<string>wxs</string>
				<string>xml</string>
				<string>xaml</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>xml.icns</string>
			<key>CFBundleTypeName</key>
			<string>XML document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>eyaml</string>
				<string>eyml</string>
				<string>yaml</string>
				<string>yml</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>yaml.icns</string>
			<key>CFBundleTypeName</key>
			<string>YAML document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>bash</string>
				<string>bash_login</string>
				<string>bash_logout</string>
				<string>bash_profile</string>
				<string>bashrc</string>
				<string>profile</string>
				<string>rhistory</string>
				<string>rprofile</string>
				<string>sh</string>
				<string>zlogin</string>
				<string>zlogout</string>
				<string>zprofile</string>
				<string>zsh</string>
				<string>zshenv</string>
				<string>zshrc</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>shell.icns</string>
			<key>CFBundleTypeName</key>
			<string>Shell script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>clj</string>
				<string>cljs</string>
				<string>cljx</string>
				<string>clojure</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Clojure source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>code-workspace</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>VS Code workspace file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>coffee</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>CoffeeScript source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>csv</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Comma Separated Values</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>cmake</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>CMake script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>dart</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Dart script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>diff</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Diff file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>dockerfile</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Dockerfile</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>gradle</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Gradle file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>groovy</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Groovy script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>makefile</string>
				<string>mk</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Makefile</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>lua</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Lua script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>pug</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Pug document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>ipynb</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Jupyter</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>lock</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Lockfile</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>log</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Log file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>txt</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Plain Text File</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>xcodeproj</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Xcode project file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>xcworkspace</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Xcode workspace file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>vb</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Visual Basic script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>r</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>R source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>rs</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Rust source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>rst</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Restructured Text document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>tex</string>
				<string>cls</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>LaTeX document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>fs</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>F# source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>fsi</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>F# signature file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>fsx</string>
				<string>fsscript</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>F# script</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>svg</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>SVG document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>toml</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>TOML document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>swift</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Swift source code</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>containerfile</string>
				<string>ctp</string>
				<string>dot</string>
				<string>edn</string>
				<string>handlebars</string>
				<string>hbs</string>
				<string>ml</string>
				<string>mli</string>
				<string>pl</string>
				<string>pl6</string>
				<string>pm</string>
				<string>pm6</string>
				<string>pod</string>
				<string>pp</string>
				<string>properties</string>
				<string>psgi</string>
				<string>rt</string>
				<string>t</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Visual Studio Code document</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array/>
			<key>CFBundleTypeIconFile</key>
			<string>default.icns</string>
			<key>CFBundleTypeName</key>
			<string>Folder</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>TEXT</string>
				<string>utxt</string>
				<string>TUTX</string>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.folder</string>
			</array>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>Electron</string>
	<key>CFBundleHelpBookFolder</key>
	<string>VS Code HelpBook</string>
	<key>CFBundleHelpBookName</key>
	<string>VS Code HelpBook</string>
	<key>CFBundleIconFile</key>
	<string>Code.icns</string>
	<key>CFBundleIdentifier</key>
	<string>com.microsoft.VSCode</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Code</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.101.1</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleURLName</key>
			<string>Visual Studio Code</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>vscode</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1.101.1</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTSDKBuild</key>
	<string>24B75</string>
	<key>DTSDKName</key>
	<string>macosx15.1</string>
	<key>DTXcode</key>
	<string>1610</string>
	<key>DTXcodeBuild</key>
	<string>16B40</string>
	<key>ElectronAsarIntegrity</key>
	<dict>
		<key>Resources/default_app.asar</key>
		<dict>
			<key>algorithm</key>
			<string>SHA256</string>
			<key>hash</key>
			<string>4b21efee134447fbbe75e821dc00db88b1c246400fab246d25d46df96179ee99</string>
		</dict>
	</dict>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.developer-tools</string>
	<key>LSEnvironment</key>
	<dict>
		<key>MallocNanoZone</key>
		<string>0</string>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>11.0</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleEventsUsageDescription</key>
	<string>An application in Visual Studio Code wants to use AppleScript.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>This app needs access to Bluetooth</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>This app needs access to Bluetooth</string>
	<key>NSCameraUsageDescription</key>
	<string>An application in Visual Studio Code wants to use the Camera.</string>
	<key>NSHighResolutionCapable</key>
	<true/>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright (C) 2024 Microsoft. All rights reserved</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>An application in Visual Studio Code wants to use the Microphone.</string>
	<key>NSPrefersDisplaySafeAreaCompatibilityMode</key>
	<false/>
	<key>NSPrincipalClass</key>
	<string>AtomApplication</string>
	<key>NSQuitAlwaysKeepsWindows</key>
	<false/>
	<key>NSRequiresAquaSystemAppearance</key>
	<false/>
	<key>NSSupportsAutomaticGraphicsSwitching</key>
	<true/>
</dict>
</plist>
