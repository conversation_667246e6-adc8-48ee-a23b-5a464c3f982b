# References View

This extension shows reference search results as separate view, just like search results. It complements the peek view presentation that is also built into VS Code. The following features are available:

- List All References via the Command Palette, the Context Menu, or via <kbd>Alt+Shift+F12</kbd>
- View references in a dedicated tree view that sits in the sidebar
- Navigate through search results via <kbd>F4</kbd> and <kbd>Shift+F4</kbd>
- Remove references from the list via inline commands

![](https://raw.githubusercontent.com/microsoft/vscode-references-view/master/media/demo.png)

**Note** that this extension is bundled with Visual Studio Code version 1.29 and later - it doesn't need to be installed anymore.

## Requirements

This extension is just an alternative UI for reference search and extensions implementing reference search must still be installed.

## Issues

This extension ships with Visual Studio Code and uses its issue tracker. Please file issue here: https://github.com/Microsoft/vscode/issues

# Contributing

This project welcomes contributions and suggestions. Most contributions require you to agree to a
Contributor License Agreement (CLA) declaring that you have the right to, and actually do, grant us
the rights to use your contribution. For details, visit https://cla.microsoft.com.

When you submit a pull request, a CLA-bot will automatically determine whether you need to provide
a CLA and decorate the PR appropriately (e.g., label, comment). Simply follow the instructions
provided by the bot. You will only need to do this once across all repos using our CLA.

This project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/).
For more information see the [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or
contact [<EMAIL>](mailto:<EMAIL>) with any additional questions or comments.
