{"displayName": "Media Preview", "description": "Provides VS Code's built-in previews for images, audio, and video", "customEditor.audioPreview.displayName": "Audio Preview", "customEditor.imagePreview.displayName": "Image Preview", "customEditor.videoPreview.displayName": "Video Preview", "videoPreviewerAutoPlay": "Start playing videos on mute automatically.", "videoPreviewerLoop": "Loop videos over again automatically.", "command.zoomIn": "Zoom in", "command.zoomOut": "Zoom out", "command.copyImage": "Copy", "command.reopenAsPreview": "Reopen as image preview", "command.reopenAsText": "Reopen as source text"}