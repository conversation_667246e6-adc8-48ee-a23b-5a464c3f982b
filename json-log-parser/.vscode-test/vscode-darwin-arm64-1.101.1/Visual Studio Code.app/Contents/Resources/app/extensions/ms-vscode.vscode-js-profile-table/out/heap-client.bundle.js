(()=>{var e={273:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,".pORror3dhFo_jlOpYDCf{align-items:center;background:var(--vscode-input-background);border:1px solid transparent;border-radius:2px;display:flex;flex-grow:1}.pORror3dhFo_jlOpYDCf:focus-within{border-color:var(--vscode-focusBorder)}.pORror3dhFo_jlOpYDCf>input{background:none;border:0;color:var(--vscode-input-foreground);flex-grow:1;font-family:var(--vscode-font-family);font-size:var(--vscode-editor-font-size);font-weight:var(--vscode-editor-font-weight);padding:3px 4px}.pORror3dhFo_jlOpYDCf>input::-moz-placeholder{color:var(--vscode-input-placeholderForeground)}.pORror3dhFo_jlOpYDCf>input::placeholder{color:var(--vscode-input-placeholderForeground)}.pORror3dhFo_jlOpYDCf>input:focus{outline:0}.pORror3dhFo_jlOpYDCf>button{align-self:stretch;flex-shrink:0;margin-bottom:2px;margin-top:2px;padding-bottom:1px;padding-top:1px}.TxnreUUAKeaIj08v1D74{outline:1px solid var(--vscode-inputValidation-errorBorder)!important}",""]),a.locals={wrapper:"pORror3dhFo_jlOpYDCf",error:"TxnreUUAKeaIj08v1D74"};const l=a},838:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,"._10qE_dwQvcTIkxXSevC{align-items:center;background:var(--vscode-editorWidget-background);box-shadow:0 0 8px 2px var(--vscode-widget-shadow);box-sizing:border-box;display:flex;height:33px;padding:4px;position:relative}",""]),a.locals={f:"_10qE_dwQvcTIkxXSevC"};const l=a},646:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,".xmE80NYmNXD_PVlCFkpw{background:var(--vscode-inputValidation-errorBackground);border:1px solid var(--vscode-inputValidation-errorBorder);padding:4px;position:absolute;top:calc(100% - 4px);z-index:1}",""]),a.locals={error:"xmE80NYmNXD_PVlCFkpw"};const l=a},94:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,".kMe1rh3sGlRge1xNo2FI{align-items:center;background:none;border:1px solid transparent;border-radius:3px;color:var(--vscode-editorWidget-foreground);cursor:pointer;display:flex;margin-left:2px;margin-right:2px;outline:0!important;padding:1px}.kMe1rh3sGlRge1xNo2FI+.kMe1rh3sGlRge1xNo2FI{margin-left:0}.kMe1rh3sGlRge1xNo2FI:hover{background-color:var(--vscode-inputOption-hoverBackground)}.kMe1rh3sGlRge1xNo2FI:focus{border-color:var(--vscode-focusBorder)}.kMe1rh3sGlRge1xNo2FI[aria-checked=true]{background:var(--vscode-inputOption-activeBackground)!important;border:1px solid var(--vscode-inputOption-activeBorder);color:var(--vscode-inputOption-activeForeground)}.kMe1rh3sGlRge1xNo2FI>svg{height:16px;width:16px}",""]),a.locals={button:"kMe1rh3sGlRge1xNo2FI"};const l=a},71:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,"*{margin:0;padding:0}.f0Jk8dUahhi2qYS9k4oW{display:flex;flex-direction:column;height:100vh}.zHL1SBvvBysxPwQUMdES{flex-shrink:0;padding-bottom:10px}.Cekf7yByuJxmXr1F30ko{display:flex;flex-basis:0px;flex-direction:column;flex-grow:1;overflow:hidden;position:relative}",""]),a.locals={wrapper:"f0Jk8dUahhi2qYS9k4oW",filter:"zHL1SBvvBysxPwQUMdES",rows:"Cekf7yByuJxmXr1F30ko"};const l=a},13:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,"*{margin:0;padding:0}.s60FX4Gsndji9tYAIKUs{display:flex;flex-direction:column;height:100vh}.b9nxfCTXGDNxo2IiwNIH{flex-shrink:0;padding-bottom:10px}.cBcdOYmT9_99aVdm9PZF{display:flex;flex-basis:0px;flex-direction:column;flex-grow:1;overflow:hidden;position:relative}",""]),a.locals={wrapper:"s60FX4Gsndji9tYAIKUs",filter:"b9nxfCTXGDNxo2IiwNIH",rows:"cBcdOYmT9_99aVdm9PZF"};const l=a},925:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,".oKKFacfbvSqqq_aC8AE3{flex-grow:1;font-family:var(--vscode-editor-font-family);overflow:auto}.SmGXcbNWaLnOSaUk6_wl{cursor:default;display:flex;height:23px;-webkit-user-select:none;-moz-user-select:none;user-select:none}.SmGXcbNWaLnOSaUk6_wl:focus{background:var(--vscode-list-focusBackground);color:var(--vscode-list-focusForeground);outline:0}.SmGXcbNWaLnOSaUk6_wl>div{margin:2px 4px}.MujOaFcerHdWdwd6a6Up{cursor:pointer;margin-left:244px!important}.QgpWiq7tKfenPd7TZWkz,.enzbXVI4PCTd9u4rZnht.XyAbSpz9jylz8ZS39BwX{text-align:right;width:110px}.enzbXVI4PCTd9u4rZnht.XyAbSpz9jylz8ZS39BwX{cursor:pointer}.enzbXVI4PCTd9u4rZnht svg{display:inline-block;height:1em;margin-right:.25em}.QgpWiq7tKfenPd7TZWkz{color:var(--vscode-terminal-ansiYellow);flex-shrink:0;z-index:0}.QgpWiq7tKfenPd7TZWkz,.QgpWiq7tKfenPd7TZWkz>span{position:relative}.ly1Zy3MRQIzbgri0u5Nx{align-items:center;color:var(--vscode-terminal-foreground);display:flex;flex-grow:1;overflow:hidden;padding-left:10px}.ly1Zy3MRQIzbgri0u5Nx.eyDJMWoZxGo2OH8Utijk{opacity:.5}.ly1Zy3MRQIzbgri0u5Nx a{color:var(--vscode-terminal-foreground);cursor:pointer;text-decoration:none}.ly1Zy3MRQIzbgri0u5Nx a:focus,.ly1Zy3MRQIzbgri0u5Nx a:hover{text-decoration:underline}.ly1Zy3MRQIzbgri0u5Nx a:focus{outline:1px solid var(--vscode-focusBorder)}.e_Y8B9frar9b2mXzvehf,.NFJunN7lzsRC155ophiH{overflow:hidden;white-space:nowrap}.e_Y8B9frar9b2mXzvehf,.NFJunN7lzsRC155ophiH{text-overflow:ellipsis}.NFJunN7lzsRC155ophiH{flex-shrink:0;max-width:calc(100% - 20px)}.e_Y8B9frar9b2mXzvehf{direction:rtl;flex-grow:1;flex-shrink:1;font-family:var(--vscode-font-family);font-size:.8em;margin-left:2em;opacity:.8}.fvv0GAqFPWwrnW2jN8rt{background:none;border:0;flex-shrink:0;opacity:.7;outline:0}.fvv0GAqFPWwrnW2jN8rt,.fvv0GAqFPWwrnW2jN8rt svg{cursor:pointer;width:1em}.SmGXcbNWaLnOSaUk6_wl:hover .fvv0GAqFPWwrnW2jN8rt{opacity:1}.IWaoWposwXCYN2K4Z1vv{background:hsla(0,0%,100%,.1);border-bottom:2px solid hsla(0,0%,100%,.2);bottom:0;left:0;position:absolute;right:0;top:0;transform-origin:100%;z-index:-1}.vscode-light .IWaoWposwXCYN2K4Z1vv{background:rgba(0,0,0,.2)}.vscode-high-contrast .IWaoWposwXCYN2K4Z1vv{background:#fff}",""]),a.locals={rows:"oKKFacfbvSqqq_aC8AE3",row:"SmGXcbNWaLnOSaUk6_wl",footer:"MujOaFcerHdWdwd6a6Up",duration:"QgpWiq7tKfenPd7TZWkz",heading:"enzbXVI4PCTd9u4rZnht",timing:"XyAbSpz9jylz8ZS39BwX",location:"ly1Zy3MRQIzbgri0u5Nx",virtual:"eyDJMWoZxGo2OH8Utijk",file:"e_Y8B9frar9b2mXzvehf",fn:"NFJunN7lzsRC155ophiH",expander:"fvv0GAqFPWwrnW2jN8rt",impactBar:"IWaoWposwXCYN2K4Z1vv"};const l=a},806:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(r)for(var l=0;l<this.length;l++){var s=this[l][0];null!=s&&(a[s]=!0)}for(var c=0;c<e.length;c++){var u=[].concat(e[c]);r&&a[u[0]]||(void 0!==i&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=i),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),o&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=o):u[4]="".concat(o)),t.push(u))}},t}},173:e=>{"use strict";e.exports=function(e){return e[1]}},604:e=>{"use strict";var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var i={},a=[],l=0;l<e.length;l++){var s=e[l],c=r.base?s[0]+r.base:s[0],u=i[c]||0,d="".concat(c," ").concat(u);i[c]=u+1;var _=n(d),p={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==_)t[_].references++,t[_].updater(p);else{var f=o(p,r);r.byIndex=l,t.splice(l,0,{identifier:d,updater:f,references:1})}a.push(d)}return a}function o(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,o){var i=r(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var l=n(i[a]);t[l].references--}for(var s=r(e,o),c=0;c<i.length;c++){var u=n(i[c]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}i=s}}},863:e=>{"use strict";var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},896:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},124:(e,t,n)=>{"use strict";e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},101:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,o&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var i=n.sourceMap;i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},917:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},113:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M8.85352 11.7021H7.85449L7.03809 9.54297H3.77246L3.00439 11.7021H2L4.9541 4H5.88867L8.85352 11.7021ZM6.74268 8.73193L5.53418 5.4502C5.49479 5.34277 5.4554 5.1709 5.41602 4.93457H5.39453C5.35872 5.15299 5.31755 5.32487 5.271 5.4502L4.07324 8.73193H6.74268Z"></path><path d="M13.756 11.7021H12.8752V10.8428H12.8537C12.4706 11.5016 11.9066 11.8311 11.1618 11.8311C10.6139 11.8311 10.1843 11.686 9.87273 11.396C9.56479 11.106 9.41082 10.721 9.41082 10.2412C9.41082 9.21354 10.016 8.61556 11.2262 8.44727L12.8752 8.21631C12.8752 7.28174 12.4974 6.81445 11.7419 6.81445C11.0794 6.81445 10.4815 7.04004 9.94793 7.49121V6.58887C10.4886 6.24512 11.1117 6.07324 11.8171 6.07324C13.1097 6.07324 13.756 6.75716 13.756 8.125V11.7021ZM12.8752 8.91992L11.5485 9.10254C11.1403 9.15983 10.8324 9.26188 10.6247 9.40869C10.417 9.55192 10.3132 9.80794 10.3132 10.1768C10.3132 10.4453 10.4081 10.6655 10.5978 10.8374C10.7912 11.0057 11.0472 11.0898 11.3659 11.0898C11.8027 11.0898 12.1626 10.9377 12.4455 10.6333C12.7319 10.3254 12.8752 9.93685 12.8752 9.46777V8.91992Z"></path></svg>'},84:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.976 10.072l4.357-4.357.62.618L8.284 11h-.618L3 6.333l.619-.618 4.357 4.357z"></path></svg>'},974:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.072 8.024L5.715 3.667l.618-.62L11 7.716v.618L6.333 13l-.618-.619 4.357-4.357z"></path></svg>'},423:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M9.13 15l-.53-.77a1.85 1.85 0 0 0-.28-2.54 3.51 3.51 0 0 1-1.19-2c-1.56 2.23-.75 3.46 0 4.55l-.55.76A4.4 4.4 0 0 1 3 10.46S2.79 8.3 5.28 6.19c0 0 2.82-2.61 1.84-4.54L7.83 1a6.57 6.57 0 0 1 2.61 6.94 2.57 2.57 0 0 0 .56-.81l.87-.07c.07.12 1.84 2.93.89 5.3A4.72 4.72 0 0 1 9.13 15zm-2-6.95l.87.39a3 3 0 0 0 .92 2.48 2.64 2.64 0 0 1 1 2.8A3.241 3.241 0 0 0 11.8 12a4.87 4.87 0 0 0-.41-3.63 1.85 1.85 0 0 1-1.84.86l-.35-.68a5.31 5.31 0 0 0-.89-5.8C8.17 4.87 6 6.83 5.93 6.94 3.86 8.7 4 10.33 4 10.4a3.47 3.47 0 0 0 1.59 3.14C5 12.14 5 10.46 7.16 8.05h-.03z"></path></svg>'},143:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.012 2h.976v3.113l2.56-1.557.486.885L11.47 6l2.564 1.559-.485.885-2.561-1.557V10h-.976V6.887l-2.56 1.557-.486-.885L9.53 6 6.966 4.441l.485-.885 2.561 1.557V2zM2 10h4v4H2v-4z"></path></svg>'}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={id:r,exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0,(()=>{"use strict";var e,t,r,o,i,a,l,s,c={},u=[],d=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,_=Array.isArray;function p(e,t){for(var n in t)e[n]=t[n];return e}function f(e){var t=e.parentNode;t&&t.removeChild(e)}function h(t,n,r){var o,i,a,l={};for(a in n)"key"==a?o=n[a]:"ref"==a?i=n[a]:l[a]=n[a];if(arguments.length>2&&(l.children=arguments.length>3?e.call(arguments,2):r),"function"==typeof t&&null!=t.defaultProps)for(a in t.defaultProps)void 0===l[a]&&(l[a]=t.defaultProps[a]);return v(t,l,o,i,null)}function v(e,n,o,i,a){var l={type:e,props:n,key:o,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==a?++r:a,__i:-1};return null==a&&null!=t.vnode&&t.vnode(l),l}function m(e){return e.children}function g(e,t){this.props=e,this.context=t}function y(e,t){if(null==t)return e.__?y(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?y(e):null}function b(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return b(e)}}function x(e){(!e.__d&&(e.__d=!0)&&o.push(e)&&!w.__r++||i!==t.debounceRendering)&&((i=t.debounceRendering)||a)(w)}function w(){var e,t,n,r,i,a,s,c,u;for(o.sort(l);e=o.shift();)e.__d&&(t=o.length,r=void 0,i=void 0,a=void 0,c=(s=(n=e).__v).__e,(u=n.__P)&&(r=[],i=[],(a=p({},s)).__v=s.__v+1,M(u,a,s,n.__n,void 0!==u.ownerSVGElement,null!=s.__h?[c]:null,r,null==c?y(s):c,s.__h,i),a.__.__k[a.__i]=a,P(r,a,i),a.__e!=c&&b(a)),o.length>t&&o.sort(l));w.__r=0}function k(e,t,n,r,o,i,a,l,s,d,p){var f,h,g,b,x,w,k,A,F,z=0,T=r&&r.__k||u,P=T.length,I=P,L=t.length;for(n.__k=[],f=0;f<L;f++)null!=(b=n.__k[f]=null==(b=t[f])||"boolean"==typeof b||"function"==typeof b?null:b.constructor==String||"number"==typeof b||"bigint"==typeof b?v(null,b,null,null,b):_(b)?v(m,{children:b},null,null,null):b.__b>0?v(b.type,b.props,b.key,b.ref?b.ref:null,b.__v):b)?(b.__=n,b.__b=n.__b+1,b.__i=f,-1===(A=C(b,T,k=f+z,I))?g=c:(g=T[A]||c,T[A]=void 0,I--),M(e,b,g,o,i,a,l,s,d,p),x=b.__e,(h=b.ref)&&g.ref!=h&&(g.ref&&E(g.ref,null,b),p.push(h,b.__c||x,b)),null==w&&null!=x&&(w=x),(F=g===c||null===g.__v)?-1==A&&z--:A!==k&&(A===k+1?z++:A>k?I>L-k?z+=A-k:z--:z=A<k&&A==k-1?A-k:0),k=f+z,"function"==typeof b.type?(A!==k||g.__k===b.__k?s=S(b,s,e):void 0!==b.__d?s=b.__d:x&&(s=x.nextSibling),b.__d=void 0):x&&(s=A!==k||F?N(e,x,s):x.nextSibling),"function"==typeof n.type&&(n.__d=s)):(g=T[f])&&null==g.key&&g.__e&&(g.__e==s&&(s=y(g),"function"==typeof n.type&&(n.__d=s)),H(g,g,!1),T[f]=null);for(n.__e=w,f=P;f--;)null!=T[f]&&("function"==typeof n.type&&null!=T[f].__e&&T[f].__e==s&&(n.__d=T[f].__e.nextSibling),H(T[f],T[f]))}function S(e,t,n){for(var r,o=e.__k,i=0;o&&i<o.length;i++)(r=o[i])&&(r.__=e,t="function"==typeof r.type?S(r,t,n):N(n,r.__e,t));return t}function N(e,t,n){return t!=n&&e.insertBefore(t,n||null),t.nextSibling}function C(e,t,n,r){var o=e.key,i=e.type,a=n-1,l=n+1,s=t[n];if(null===s||s&&o==s.key&&i===s.type)return n;if(r>(null!=s?1:0))for(;a>=0||l<t.length;){if(a>=0){if((s=t[a])&&o==s.key&&i===s.type)return a;a--}if(l<t.length){if((s=t[l])&&o==s.key&&i===s.type)return l;l++}}return-1}function A(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||d.test(t)?n:n+"px"}function F(e,t,n,r,o){var i;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||A(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||A(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/,"$1")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r?n.u=r.u:(n.u=Date.now(),e.addEventListener(t,i?T:z,i)):e.removeEventListener(t,i?T:z,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&"rowSpan"!==t&&"colSpan"!==t&&"role"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,n))}}function z(e){var n=this.l[e.type+!1];if(e.t){if(e.t<=n.u)return}else e.t=Date.now();return n(t.event?t.event(e):e)}function T(e){return this.l[e.type+!0](t.event?t.event(e):e)}function M(e,n,r,o,i,a,l,s,c,u){var d,f,h,v,y,b,x,w,S,N,C,A,F,z,T,M=n.type;if(void 0!==n.constructor)return null;null!=r.__h&&(c=r.__h,s=n.__e=r.__e,n.__h=null,a=[s]),(d=t.__b)&&d(n);e:if("function"==typeof M)try{if(w=n.props,S=(d=M.contextType)&&o[d.__c],N=d?S?S.props.value:d.__:o,r.__c?x=(f=n.__c=r.__c).__=f.__E:("prototype"in M&&M.prototype.render?n.__c=f=new M(w,N):(n.__c=f=new g(w,N),f.constructor=M,f.render=L),S&&S.sub(f),f.props=w,f.state||(f.state={}),f.context=N,f.__n=o,h=f.__d=!0,f.__h=[],f._sb=[]),null==f.__s&&(f.__s=f.state),null!=M.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=p({},f.__s)),p(f.__s,M.getDerivedStateFromProps(w,f.__s))),v=f.props,y=f.state,f.__v=n,h)null==M.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==M.getDerivedStateFromProps&&w!==v&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(w,N),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(w,f.__s,N)||n.__v===r.__v)){for(n.__v!==r.__v&&(f.props=w,f.state=f.__s,f.__d=!1),n.__e=r.__e,n.__k=r.__k,n.__k.forEach((function(e){e&&(e.__=n)})),C=0;C<f._sb.length;C++)f.__h.push(f._sb[C]);f._sb=[],f.__h.length&&l.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(w,f.__s,N),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(v,y,b)}))}if(f.context=N,f.props=w,f.__P=e,f.__e=!1,A=t.__r,F=0,"prototype"in M&&M.prototype.render){for(f.state=f.__s,f.__d=!1,A&&A(n),d=f.render(f.props,f.state,f.context),z=0;z<f._sb.length;z++)f.__h.push(f._sb[z]);f._sb=[]}else do{f.__d=!1,A&&A(n),d=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++F<25);f.state=f.__s,null!=f.getChildContext&&(o=p(p({},o),f.getChildContext())),h||null==f.getSnapshotBeforeUpdate||(b=f.getSnapshotBeforeUpdate(v,y)),k(e,_(T=null!=d&&d.type===m&&null==d.key?d.props.children:d)?T:[T],n,r,o,i,a,l,s,c,u),f.base=n.__e,n.__h=null,f.__h.length&&l.push(f),x&&(f.__E=f.__=null)}catch(e){n.__v=null,c||null!=a?(n.__e=s,n.__h=!!c,a[a.indexOf(s)]=null):(n.__e=r.__e,n.__k=r.__k),t.__e(e,n,r)}else null==a&&n.__v===r.__v?(n.__k=r.__k,n.__e=r.__e):n.__e=I(r.__e,n,r,o,i,a,l,c,u);(d=t.diffed)&&d(n)}function P(e,n,r){n.__d=void 0;for(var o=0;o<r.length;o++)E(r[o],r[++o],r[++o]);t.__c&&t.__c(n,e),e.some((function(n){try{e=n.__h,n.__h=[],e.some((function(e){e.call(n)}))}catch(e){t.__e(e,n.__v)}}))}function I(t,n,r,o,i,a,l,s,u){var d,p,h,v=r.props,m=n.props,g=n.type,b=0;if("svg"===g&&(i=!0),null!=a)for(;b<a.length;b++)if((d=a[b])&&"setAttribute"in d==!!g&&(g?d.localName===g:3===d.nodeType)){t=d,a[b]=null;break}if(null==t){if(null===g)return document.createTextNode(m);t=i?document.createElementNS("http://www.w3.org/2000/svg",g):document.createElement(g,m.is&&m),a=null,s=!1}if(null===g)v===m||s&&t.data===m||(t.data=m);else{if(a=a&&e.call(t.childNodes),p=(v=r.props||c).dangerouslySetInnerHTML,h=m.dangerouslySetInnerHTML,!s){if(null!=a)for(v={},b=0;b<t.attributes.length;b++)v[t.attributes[b].name]=t.attributes[b].value;(h||p)&&(h&&(p&&h.__html==p.__html||h.__html===t.innerHTML)||(t.innerHTML=h&&h.__html||""))}if(function(e,t,n,r,o){var i;for(i in n)"children"===i||"key"===i||i in t||F(e,i,null,n[i],r);for(i in t)o&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||n[i]===t[i]||F(e,i,t[i],n[i],r)}(t,m,v,i,s),h)n.__k=[];else if(k(t,_(b=n.props.children)?b:[b],n,r,o,i&&"foreignObject"!==g,a,l,a?a[0]:r.__k&&y(r,0),s,u),null!=a)for(b=a.length;b--;)null!=a[b]&&f(a[b]);s||("value"in m&&void 0!==(b=m.value)&&(b!==t.value||"progress"===g&&!b||"option"===g&&b!==v.value)&&F(t,"value",b,v.value,!1),"checked"in m&&void 0!==(b=m.checked)&&b!==t.checked&&F(t,"checked",b,v.checked,!1))}return t}function E(e,n,r){try{"function"==typeof e?e(n):e.current=n}catch(e){t.__e(e,r)}}function H(e,n,r){var o,i;if(t.unmount&&t.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||E(o,null,n)),null!=(o=e.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(e){t.__e(e,n)}o.base=o.__P=null,e.__c=void 0}if(o=e.__k)for(i=0;i<o.length;i++)o[i]&&H(o[i],n,r||"function"!=typeof e.type);r||null==e.__e||f(e.__e),e.__=e.__e=e.__d=void 0}function L(e,t,n){return this.constructor(e,n)}e=u.slice,t={__e:function(e,t,n,r){for(var o,i,a;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),a=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,r||{}),a=o.__d),a)return o.__E=o}catch(t){e=t}throw e}},r=0,g.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=p({},this.state),"function"==typeof e&&(e=e(p({},n),this.props)),e&&p(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),x(this))},g.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),x(this))},g.prototype.render=m,o=[],a="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,l=function(e,t){return e.__v.__b-t.__v.__b},w.__r=0,s=0;var W,O,R,U,j=0,D=[],B=[],q=t.__b,X=t.__r,Z=t.diffed,V=t.__c,$=t.unmount;function G(e,n){t.__h&&t.__h(O,e,j||n),j=0;var r=O.__H||(O.__H={__:[],__h:[]});return e>=r.__.length&&r.__.push({__V:B}),r.__[e]}function Y(e){return j=1,function(e,t,n){var r=G(W++,2);if(r.t=e,!r.__c&&(r.__=[ce(void 0,t),function(e){var t=r.__N?r.__N[0]:r.__[0],n=r.t(t,e);t!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=O,!O.u)){var o=function(e,t,n){if(!r.__c.__H)return!0;var o=r.__c.__H.__.filter((function(e){return e.__c}));if(o.every((function(e){return!e.__N})))return!i||i.call(this,e,t,n);var a=!1;return o.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(a=!0)}})),!(!a&&r.__c.props===e)&&(!i||i.call(this,e,t,n))};O.u=!0;var i=O.shouldComponentUpdate,a=O.componentWillUpdate;O.componentWillUpdate=function(e,t,n){if(this.__e){var r=i;i=void 0,o(e,t,n),i=r}a&&a.call(this,e,t,n)},O.shouldComponentUpdate=o}return r.__N||r.__}(ce,e)}function K(e,n){var r=G(W++,3);!t.__s&&se(r.__H,n)&&(r.__=e,r.i=n,O.__H.__h.push(r))}function Q(e,n){var r=G(W++,4);!t.__s&&se(r.__H,n)&&(r.__=e,r.i=n,O.__h.push(r))}function J(e){return j=5,ee((function(){return{current:e}}),[])}function ee(e,t){var n=G(W++,7);return se(n.__H,t)?(n.__V=e(),n.i=t,n.__h=e,n.__V):n.__}function te(e,t){return j=8,ee((function(){return e}),t)}function ne(e){var t=O.context[e.__c],n=G(W++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(O)),t.props.value):e.__}function re(){for(var e;e=D.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(ae),e.__H.__h.forEach(le),e.__H.__h=[]}catch(n){e.__H.__h=[],t.__e(n,e.__v)}}t.__b=function(e){O=null,q&&q(e)},t.__r=function(e){X&&X(e),W=0;var t=(O=e.__c).__H;t&&(R===O?(t.__h=[],O.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=B,e.__N=e.i=void 0}))):(t.__h.forEach(ae),t.__h.forEach(le),t.__h=[],W=0)),R=O},t.diffed=function(e){Z&&Z(e);var n=e.__c;n&&n.__H&&(n.__H.__h.length&&(1!==D.push(n)&&U===t.requestAnimationFrame||((U=t.requestAnimationFrame)||ie)(re)),n.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==B&&(e.__=e.__V),e.i=void 0,e.__V=B}))),R=O=null},t.__c=function(e,n){n.some((function(e){try{e.__h.forEach(ae),e.__h=e.__h.filter((function(e){return!e.__||le(e)}))}catch(r){n.some((function(e){e.__h&&(e.__h=[])})),n=[],t.__e(r,e.__v)}})),V&&V(e,n)},t.unmount=function(e){$&&$(e);var n,r=e.__c;r&&r.__H&&(r.__H.__.forEach((function(e){try{ae(e)}catch(e){n=e}})),r.__H=void 0,n&&t.__e(n,r.__v))};var oe="function"==typeof requestAnimationFrame;function ie(e){var t,n=function(){clearTimeout(r),oe&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);oe&&(t=requestAnimationFrame(n))}function ae(e){var t=O,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),O=t}function le(e){var t=O;e.__c=e.__(),O=t}function se(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function ce(e,t){return"function"==typeof t?t(e):t}var ue,de=n(113),_e=n(143);!function(e){e[e.String=0]="String",e[e.Number=1]="Number"}(ue||(ue={}));const pe={[ue.Number]:{":":e=>t=>t===Number(e),"=":e=>t=>t===Number(e),">":e=>t=>t>Number(e),"<":e=>t=>t<Number(e),"<=":e=>t=>t<=Number(e),">=":e=>t=>t>=Number(e),"<>":e=>t=>t!==Number(e),"!=":e=>t=>t!==Number(e)},[ue.String]:{":":e=>t=>t===e,"=":e=>t=>t===e,"!=":e=>t=>t!==e,"<>":e=>t=>t!==e,"~=":e=>{const t=/^\/(.+)\/([a-z])*$/.exec(e),n=t?new RegExp(t[1],t[2]):new RegExp(e);return e=>(n.lastIndex=0,n.test(e))}}};class fe extends Error{constructor(e,t){super(e),this.index=t}}const he=new Set(Object.values(pe).map((e=>Object.keys(e))).reduce(((e,t)=>[...e,...t]),[]));class ve{get eof(){return this.data?.length===this.length||Array.isArray(this._read)}get loaded(){return this.data||[]}static fromArray(e,t){return this.fromTopLevelArray(e,(e=>ve.fromArray(t(e),t)))}static fromTopLevelArray(e,t){const n=new ve(e.length,(()=>Promise.resolve(e)),t);return n.data=e,n}static fromProvider(e,t,n){return ve.fromProvider(e,t,n)}constructor(e,t,n){this.length=e,this._getChildren=n,this.asyncLoads=[],this.children=new Map,Array.isArray(t)||t instanceof Array?this.data=t:this._read=t}setSort(e){e!==this.sortFn&&(this.sortFn=e,this.eof?this.data&&this.data.sort(e):(this.data=void 0,this.asyncLoads=[]))}getChildren(e){let t=this.children.get(e);return t||(t=this._getChildren(e),this.children.set(e,t)),t.setSort(this.sortFn),t}didReadUpTo(e){if(this.eof||!this._read)return!0;const t=this.asyncLoads[this.asyncLoads.length-1];return!!(t&&t.upTo>=e)}async read(e){if(!this._read)return Promise.resolve(this.loaded);const t=this.asyncLoads[this.asyncLoads.length-1]||{upTo:0,p:Promise.resolve()};if(t.upTo>=e)return t.p;const n=t.p.then((async()=>{const n=await this._read(t.upTo,e,this.sortFn);return this.data?.length?this.data=this.data.concat(n):this.data=n,this.data}));return this.asyncLoads.push({upTo:e,p:n}),n}}const me=(e,t,n,r)=>{let o=!1;t(n)&&(r.selected.add(n),r.selectedAndParents.add(n),o=!0);const i=e.getChildren(n);for(const e of i.loaded)me(i,t,e,r)&&(r.selectedAndParents.add(n),o=!0);return o};var ge=n(604),ye=n.n(ge),be=n(101),xe=n.n(be),we=n(863),ke=n.n(we),Se=n(124),Ne=n.n(Se),Ce=n(896),Ae=n.n(Ce),Fe=n(917),ze=n.n(Fe),Te=n(273),Me={};Me.styleTagTransform=ze(),Me.setAttributes=Ne(),Me.insert=ke().bind(null,"head"),Me.domAPI=xe(),Me.insertStyleElement=Ae(),ye()(Te.A,Me);const Pe=Te.A&&Te.A.locals?Te.A.locals:void 0,Ie=(...e)=>e.filter(Boolean).join(" "),Ee=({value:e,hasError:t,min:n,type:r,onChange:o,placeholder:i="Filter for function",foot:a})=>{const l=te((e=>{o(e.target.value)}),[o]);return h("div",{className:Pe.wrapper},h("input",{className:Ie(t&&Pe.error),type:r,min:n,value:e,placeholder:i,onPaste:l,onKeyUp:l}),a)};var He=n(838),Le={};Le.styleTagTransform=ze(),Le.setAttributes=Ne(),Le.insert=ke().bind(null,"head"),Le.domAPI=xe(),Le.insertStyleElement=Ae(),ye()(He.A,Le);const We=He.A&&He.A.locals?He.A.locals:void 0,Oe=({children:e})=>h("div",{className:We.f},e);var Re=n(646),Ue={};Ue.styleTagTransform=ze(),Ue.setAttributes=Ne(),Ue.insert=ke().bind(null,"head"),Ue.domAPI=xe(),Ue.insertStyleElement=Ae(),ye()(Re.A,Ue);const je=Re.A&&Re.A.locals?Re.A.locals:void 0;var De=n(94),Be={};Be.styleTagTransform=ze(),Be.setAttributes=Ne(),Be.insert=ke().bind(null,"head"),Be.domAPI=xe(),Be.insertStyleElement=Ae(),ye()(De.A,Be);const qe=De.A&&De.A.locals?De.A.locals:void 0,Xe=({icon:e,label:t,checked:n,onChange:r,onClick:o})=>{const i=te((()=>{o?.(),r?.(!n)}),[n,o,r]);return h("button",{className:qe.button,type:"button",role:"switch",alt:t,title:t,"aria-label":t,"aria-checked":n?"true":"false",dangerouslySetInnerHTML:{__html:e},onClick:i})},Ze=function(e,t){var n={__c:t="__cC"+s++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some((function(e){e.__e=!0,x(e)}))},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}(acquireVsCodeApi()),Ve=(e,t)=>{const n=ne(Ze),[r,o]=Y(n.getState()?.[e]??t);return((t,o)=>{const i=J(!0);K((()=>{i.current?i.current=!1:n.setState({...n.getState(),[e]:r})}),o)})(0,[r]),[r,o]},$e=()=>({placeholder:e,data:t,onChange:n,foot:r})=>{const[o,i]=Y(!1),[a,l]=Y(!1),[s,c]=Ve("filterText",""),[u,d]=Y(void 0);return K((()=>{try{n((e=>{const t=((e,t,n=pe)=>{const r=[],o=[];for(let i=0;i<e.length;i++){const a=e[i];switch(a.token){case 1:const l=t.datasource.properties[a.text];if(!l){const e=Object.keys(t.datasource.properties).join(", ");throw new fe(`Unknown column @${a.text}, have: ${e}`,a.start)}const s=e[++i];if(2!==s?.token)throw new fe(`Missing operator for column @${a.text}`,a.start);if(!n[l.type][s.text])throw new fe(`Unknown operator for @${a.text}, have: ${Object.keys(n[l.type]).join(", ")}`,s.start);const c=e[++i];if(3!==c?.token)throw new fe(`Missing operand for column @${c.text}`,a.start);const u=n[l.type][s.text](c.text);r.push((e=>u(l.accessor(e))));break;case 0:o.push(a.text.trim());break;default:throw new Error(`Illegal token ${a.token}`)}}const i=o.join(" ").trim();if(i){const e=`/${t.regex?i:(a=i,a.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&"))}/`+(t.caseSensitive?"":"i"),o=n[ue.String]["~="](e);r.push((e=>o(t.datasource.genericMatchStr(e))))}var a;return e=>{for(const t of r)if(!t(e))return!1;return!0}})((e=>{const t=[];let n=0;const r=(t,r)=>{let o="";const i=n;for(;n<e.length;){const t=e[n];if("\\"!==t){if(!r(t,n))break;o+=t,n++}else o+=e[++n],n++}return{token:t,text:o,start:i,length:n-i}};let o=0;for("@"===e[0]&&(o=1,n++);n<e.length;){const i=e[n];switch(o){case 0:const a=e.indexOf(" @",n);-1===a?t.push(r(0,(()=>!0))):(t.push(r(0,((e,t)=>t<=a))),n++,o=1);break;case 1:t.push(r(1,(e=>e>="A"&&e<="z"))),o=2;break;case 2:t.push(r(2,(e=>he.has(e)))),o=3;break;case 3:const l='"'!==i&&"'"!==i;l||n++,t.push(r(3,(e=>l?" "!==e:e!==i))),o=0,l||n++;break;default:throw new Error(`Illegal state ${o}`)}}return t})(e.input),e),n={selected:new Set,selectedAndParents:new Set,all:!e.input.trim()};for(const r of e.datasource.data.loaded)me(e.datasource.data,t,r,n);return n})({input:s,regex:o,caseSensitive:a,datasource:t})),d(void 0)}catch(e){d(e.message)}}),[o,a,s,t]),h(Oe,null,h(Ee,{value:s,placeholder:e,onChange:c,hasError:!!u,foot:h(m,null,h(Xe,{icon:de,label:"Match Case",checked:a,onChange:l}),h(Xe,{icon:_e,label:"Use Regular Expression",checked:o,onChange:i}))}),u&&h("div",{className:je.error},u),r)};var Ge=n(71),Ye={};Ye.styleTagTransform=ze(),Ye.setAttributes=Ne(),Ye.insert=ke().bind(null,"head"),Ye.domAPI=xe(),Ye.insertStyleElement=Ae(),ye()(Ge.A,Ye);const Ke=Ge.A&&Ge.A.locals?Ge.A.locals:void 0;class Qe{static root(){return new Qe({id:-1,selfSize:0,children:[],callFrame:{functionName:"(root)",lineNumber:-1,columnNumber:-1,scriptId:"0",url:""}})}get id(){return this.node.id}get callFrame(){return this.node.callFrame}get src(){return this.node.src}constructor(e,t){var n;this.node=e,this.parent=t,this.children={},this.totalSize=0,this.selfSize=0,this.childrenSize=0,this.category=(void 0,(n=e.callFrame).functionName=n.functionName||"(anonymous)",n.lineNumber<0?0:(n.url.includes("node_modules"),2))}toJSON(){return{category:this.category,children:this.children,childrenSize:this.childrenSize,selfSize:this.selfSize,totalSize:this.totalSize,id:this.id,callFrame:this.callFrame,src:this.src}}}const Je=(e,t)=>{const n=new Qe(e,t);e.children.forEach((e=>{const t=Je(e,n);n.children[t.id]=t,n.childrenSize++})),n.selfSize=e.selfSize,n.totalSize=e.selfSize;for(const e in n.children)n.totalSize+=n.children[e].totalSize;return n};var et=n(13),tt={};tt.styleTagTransform=ze(),tt.setAttributes=Ne(),tt.insert=ke().bind(null,"head"),tt.domAPI=xe(),tt.insertStyleElement=Ae(),ye()(et.A,tt);const nt=et.A&&et.A.locals?et.A.locals:void 0;var rt=n(423);var ot=n(84);const it=({i:e,...t})=>h("span",{dangerouslySetInnerHTML:{__html:e},style:{color:"var(--vscode-icon-foreground)"},...t}),at=e=>{if(e.callFrame.url){if(!e.src?.source.path){let t=`${e.callFrame.url}`;return e.callFrame.lineNumber>=0&&(t+=`:${e.callFrame.lineNumber}`),t}return e.src.relativePath?`${e.src.relativePath}:${e.src.lineNumber}`:`${e.src.source.path}:${e.src.lineNumber}`}},lt=new Intl.NumberFormat(void 0,{maximumFractionDigits:0,minimumFractionDigits:0});Symbol("unset");var st=n(925),ct={};ct.styleTagTransform=ze(),ct.setAttributes=Ne(),ct.insert=ke().bind(null,"head"),ct.domAPI=xe(),ct.insertStyleElement=Ae(),ye()(st.A,ct);const ut=st.A&&st.A.locals?st.A.locals:void 0,dt=({row:e,renderRow:t,style:n})=>h("div",{style:n},ee((()=>t(e)),[e])),_t=(e,t)=>`position:absolute;left:0;right:0;height:${t}px;top:${e*t}px`,pt=(e,t,n)=>{n((n=>{if(n.get(e)===t){const t=new Map(n);return t.delete(e),t}return n}))},ft=({depth:e,position:t,node:n,dataProvider:r,promise:o,onLoadMore:i})=>{const[a,l]=Y(!!o);return r.eof?null:(K((()=>{o?o.finally((()=>l(!1))):l(!1)}),[o]),h("div",{className:ut.row,"data-row-id":`loading-${t}`,tabIndex:0,role:"treeitem","aria-posinset":t,"aria-level":e+1},h("div",{className:ut.footer,style:{paddingLeft:15*e}},a?"Loading...":h(m,null,h("a",{role:"button",onClick:()=>i(n,r)},"Load more rows")))))};var ht=n(974);const vt=e=>{const t=[e.id];for(let n=e.parent;n;n=n.parent)t.push(n.id);return t.join("-")},mt=({impact:e})=>h("div",{className:ut.impactBar,style:{transform:`scaleX(${e})`}}),gt=(e,t)=>t.selfSize-e.selfSize,yt=(e,t)=>t.totalSize-e.totalSize,bt=(()=>{const e=({containerRef:e=J(null),data:t,className:n,renderRow:r,rowHeight:o,overscanCount:i})=>{const[a,l]=Y([]),s=t.length*o,c=te((()=>{const{current:n}=e;if(!n)return;const r=n.scrollTop,a=Math.max(0,Math.floor(r/o)-i),s=Math.min(t.length-1,a+Math.ceil(n.clientHeight/o)+2*i);l(function(e,t){const n=[];for(let r=e;r<t;r++)n.push(r);return n}(a,s+1))}),[t,o,i]);return((e,t,n)=>{K((()=>{if(!t)return;const r=new ResizeObserver((t=>{for(const n of t)e(n)}));return r.observe(t,n),()=>r.disconnect()}),[e,t,n])})(c,e.current),Q((()=>c()),[c]),h("div",{ref:e,className:n,style:{height:"100%",overflow:"auto"},onScroll:c},h("div",{style:{height:s,position:"relative"}},a.map((e=>h(dt,{renderRow:r,row:t[e],style:_t(e,o),key:e})))))};return({data:t,header:n,query:r,sortFn:o,row:i})=>{const a=J(new Map),[l,s]=Y(new Map),c=J(null),[u,d]=Y(void 0),[_,p]=Y(new Set),f=ee((()=>{const e=o?t.loaded.slice().sort(o):t.loaded;for(const n of e)t.setSort(o),a.current.set(n,t);return e}),[t,o]),v=ee((()=>{const e=f.filter((e=>r.selectedAndParents.has(e))).map((e=>({node:e,position:1,depth:0,provider:t})));for(let t=0;t<e.length;t++){const{node:n,depth:o,isFooter:i,entireSubtree:l}=e[t];if(!i&&_.has(n)){const i=a.current.get(n)?.getChildren(n);if(i){for(const e of i.loaded)a.current.set(e,i);const s=[];for(const e of i.loaded)(r.all||r.selectedAndParents.has(e)||l)&&s.push({node:e,position:t+1,depth:o+1,provider:i,entireSubtree:l||r.selected.has(e)});r.all&&s.push({isFooter:!0,node:n,position:t+s.length,depth:o+1,provider:i}),e.splice(t+1,0,...s)}}}return e}),[f,_,o,r,l]),g=te(((e,t)=>{const n=a.current.get(t);let r;switch(e.key){case"Enter":case"Space":p(((e,t)=>{const n=new Set([...e]);return n.has(t)?n.delete(t):n.add(t),n})(_,t)),e.preventDefault();break;case"ArrowDown":r=v[v.findIndex((e=>e.node===t))+1]?.node;break;case"ArrowUp":r=v[v.findIndex((e=>e.node===t))-1]?.node;break;case"ArrowLeft":_.has(t)?p(((e,t)=>{const n=new Set([...e]);return n.delete(t),n})(_,t)):r=t.parent;break;case"ArrowRight":{const e=n?.getChildren(t);e?.length&&!_.has(t)?p(((e,t)=>{const n=new Set([...e,t]);return n.add(t),n})(_,t)):r=v.find((e=>e.node?.parent===t))?.node;break}case"Home":c.current&&(c.current.scrollTop=0),r=v[0]?.node;break;case"End":c.current&&(c.current.scrollTop=c.current.scrollHeight),r=v[v.length-1]?.node;break;case"*":{const e=new Set(_);if(u&&u.parent){const t=u?.parent,n=t&&a.current.get(t);for(const t of n?.getChildren(u).loaded||[])e.add(t);p(e)}break}}r&&(d(r),e.preventDefault())}),[v,_]);K((()=>{s((e=>{let t;for(const n of _){const r=a.current.get(n)?.getChildren(n);if(r&&!r.didReadUpTo(100)){t??=new Map(e);const o=r.read(100).then((()=>pt(n,o,s)));t.set(n,o)}}return t||e}))}),[_,o]),K((()=>c.current?.setAttribute("role","tree")),[c.current]),Q((()=>{const e=c.current;e&&u&&setTimeout((()=>{const t=e.querySelector(`[data-row-id="${(e=>{const t=[e.id];for(let n=e.parent;n;n=n.parent)t.push(n.id);return t.join("-")})(u)}"]`);t?.focus()}))}),[u]);const y=(e,t)=>{p((n=>{const r=new Set(n);return e?r.add(t):r.delete(t),r.size!==n.size?r:n}))},b=te(((e,t)=>{const n=t,r=e;s((e=>{const t=new Map(e),o=n.read(n.loaded.length+100).then((()=>pt(r,o,s)));return t.set(r,o),t}))}),[]),x=te((e=>e.isFooter?h(ft,{node:e.node,depth:e.depth,position:e.position,promise:l.get(e.node),dataProvider:e.provider,onLoadMore:b}):h(i,{onKeyDown:g,node:e.node,depth:e.depth,position:e.position,numChildren:e.provider.getChildren(e.node).length,expanded:_.has(e.node),onExpanded:y,onFocus:d})),[_,p,g]);return h(m,null,n,h(e,{containerRef:c,className:ut.rows,data:v,renderRow:x,rowHeight:25,overscanCount:30}))}})(),xt=({data:e,query:t})=>{const[n,r]=Y((()=>gt));return h(bt,{data:e,sortFn:n,query:t,header:h(kt,{sortFn:n,onChangeSort:r}),row:St})},wt=({node:e,depth:t,numChildren:n,expanded:r,position:o,onKeyDown:i,onFocus:a,onClick:l,onExpanded:s,children:c,rowText:u,locationText:d,virtual:_=!d})=>{const p=te((()=>s(!r,e)),[r,e]),f=te((t=>{i?.(t,e)}),[i,e]),v=te((()=>{a?.(e)}),[a,e]);let m=e;for(;m.parent;)m=m.parent;const g=h("span",{className:ut.expander},n>0?h(it,{i:r?ot:ht}):null);return h("div",{className:ut.row,style:{cursor:n>0?"pointer":"default"},"data-row-id":vt(e),onKeyDown:f,onFocus:v,onClick:p,tabIndex:0,role:"treeitem","aria-posinset":o,"aria-level":t+1,"aria-expanded":r},c,d?h("div",{className:ut.location,style:{marginLeft:15*t}},g," ",h("span",{className:ut.fn,style:{maxWidth:"80%"}},u),h("span",{className:ut.file},h("a",{href:"#",onClick:l},d))):h("div",{className:Ie(ut.location,_&&ut.virtual),style:{marginLeft:15*t}},g," ",h("span",{className:ut.fn},u)))},kt=({sortFn:e,onChangeSort:t})=>h("div",{className:ut.row},h("div",{id:"self-size-header",className:Ie(ut.heading,ut.timing),"aria-sort":e===gt?"descending":void 0,onClick:te((()=>t((()=>e===gt?void 0:gt))),[e])},e===gt&&h(it,{i:ot}),"Self Size"),h("div",{id:"total-size-header",className:Ie(ut.heading,ut.timing),"aria-sort":e===yt?"descending":void 0,onClick:te((()=>t((()=>e===yt?void 0:yt))),[e])},e===yt&&h(it,{i:ot}),"Total Size"),h("div",{className:ut.heading},"File")),St=e=>{const{node:t}=e;let n=e.node;for(;n.parent;)n=n.parent;const r=ne(Ze),o=te((e=>r.postMessage({type:"openDocument",callFrame:t.callFrame,location:t.src,toSide:e.altKey})),[r,t]);return h(wt,{...e,onClick:o,rowText:t.callFrame.functionName,locationText:at(t)},h("div",{className:ut.duration,"aria-labelledby":"self-size-header"},h(mt,{impact:t.selfSize/t.totalSize}),lt.format(t.selfSize/1e3),"kB"),h("div",{className:ut.duration,"aria-labelledby":"total-size-header"},h(mt,{impact:t.totalSize/n.totalSize}),lt.format(t.totalSize/1e3),"kB"))},Nt=(e=>{const t=Qe.root();for(const n of e.head.children){const e=Je(n,t);t.children[e.id]=e,t.childrenSize++}for(const e in t.children)t.totalSize+=t.children[e].totalSize;return t})(MODEL),Ct=Object.values(Nt.children),At=document.createElement("div");At.classList.add(nt.wrapper),document.body.appendChild(At),function(n,r,o){var i,a,l,s;t.__&&t.__(n,r),a=(i="function"==typeof o)?null:o&&o.__k||r.__k,l=[],s=[],M(r,n=(!i&&o||r).__k=h(m,null,[n]),a||c,c,void 0!==r.ownerSVGElement,!i&&o?[o]:a?null:r.firstChild?e.call(r.childNodes):null,l,!i&&o?o:a?a.__e:r.firstChild,i,s),P(l,n,s)}(h((({data:e,body:t,filterFooter:n})=>{const r=ee($e,[]),[o,i]=Y(void 0),a=ee((()=>n?h(n,{viewType:"jsProfileVisualizer.heapprofile.flame",requireExtension:"ms-vscode.vscode-js-profile-flame"}):void 0),[n]);return h(m,null,h("div",{className:Ke.filter},h(r,{data:e,onChange:i,placeholder:"Filter functions or files, or start a query()",foot:a})),h("div",{className:Ke.rows},o&&h(t,{query:o,data:e.data})))}),{data:{data:ve.fromArray(Ct,(e=>Object.values(e.children))),genericMatchStr:e=>[e.callFrame.functionName,e.callFrame.url,e.src?.source.path??""].join(" "),properties:{function:{type:ue.String,accessor:e=>e.callFrame.functionName},url:{type:ue.String,accessor:e=>e.callFrame.url},path:{type:ue.String,accessor:e=>e.src?.relativePath??e.callFrame.url},line:{type:ue.Number,accessor:e=>e.src?e.src.lineNumber:e.callFrame.lineNumber},selfSize:{type:ue.Number,accessor:e=>e.selfSize},totalSize:{type:ue.Number,accessor:e=>e.totalSize||0},id:{type:ue.Number,accessor:e=>e.id}}},body:({query:e,data:t})=>h(xt,{query:e,data:t}),filterFooter:({viewType:e,requireExtension:t})=>{const n=ne(Ze),r=te((()=>n.postMessage({type:"reopenWith",viewType:e,requireExtension:t})),[n]);return h(Xe,{icon:rt,label:"Show flame graph",checked:!1,onClick:r})}}),At)})()})();