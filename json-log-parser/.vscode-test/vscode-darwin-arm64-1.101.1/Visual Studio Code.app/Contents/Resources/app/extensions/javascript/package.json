{"name": "javascript", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "0.10.x"}, "categories": ["Programming Languages"], "contributes": {"configurationDefaults": {"[javascript]": {"editor.maxTokenizationLineLength": 2500}}, "languages": [{"id": "javascriptreact", "aliases": ["JavaScript JSX", "JavaScript React", "jsx"], "extensions": [".jsx"], "configuration": "./javascript-language-configuration.json"}, {"id": "javascript", "aliases": ["JavaScript", "javascript", "js"], "extensions": [".js", ".es6", ".mjs", ".cjs", ".pac"], "filenames": ["jake<PERSON>le"], "firstLine": "^#!.*\\bnode", "mimetypes": ["text/javascript"], "configuration": "./javascript-language-configuration.json"}, {"id": "jsx-tags", "aliases": [], "configuration": "./tags-language-configuration.json"}], "grammars": [{"language": "javascriptreact", "scopeName": "source.js.jsx", "path": "./syntaxes/JavaScriptReact.tmLanguage.json", "embeddedLanguages": {"meta.tag.js": "jsx-tags", "meta.tag.without-attributes.js": "jsx-tags", "meta.tag.attributes.js.jsx": "javascriptreact", "meta.embedded.expression.js": "javascriptreact"}, "tokenTypes": {"punctuation.definition.template-expression": "other", "entity.name.type.instance.jsdoc": "other", "entity.name.function.tagged-template": "other", "meta.import string.quoted": "other", "variable.other.jsdoc": "other"}}, {"language": "javascript", "scopeName": "source.js", "path": "./syntaxes/JavaScript.tmLanguage.json", "embeddedLanguages": {"meta.tag.js": "jsx-tags", "meta.tag.without-attributes.js": "jsx-tags", "meta.tag.attributes.js": "javascript", "meta.embedded.expression.js": "javascript"}, "tokenTypes": {"punctuation.definition.template-expression": "other", "entity.name.type.instance.jsdoc": "other", "entity.name.function.tagged-template": "other", "meta.import string.quoted": "other", "variable.other.jsdoc": "other"}}, {"scopeName": "source.js.regexp", "path": "./syntaxes/Regular Expressions (JavaScript).tmLanguage"}], "semanticTokenScopes": [{"language": "javascript", "scopes": {"property": ["variable.other.property.js"], "property.readonly": ["variable.other.constant.property.js"], "variable": ["variable.other.readwrite.js"], "variable.readonly": ["variable.other.constant.object.js"], "function": ["entity.name.function.js"], "namespace": ["entity.name.type.module.js"], "variable.defaultLibrary": ["support.variable.js"], "function.defaultLibrary": ["support.function.js"]}}, {"language": "javascriptreact", "scopes": {"property": ["variable.other.property.jsx"], "property.readonly": ["variable.other.constant.property.jsx"], "variable": ["variable.other.readwrite.jsx"], "variable.readonly": ["variable.other.constant.object.jsx"], "function": ["entity.name.function.jsx"], "namespace": ["entity.name.type.module.jsx"], "variable.defaultLibrary": ["support.variable.js"], "function.defaultLibrary": ["support.function.js"]}}], "snippets": [{"language": "javascript", "path": "./snippets/javascript.code-snippets"}, {"language": "javascriptreact", "path": "./snippets/javascript.code-snippets"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}