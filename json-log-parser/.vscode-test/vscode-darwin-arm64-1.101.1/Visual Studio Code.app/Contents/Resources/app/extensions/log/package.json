{"name": "log", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin emilast/vscode-logfile-highlighter syntaxes/log.tmLanguage ./syntaxes/log.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "log", "extensions": [".log", "*.log.?"], "aliases": ["Log"]}], "grammars": [{"language": "log", "scopeName": "text.log", "path": "./syntaxes/log.tmLanguage.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}