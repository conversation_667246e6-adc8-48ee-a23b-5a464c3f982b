{"displayName": "Reference Search View", "description": "Reference Search results as separate, stable view in the sidebar", "config.references.preferredLocation": "Controls whether 'Peek References' or 'Find References' is invoked when selecting CodeLens references.", "config.references.preferredLocation.peek": "Show references in peek editor.", "config.references.preferredLocation.view": "Show references in separate view.", "container.title": "References", "view.title": "Reference Search Results", "cmd.category.references": "References", "cmd.references-view.findReferences": "Find All References", "cmd.references-view.findImplementations": "Find All Implementations", "cmd.references-view.clearHistory": "Clear History", "cmd.references-view.clear": "Clear", "cmd.references-view.refresh": "Refresh", "cmd.references-view.pickFromHistory": "Show History", "cmd.references-view.removeReferenceItem": "<PERSON><PERSON><PERSON>", "cmd.references-view.copy": "Copy", "cmd.references-view.copyAll": "Copy All", "cmd.references-view.copyPath": "Copy Path", "cmd.references-view.refind": "<PERSON><PERSON>", "cmd.references-view.showCallHierarchy": "Show Call Hierarchy", "cmd.references-view.showOutgoingCalls": "Show Outgoing Calls", "cmd.references-view.showIncomingCalls": "Show Incoming Calls", "cmd.references-view.removeCallItem": "<PERSON><PERSON><PERSON>", "cmd.references-view.next": "Go to Next Reference", "cmd.references-view.prev": "Go to Previous Reference", "cmd.references-view.showTypeHierarchy": "Show Type Hierarchy", "cmd.references-view.showSupertypes": "Show Supertypes", "cmd.references-view.showSubtypes": "Show Subtypes", "cmd.references-view.removeTypeItem": "<PERSON><PERSON><PERSON>"}