{"name": "julia", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "0.10.x"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin JuliaEditorSupport/atom-language-julia grammars/julia_vscode.json ./syntaxes/julia.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "julia", "aliases": ["<PERSON>", "julia"], "extensions": [".jl"], "firstLine": "^#!\\s*/.*\\bjulia[0-9.-]*\\b", "configuration": "./language-configuration.json"}, {"id": "juliamarkdown", "aliases": ["<PERSON>", "juliamarkdown"], "extensions": [".jmd"]}], "grammars": [{"language": "julia", "scopeName": "source.julia", "path": "./syntaxes/julia.tmLanguage.json", "embeddedLanguages": {"meta.embedded.inline.cpp": "cpp", "meta.embedded.inline.javascript": "javascript", "meta.embedded.inline.python": "python", "meta.embedded.inline.r": "r", "meta.embedded.inline.sql": "sql"}}], "configurationDefaults": {"[julia]": {"editor.defaultColorDecorators": "never"}}}}