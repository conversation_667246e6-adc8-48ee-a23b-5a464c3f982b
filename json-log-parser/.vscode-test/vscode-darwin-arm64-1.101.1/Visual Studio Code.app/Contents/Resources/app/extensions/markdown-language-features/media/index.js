var qe=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var He=qe((It,Ie)=>{var ke="Expected a function",Oe=NaN,ot="[object Symbol]",st=/^\s+|\s+$/g,lt=/^[-+]0x[0-9a-f]+$/i,ct=/^0b[01]+$/i,dt=/^0o[0-7]+$/i,ut=parseInt,ft=typeof global=="object"&&global&&global.Object===Object&&global,mt=typeof self=="object"&&self&&self.Object===Object&&self,gt=ft||mt||Function("return this")(),pt=Object.prototype,vt=pt.toString,ht=Math.max,bt=Math.min,pe=function(){return gt.Date.now()};function yt(e,t,n){var r,i,a,s,o,d,m=0,y=!1,T=!1,S=!0;if(typeof e!="function")throw new TypeError(ke);t=De(t)||0,ne(n)&&(y=!!n.leading,T="maxWait"in n,a=T?ht(De(n.maxWait)||0,t):a,S="trailing"in n?!!n.trailing:S);function M(g){var w=r,k=i;return r=i=void 0,m=g,s=e.apply(k,w),s}function re(g){return m=g,o=setTimeout(E,t),y?M(g):s}function X(g){var w=g-d,k=g-m,I=t-w;return T?bt(I,a-k):I}function R(g){var w=g-d,k=g-m;return d===void 0||w>=t||w<0||T&&k>=a}function E(){var g=pe();if(R(g))return D(g);o=setTimeout(E,X(g))}function D(g){return o=void 0,S&&r?M(g):(r=i=void 0,s)}function B(){o!==void 0&&clearTimeout(o),m=0,r=d=i=o=void 0}function Y(){return o===void 0?s:D(pe())}function N(){var g=pe(),w=R(g);if(r=arguments,i=this,d=g,w){if(o===void 0)return re(d);if(T)return o=setTimeout(E,t),M(d)}return o===void 0&&(o=setTimeout(E,t)),s}return N.cancel=B,N.flush=Y,N}function Tt(e,t,n){var r=!0,i=!0;if(typeof e!="function")throw new TypeError(ke);return ne(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),yt(e,t,{leading:r,maxWait:t,trailing:i})}function ne(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function wt(e){return!!e&&typeof e=="object"}function Et(e){return typeof e=="symbol"||wt(e)&&vt.call(e)==ot}function De(e){if(typeof e=="number")return e;if(Et(e))return Oe;if(ne(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=ne(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(st,"");var n=ct.test(e);return n||dt.test(e)?ut(e.slice(2),n?2:8):lt.test(e)?Oe:+e}Ie.exports=Tt});var we="code-line",U=class{constructor(t,n,r){this.element=t;this.line=n;this.codeElement=r;this._detailParentElements=Array.from(Xe(t,"DETAILS"))}get isVisible(){return!this._detailParentElements.some(t=>!t.open)}},se=(()=>{let e,t=-1;return n=>{if(!e||n!==t){t=n,e=[new U(document.body,-1)];for(let r of document.getElementsByClassName(we)){if(!(r instanceof HTMLElement))continue;let i=+r.getAttribute("data-line");isNaN(i)||(r.tagName==="CODE"&&r.parentElement&&r.parentElement.tagName==="PRE"?e.push(new U(r.parentElement,i,r)):r.tagName==="UL"||r.tagName==="OL"||e.push(new U(r,i)))}}return e}})();function le(e,t){let n=Math.floor(e),r=se(t),i=r[0]||null;for(let a of r){if(a.line===n)return{previous:a,next:void 0};if(a.line>n)return{previous:i,next:a};i=a}return{previous:i}}function ze(e,t){let n=se(t).filter(d=>d.isVisible),r=e-window.scrollY,i=-1,a=n.length-1;for(;i+1<a;){let d=Math.floor((i+a)/2),m=W(n[d]);m.top+m.height>=r?a=d:i=d}let s=n[a],o=W(s);return a>=1&&o.top>r?{previous:n[i],next:s}:a>1&&a<n.length&&o.top+o.height>r?{previous:s,next:n[a+1]}:{previous:s}}function W({element:e}){let t=e.getBoundingClientRect(),n=e.querySelector(`.${we}`);if(n){let r=n.getBoundingClientRect(),i=Math.max(1,r.top-t.top);return{top:t.top,height:i}}return t}function J(e,t,n){if(!n.settings?.scrollPreviewWithEditor)return;if(e<=0){window.scroll(window.scrollX,0);return}let{previous:r,next:i}=le(e,t);if(!r)return;let a=0,s=W(r),o=s.top;if(i&&i.line!==r.line){let d=(e-r.line)/(i.line-r.line),m=o+s.height,y=i.element.getBoundingClientRect().top-m;a=m+d*y}else{let d=e-Math.floor(e);a=o+s.height*d}a=Math.abs(a)<1?Math.sign(a):a,window.scroll(window.scrollX,Math.max(1,window.scrollY+a))}function ce(e,t){let{previous:n,next:r}=ze(e,t);if(n){if(n.line<0)return 0;let i=W(n),a=e-window.scrollY-i.top;if(r){let s=a/(W(r).top-i.top);return n.line+s*(r.line-n.line)}else{let s=a/i.height;return n.line+s}}return null}function Ee(e,t){return se(t).find(n=>n.element.id===e)}function*Xe(e,t){for(let n=e.parentElement;n;n=n.parentElement)n.tagName===t&&(yield n)}var Q=class{onDidChangeTextEditorSelection(t,n){let{previous:r}=le(t,n);this._update(r&&(r.codeElement||r.element))}_update(t){this._unmarkActiveElement(this._current),this._markActiveElement(t),this._current=t}_unmarkActiveElement(t){t&&t.classList.toggle("code-active-line",!1)}_markActiveElement(t){t&&t.classList.toggle("code-active-line",!0)}};function Se(e){document.readyState==="loading"||document.readyState==="uninitialized"?document.addEventListener("DOMContentLoaded",e):e()}var Ae=(e,t)=>({postMessage(n,r){e.postMessage({type:n,source:t.settings.source,...r})}});function de(e){let t=document.getElementById("vscode-markdown-preview-data");if(t){let n=t.getAttribute(e);if(n)return n}throw new Error(`Could not load data for ${e}`)}function ue(e){return JSON.parse(de(e))}var Z=class{constructor(){this._settings=ue("data-settings")}get settings(){return this._settings}updateSettings(t){this._settings=t}};var Le=11;function Ye(e,t){var n=t.attributes,r,i,a,s,o;if(!(t.nodeType===Le||e.nodeType===Le)){for(var d=n.length-1;d>=0;d--)r=n[d],i=r.name,a=r.namespaceURI,s=r.value,a?(i=r.localName||i,o=e.getAttributeNS(a,i),o!==s&&(r.prefix==="xmlns"&&(i=r.name),e.setAttributeNS(a,i,s))):(o=e.getAttribute(i),o!==s&&e.setAttribute(i,s));for(var m=e.attributes,y=m.length-1;y>=0;y--)r=m[y],i=r.name,a=r.namespaceURI,a?(i=r.localName||i,t.hasAttributeNS(a,i)||e.removeAttributeNS(a,i)):t.hasAttribute(i)||e.removeAttribute(i)}}var ee,Ge="http://www.w3.org/1999/xhtml",b=typeof document>"u"?void 0:document,$e=!!b&&"content"in b.createElement("template"),Ke=!!b&&b.createRange&&"createContextualFragment"in b.createRange();function Je(e){var t=b.createElement("template");return t.innerHTML=e,t.content.childNodes[0]}function Qe(e){ee||(ee=b.createRange(),ee.selectNode(b.body));var t=ee.createContextualFragment(e);return t.childNodes[0]}function Ze(e){var t=b.createElement("body");return t.innerHTML=e,t.childNodes[0]}function et(e){return e=e.trim(),$e?Je(e):Ke?Qe(e):Ze(e)}function te(e,t){var n=e.nodeName,r=t.nodeName,i,a;return n===r?!0:(i=n.charCodeAt(0),a=r.charCodeAt(0),i<=90&&a>=97?n===r.toUpperCase():a<=90&&i>=97?r===n.toUpperCase():!1)}function tt(e,t){return!t||t===Ge?b.createElement(e):b.createElementNS(t,e)}function nt(e,t){for(var n=e.firstChild;n;){var r=n.nextSibling;t.appendChild(n),n=r}return t}function fe(e,t,n){e[n]!==t[n]&&(e[n]=t[n],e[n]?e.setAttribute(n,""):e.removeAttribute(n))}var xe={OPTION:function(e,t){var n=e.parentNode;if(n){var r=n.nodeName.toUpperCase();r==="OPTGROUP"&&(n=n.parentNode,r=n&&n.nodeName.toUpperCase()),r==="SELECT"&&!n.hasAttribute("multiple")&&(e.hasAttribute("selected")&&!t.selected&&(e.setAttribute("selected","selected"),e.removeAttribute("selected")),n.selectedIndex=-1)}fe(e,t,"selected")},INPUT:function(e,t){fe(e,t,"checked"),fe(e,t,"disabled"),e.value!==t.value&&(e.value=t.value),t.hasAttribute("value")||e.removeAttribute("value")},TEXTAREA:function(e,t){var n=t.value;e.value!==n&&(e.value=n);var r=e.firstChild;if(r){var i=r.nodeValue;if(i==n||!n&&i==e.placeholder)return;r.nodeValue=n}},SELECT:function(e,t){if(!t.hasAttribute("multiple")){for(var n=-1,r=0,i=e.firstChild,a,s;i;)if(s=i.nodeName&&i.nodeName.toUpperCase(),s==="OPTGROUP")a=i,i=a.firstChild;else{if(s==="OPTION"){if(i.hasAttribute("selected")){n=r;break}r++}i=i.nextSibling,!i&&a&&(i=a.nextSibling,a=null)}e.selectedIndex=n}}},V=1,Me=11,Ne=3,Ce=8;function O(){}function rt(e){if(e)return e.getAttribute&&e.getAttribute("id")||e.id}function it(e){return function(n,r,i){if(i||(i={}),typeof r=="string")if(n.nodeName==="#document"||n.nodeName==="HTML"||n.nodeName==="BODY"){var a=r;r=b.createElement("html"),r.innerHTML=a}else r=et(r);else r.nodeType===Me&&(r=r.firstElementChild);var s=i.getNodeKey||rt,o=i.onBeforeNodeAdded||O,d=i.onNodeAdded||O,m=i.onBeforeElUpdated||O,y=i.onElUpdated||O,T=i.onBeforeNodeDiscarded||O,S=i.onNodeDiscarded||O,M=i.onBeforeElChildrenUpdated||O,re=i.skipFromChildren||O,X=i.addChild||function(l,c){return l.appendChild(c)},R=i.childrenOnly===!0,E=Object.create(null),D=[];function B(l){D.push(l)}function Y(l,c){if(l.nodeType===V)for(var p=l.firstChild;p;){var u=void 0;c&&(u=s(p))?B(u):(S(p),p.firstChild&&Y(p,c)),p=p.nextSibling}}function N(l,c,p){T(l)!==!1&&(c&&c.removeChild(l),S(l),Y(l,p))}function g(l){if(l.nodeType===V||l.nodeType===Me)for(var c=l.firstChild;c;){var p=s(c);p&&(E[p]=c),g(c),c=c.nextSibling}}g(n);function w(l){d(l);for(var c=l.firstChild;c;){var p=c.nextSibling,u=s(c);if(u){var f=E[u];f&&te(c,f)?(c.parentNode.replaceChild(f,c),I(f,c)):w(c)}else w(c);c=p}}function k(l,c,p){for(;c;){var u=c.nextSibling;(p=s(c))?B(p):N(c,l,!0),c=u}}function I(l,c,p){var u=s(c);if(u&&delete E[u],!p){var f=m(l,c);if(f===!1||(f instanceof HTMLElement&&(l=f,g(l)),e(l,c),y(l),M(l,c)===!1))return}l.nodeName!=="TEXTAREA"?Ve(l,c):xe.TEXTAREA(l,c)}function Ve(l,c){var p=re(l,c),u=c.firstChild,f=l.firstChild,F,A,_,$,C;e:for(;u;){for($=u.nextSibling,F=s(u);!p&&f;){if(_=f.nextSibling,u.isSameNode&&u.isSameNode(f)){u=$,f=_;continue e}A=s(f);var K=f.nodeType,P=void 0;if(K===u.nodeType&&(K===V?(F?F!==A&&((C=E[F])?_===C?P=!1:(l.insertBefore(C,f),A?B(A):N(f,l,!0),f=C,A=s(f)):P=!1):A&&(P=!1),P=P!==!1&&te(f,u),P&&I(f,u)):(K===Ne||K==Ce)&&(P=!0,f.nodeValue!==u.nodeValue&&(f.nodeValue=u.nodeValue))),P){u=$,f=_;continue e}A?B(A):N(f,l,!0),f=_}if(F&&(C=E[F])&&te(C,u))p||X(l,C),I(C,u);else{var oe=o(u);oe!==!1&&(oe&&(u=oe),u.actualize&&(u=u.actualize(l.ownerDocument||b)),X(l,u),w(u))}u=$,f=_}k(l,f,A);var Te=xe[l.nodeName];Te&&Te(l,c)}var v=n,G=v.nodeType,ye=r.nodeType;if(!R){if(G===V)ye===V?te(n,r)||(S(n),v=nt(n,tt(r.nodeName,r.namespaceURI))):v=r;else if(G===Ne||G===Ce){if(ye===G)return v.nodeValue!==r.nodeValue&&(v.nodeValue=r.nodeValue),v;v=r}}if(v===r)S(n);else{if(r.isSameNode&&r.isSameNode(v))return;if(I(v,r,R),D)for(var ie=0,je=D.length;ie<je;ie++){var ae=E[D[ie]];ae&&N(ae,ae.parentNode,!1)}}return!R&&v!==n&&n.parentNode&&(v.actualize&&(v=v.actualize(n.ownerDocument||b)),n.parentNode.replaceChild(v,n)),v}}var at=it(Ye),Pe=at;var me=Object.freeze({http:"http",https:"https",file:"file",untitled:"untitled",mailto:"mailto",vscode:"vscode","vscode-insiders":"vscode-insiders",notebookCell:"vscode-notebook-cell"});function ge(e,t){return t.toLowerCase().startsWith(e+":")}var Re=He(),H=0,Be=new Q,h=new Z,L=0,j=h.settings.source,q=acquireVsCodeApi(),ve=q.getState()??{},x={...ve,...ue("data-state")};typeof ve.scrollProgress<"u"&&ve?.resource!==x.resource&&(x.scrollProgress=0);q.setState(x);var z=Ae(q,h);window.cspAlerter.setPoster(z);window.styleLoadingMonitor.setPoster(z);function he(e){let t=document.getElementsByTagName("img");if(t.length>0){let n=Array.from(t,r=>r.complete?Promise.resolve():new Promise(i=>{r.addEventListener("load",()=>i()),r.addEventListener("error",()=>i())}));Promise.all(n).then(()=>setTimeout(e,0))}else setTimeout(e,0)}Se(()=>{let n=[...new DOMParser().parseFromString(de("data-initial-md-content"),"text/html").body.children];document.body.append(...n);for(let i of n)i instanceof HTMLElement&&be(i);let r=x.scrollProgress;if(Fe(),typeof r=="number"&&!h.settings.fragment){he(()=>{H+=1;let i=Math.max(1,r*document.body.clientHeight);window.scrollTo(0,i)});return}h.settings.scrollPreviewWithEditor&&he(()=>{if(h.settings.fragment){let i;try{i=encodeURIComponent(h.settings.fragment)}catch{i=h.settings.fragment}x.fragment=void 0,q.setState(x);let a=Ee(i,L);a&&(H+=1,J(a.line,L,h))}else isNaN(h.settings.line)||(H+=1,J(h.settings.line,L,h))}),typeof h.settings.selectedLine=="number"&&Be.onDidChangeTextEditorSelection(h.settings.selectedLine,L)});var St=(()=>{let e=Re(t=>{H+=1,he(()=>J(t,L,h))},50);return t=>{isNaN(t)||(x.line=t,e(t))}})();window.addEventListener("resize",()=>{H+=1,Ue()},!0);function Fe(){let e=document.getElementsByTagName("img"),t=0;for(let n of e){n.id="image-"+t,t+=1;let r=n.getAttribute("data-src"),a=r&&!(ge(me.http,r)||ge(me.https,r))?"localImage":"image";n.setAttribute("data-vscode-context",JSON.stringify({webviewSection:a,id:n.id,preventDefaultContextMenuItems:!0,resource:j,imageSource:r}))}}async function _e(e,t=5){if(!document.hasFocus()&&t>0){setTimeout(()=>{_e(e,t-1)},20);return}try{await navigator.clipboard.write([new ClipboardItem({"image/png":new Promise(n=>{let r=document.createElement("canvas");r!==null&&(r.width=e.naturalWidth,r.height=e.naturalHeight,r.getContext("2d")?.drawImage(e,0,0)),r.toBlob(i=>{i&&n(i),r.remove()},"image/png")})})])}catch(n){console.error(n);let r=window.getSelection();if(!r){await navigator.clipboard.writeText(e.getAttribute("data-src")??e.src);return}r.removeAllRanges();let i=document.createRange();i.selectNode(e),r.addRange(i),document.execCommand("copy"),r.removeAllRanges()}}window.addEventListener("message",async e=>{let t=e.data;switch(t.type){case"copyImage":{let n=document.getElementById(t.id);n instanceof HTMLImageElement&&_e(n);return}case"onDidChangeTextEditorSelection":t.source===j&&Be.onDidChangeTextEditorSelection(t.line,L);return;case"updateView":t.source===j&&St(t.line);return;case"updateContent":{let n=document.querySelector(".markdown-body"),i=new DOMParser().parseFromString(t.content,"text/html");for(let a of Array.from(i.querySelectorAll("meta")))a.hasAttribute("http-equiv")&&a.remove();if(t.source!==j){j=t.source;let a=i.querySelector(".markdown-body");n.replaceWith(a),be(a)}else{let a=i.querySelector(".markdown-body"),s=a.querySelectorAll("link");for(let o of s)o.remove();a.prepend(...s),Pe(n,a,{childrenOnly:!0,onBeforeElUpdated:(o,d)=>{if(We(o,d)){let m=o.querySelectorAll("[data-line]"),y=d.querySelectorAll("[data-line]");m.length!==y.length&&console.log("unexpected line number change");for(let T=0;T<m.length;++T){let S=m[T],M=y[T];M&&S.setAttribute("data-line",M.getAttribute("data-line"))}return!1}return o.tagName==="DETAILS"&&d.tagName==="DETAILS"&&o.hasAttribute("open")&&d.setAttribute("open",""),!0},addChild:(o,d)=>{o.appendChild(d),d instanceof HTMLElement&&be(d)}})}++L,window.dispatchEvent(new CustomEvent("vscode.markdown.updateContent")),Fe();break}}},!1);document.addEventListener("dblclick",e=>{if(!h.settings.doubleClickToSwitchToEditor)return;for(let r=e.target;r;r=r.parentNode)if(r.tagName==="A")return;let t=e.pageY,n=ce(t,L);typeof n=="number"&&!isNaN(n)&&z.postMessage("didClick",{line:Math.floor(n)})});var At=["http:","https:","mailto:","vscode:","vscode-insiders:"];document.addEventListener("click",e=>{if(!e)return;let t=e.target;for(;t;){if(t.tagName&&t.tagName==="A"&&t.href){if(t.getAttribute("href").startsWith("#"))return;let n=t.getAttribute("data-href");if(!n&&(n=t.getAttribute("href"),At.some(r=>n.startsWith(r))))return;if(!/^[a-z\-]+:/i.test(n)){z.postMessage("openLink",{href:n}),e.preventDefault(),e.stopPropagation();return}return}t=t.parentNode}},!0);window.addEventListener("scroll",Re(()=>{if(Ue(),H>0)H-=1;else{let e=ce(window.scrollY,L);typeof e=="number"&&!isNaN(e)&&z.postMessage("revealLine",{line:e})}},50));function Ue(){x.scrollProgress=window.scrollY/document.body.clientHeight,q.setState(x)}function We(e,t){let n=["open"];if(e.isEqualNode(t))return!0;if(e.tagName!==t.tagName||e.textContent!==t.textContent)return!1;let r=[...e.attributes].filter(o=>!n.includes(o.name)),i=[...t.attributes].filter(o=>!n.includes(o.name));if(r.length!==i.length)return!1;for(let o=0;o<r.length;++o){let d=r[o],m=i[o];if(d.name!==m.name||d.value!==m.value&&d.name!=="data-line")return!1}let a=Array.from(e.children),s=Array.from(t.children);return a.length===s.length&&a.every((o,d)=>We(o,s[d]))}function be(e){let t=["type","src","nonce","noModule","async"],n=e.tagName==="SCRIPT"?[e]:Array.from(e.getElementsByTagName("script"));for(let r of n){if(!(r instanceof HTMLElement))continue;let i=document.createElement("script"),a=r.innerText;i.text=a;for(let s of t){let o=r.getAttribute&&r.getAttribute(s);o&&i.setAttribute(s,o)}r.insertAdjacentElement("afterend",i),r.remove()}}
