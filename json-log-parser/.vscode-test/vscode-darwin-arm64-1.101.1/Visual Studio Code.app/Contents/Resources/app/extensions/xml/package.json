{"name": "xml", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "xml", "extensions": [".xml", ".xsd", ".ascx", ".atom", ".axml", ".axaml", ".bpmn", ".cpt", ".csl", ".c<PERSON><PERSON>j", ".csproj.user", ".dita", ".ditamap", ".dtd", ".ent", ".mod", ".dtml", ".fsproj", ".fxml", ".iml", ".isml", ".jmx", ".launch", ".menu", ".mxml", ".nuspec", ".opml", ".owl", ".proj", ".props", ".pt", ".publishsettings", ".pubxml", ".pubxml.user", ".rbxlx", ".rbxmx", ".rdf", ".rng", ".rss", ".shp<PERSON>j", ".storyboard", ".svg", ".targets", ".tld", ".tmx", ".vbproj", ".vbproj.user", ".vcxproj", ".vcxproj.filters", ".wsdl", ".wxi", ".wxl", ".wxs", ".xaml", ".xbl", ".xib", ".xlf", ".xliff", ".xpdl", ".xul", ".xoml"], "firstLine": "(\\<\\?xml.*)|(\\<svg)|(\\<\\!doctype\\s+svg)", "aliases": ["XML", "xml"], "configuration": "./xml.language-configuration.json"}, {"id": "xsl", "extensions": [".xsl", ".xslt"], "aliases": ["XSL", "xsl"], "configuration": "./xsl.language-configuration.json"}], "grammars": [{"language": "xml", "scopeName": "text.xml", "path": "./syntaxes/xml.tmLanguage.json"}, {"language": "xsl", "scopeName": "text.xml.xsl", "path": "./syntaxes/xsl.tmLanguage.json"}]}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin atom/language-xml grammars/xml.cson ./syntaxes/xml.tmLanguage.json grammars/xsl.cson ./syntaxes/xsl.tmLanguage.json"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}