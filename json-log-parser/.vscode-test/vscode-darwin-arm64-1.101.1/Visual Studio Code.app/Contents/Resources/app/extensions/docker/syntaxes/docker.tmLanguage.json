{"information_for_contributors": ["This file has been converted from https://github.com/moby/moby/blob/master/contrib/syntax/textmate/Docker.tmbundle/Syntaxes/Dockerfile.tmLanguage", "If you want to provide a fix or improvement, please create a pull request against the original repository.", "Once accepted there, we are happy to receive an update request."], "version": "https://github.com/moby/moby/commit/c2029cb2574647e4bc28ed58486b8e85883eedb9", "name": "Dockerfile", "scopeName": "source.dockerfile", "patterns": [{"captures": {"1": {"name": "keyword.other.special-method.dockerfile"}, "2": {"name": "keyword.other.special-method.dockerfile"}}, "match": "^\\s*\\b(?i:(FROM))\\b.*?\\b(?i:(AS))\\b"}, {"captures": {"1": {"name": "keyword.control.dockerfile"}, "2": {"name": "keyword.other.special-method.dockerfile"}}, "match": "^\\s*(?i:(ONBUILD)\\s+)?(?i:(ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR))\\s"}, {"captures": {"1": {"name": "keyword.operator.dockerfile"}, "2": {"name": "keyword.other.special-method.dockerfile"}}, "match": "^\\s*(?i:(ONBUILD)\\s+)?(?i:(CMD|ENTRYPOINT))\\s"}, {"include": "#string-character-escape"}, {"begin": "\"", "beginCaptures": {"1": {"name": "punctuation.definition.string.begin.dockerfile"}}, "end": "\"", "endCaptures": {"1": {"name": "punctuation.definition.string.end.dockerfile"}}, "name": "string.quoted.double.dockerfile", "patterns": [{"include": "#string-character-escape"}]}, {"begin": "'", "beginCaptures": {"1": {"name": "punctuation.definition.string.begin.dockerfile"}}, "end": "'", "endCaptures": {"1": {"name": "punctuation.definition.string.end.dockerfile"}}, "name": "string.quoted.single.dockerfile", "patterns": [{"include": "#string-character-escape"}]}, {"captures": {"1": {"name": "punctuation.whitespace.comment.leading.dockerfile"}, "2": {"name": "comment.line.number-sign.dockerfile"}, "3": {"name": "punctuation.definition.comment.dockerfile"}}, "comment": "comment.line", "match": "^(\\s*)((#).*$\\n?)"}], "repository": {"string-character-escape": {"name": "constant.character.escaped.dockerfile", "match": "\\\\."}}}