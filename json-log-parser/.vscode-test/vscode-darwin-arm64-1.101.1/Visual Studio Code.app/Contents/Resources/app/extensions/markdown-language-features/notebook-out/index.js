var ts=Object.create;var yt=Object.defineProperty;var ns=Object.getOwnPropertyDescriptor;var is=Object.getOwnPropertyNames;var os=Object.getPrototypeOf,ss=Object.prototype.hasOwnProperty;var b=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var as=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of is(e))!ss.call(t,i)&&i!==r&&yt(t,i,{get:()=>e[i],enumerable:!(n=ns(e,i))||n.enumerable});return t};var ls=(t,e,r)=>(r=t!=null?ts(os(t)):{},as(e||!t||!t.__esModule?yt(r,"default",{value:t,enumerable:!0}):r,t));var Ut=b(($l,qs)=>{qs.exports={Aacute:"\xC1",aacute:"\xE1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223E",acd:"\u223F",acE:"\u223E\u0333",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",Acy:"\u0410",acy:"\u0430",AElig:"\xC6",aelig:"\xE6",af:"\u2061",Afr:"\u{1D504}",afr:"\u{1D51E}",Agrave:"\xC0",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03B1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2A3F",amp:"&",AMP:"&",andand:"\u2A55",And:"\u2A53",and:"\u2227",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angmsd:"\u2221",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",Aogon:"\u0104",aogon:"\u0105",Aopf:"\u{1D538}",aopf:"\u{1D552}",apacir:"\u2A6F",ap:"\u2248",apE:"\u2A70",ape:"\u224A",apid:"\u224B",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224A",Aring:"\xC5",aring:"\xE5",Ascr:"\u{1D49C}",ascr:"\u{1D4B6}",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224D",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",Backslash:"\u2216",Barv:"\u2AE7",barvee:"\u22BD",barwed:"\u2305",Barwed:"\u2306",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",Because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",Bernoullis:"\u212C",Beta:"\u0392",beta:"\u03B2",beth:"\u2136",between:"\u226C",Bfr:"\u{1D505}",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bNot:"\u2AED",bnot:"\u2310",Bopf:"\u{1D539}",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxbox:"\u29C9",boxdl:"\u2510",boxdL:"\u2555",boxDl:"\u2556",boxDL:"\u2557",boxdr:"\u250C",boxdR:"\u2552",boxDr:"\u2553",boxDR:"\u2554",boxh:"\u2500",boxH:"\u2550",boxhd:"\u252C",boxHd:"\u2564",boxhD:"\u2565",boxHD:"\u2566",boxhu:"\u2534",boxHu:"\u2567",boxhU:"\u2568",boxHU:"\u2569",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxul:"\u2518",boxuL:"\u255B",boxUl:"\u255C",boxUL:"\u255D",boxur:"\u2514",boxuR:"\u2558",boxUr:"\u2559",boxUR:"\u255A",boxv:"\u2502",boxV:"\u2551",boxvh:"\u253C",boxvH:"\u256A",boxVh:"\u256B",boxVH:"\u256C",boxvl:"\u2524",boxvL:"\u2561",boxVl:"\u2562",boxVL:"\u2563",boxvr:"\u251C",boxvR:"\u255E",boxVr:"\u255F",boxVR:"\u2560",bprime:"\u2035",breve:"\u02D8",Breve:"\u02D8",brvbar:"\xA6",bscr:"\u{1D4B7}",Bscr:"\u212C",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsolb:"\u29C5",bsol:"\\",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",Bumpeq:"\u224E",bumpeq:"\u224F",Cacute:"\u0106",cacute:"\u0107",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",cap:"\u2229",Cap:"\u22D2",capcup:"\u2A47",capdot:"\u2A40",CapitalDifferentialD:"\u2145",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",Cayleys:"\u212D",ccaps:"\u2A4D",Ccaron:"\u010C",ccaron:"\u010D",Ccedil:"\xC7",ccedil:"\xE7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2A4C",ccupssm:"\u2A50",Cdot:"\u010A",cdot:"\u010B",cedil:"\xB8",Cedilla:"\xB8",cemptyv:"\u29B2",cent:"\xA2",centerdot:"\xB7",CenterDot:"\xB7",cfr:"\u{1D520}",Cfr:"\u212D",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03A7",chi:"\u03C7",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",CircleDot:"\u2299",circledR:"\xAE",circledS:"\u24C8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cir:"\u25CB",cirE:"\u29C3",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",colon:":",Colon:"\u2237",Colone:"\u2A74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",Congruent:"\u2261",conint:"\u222E",Conint:"\u222F",ContourIntegral:"\u222E",copf:"\u{1D554}",Copf:"\u2102",coprod:"\u2210",Coproduct:"\u2210",copy:"\xA9",COPY:"\xA9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21B5",cross:"\u2717",Cross:"\u2A2F",Cscr:"\u{1D49E}",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cupbrcap:"\u2A48",cupcap:"\u2A46",CupCap:"\u224D",cup:"\u222A",Cup:"\u22D3",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dagger:"\u2020",Dagger:"\u2021",daleth:"\u2138",darr:"\u2193",Darr:"\u21A1",dArr:"\u21D3",dash:"\u2010",Dashv:"\u2AE4",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",Dcaron:"\u010E",dcaron:"\u010F",Dcy:"\u0414",dcy:"\u0434",ddagger:"\u2021",ddarr:"\u21CA",DD:"\u2145",dd:"\u2146",DDotrahd:"\u2911",ddotseq:"\u2A77",deg:"\xB0",Del:"\u2207",Delta:"\u0394",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",Dfr:"\u{1D507}",dfr:"\u{1D521}",dHar:"\u2965",dharl:"\u21C3",dharr:"\u21C2",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",diam:"\u22C4",diamond:"\u22C4",Diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",DifferentialD:"\u2146",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",Dopf:"\u{1D53B}",dopf:"\u{1D555}",Dot:"\xA8",dot:"\u02D9",DotDot:"\u20DC",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrowBar:"\u2913",downarrow:"\u2193",DownArrow:"\u2193",Downarrow:"\u21D3",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVectorBar:"\u2956",DownLeftVector:"\u21BD",DownRightTeeVector:"\u295F",DownRightVectorBar:"\u2957",DownRightVector:"\u21C1",DownTeeArrow:"\u21A7",DownTee:"\u22A4",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",Dscr:"\u{1D49F}",dscr:"\u{1D4B9}",DScy:"\u0405",dscy:"\u0455",dsol:"\u29F6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",DZcy:"\u040F",dzcy:"\u045F",dzigrarr:"\u27FF",Eacute:"\xC9",eacute:"\xE9",easter:"\u2A6E",Ecaron:"\u011A",ecaron:"\u011B",Ecirc:"\xCA",ecirc:"\xEA",ecir:"\u2256",ecolon:"\u2255",Ecy:"\u042D",ecy:"\u044D",eDDot:"\u2A77",Edot:"\u0116",edot:"\u0117",eDot:"\u2251",ee:"\u2147",efDot:"\u2252",Efr:"\u{1D508}",efr:"\u{1D522}",eg:"\u2A9A",Egrave:"\xC8",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",Element:"\u2208",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25FB",emptyv:"\u2205",EmptyVerySmallSquare:"\u25AB",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",ENG:"\u014A",eng:"\u014B",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\u{1D53C}",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",Epsilon:"\u0395",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",Equal:"\u2A75",equals:"=",EqualTilde:"\u2242",equest:"\u225F",Equilibrium:"\u21CC",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erarr:"\u2971",erDot:"\u2253",escr:"\u212F",Escr:"\u2130",esdot:"\u2250",Esim:"\u2A73",esim:"\u2242",Eta:"\u0397",eta:"\u03B7",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",exponentiale:"\u2147",ExponentialE:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",Ffr:"\u{1D509}",ffr:"\u{1D523}",filig:"\uFB01",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",Fopf:"\u{1D53D}",fopf:"\u{1D557}",forall:"\u2200",ForAll:"\u2200",fork:"\u22D4",forkv:"\u2AD9",Fouriertrf:"\u2131",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",Fscr:"\u2131",gacute:"\u01F5",Gamma:"\u0393",gamma:"\u03B3",Gammad:"\u03DC",gammad:"\u03DD",gap:"\u2A86",Gbreve:"\u011E",gbreve:"\u011F",Gcedil:"\u0122",Gcirc:"\u011C",gcirc:"\u011D",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",ge:"\u2265",gE:"\u2267",gEl:"\u2A8C",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",gescc:"\u2AA9",ges:"\u2A7E",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",Gfr:"\u{1D50A}",gfr:"\u{1D524}",gg:"\u226B",Gg:"\u22D9",ggg:"\u22D9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gla:"\u2AA5",gl:"\u2277",glE:"\u2A92",glj:"\u2AA4",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gnE:"\u2269",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",Gopf:"\u{1D53E}",gopf:"\u{1D558}",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",gtcc:"\u2AA7",gtcir:"\u2A7A",gt:">",GT:">",Gt:"\u226B",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",Hacek:"\u02C7",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",HARDcy:"\u042A",hardcy:"\u044A",harrcir:"\u2948",harr:"\u2194",hArr:"\u21D4",harrw:"\u21AD",Hat:"^",hbar:"\u210F",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",Hfr:"\u210C",HilbertSpace:"\u210B",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",Hopf:"\u210D",horbar:"\u2015",HorizontalLine:"\u2500",hscr:"\u{1D4BD}",Hscr:"\u210B",hslash:"\u210F",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224E",HumpEqual:"\u224F",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xCD",iacute:"\xED",ic:"\u2063",Icirc:"\xCE",icirc:"\xEE",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",Ifr:"\u2111",Igrave:"\xCC",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Imacr:"\u012A",imacr:"\u012B",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",Im:"\u2111",imof:"\u22B7",imped:"\u01B5",Implies:"\u21D2",incare:"\u2105",in:"\u2208",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",intcal:"\u22BA",int:"\u222B",Int:"\u222C",integers:"\u2124",Integral:"\u222B",intercal:"\u22BA",Intersection:"\u22C2",intlarhk:"\u2A17",intprod:"\u2A3C",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012E",iogon:"\u012F",Iopf:"\u{1D540}",iopf:"\u{1D55A}",Iota:"\u0399",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",iscr:"\u{1D4BE}",Iscr:"\u2110",isin:"\u2208",isindot:"\u22F5",isinE:"\u22F9",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xCF",iuml:"\xEF",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\u{1D50D}",jfr:"\u{1D527}",jmath:"\u0237",Jopf:"\u{1D541}",jopf:"\u{1D55B}",Jscr:"\u{1D4A5}",jscr:"\u{1D4BF}",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039A",kappa:"\u03BA",kappav:"\u03F0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041A",kcy:"\u043A",Kfr:"\u{1D50E}",kfr:"\u{1D528}",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040C",kjcy:"\u045C",Kopf:"\u{1D542}",kopf:"\u{1D55C}",Kscr:"\u{1D4A6}",kscr:"\u{1D4C0}",lAarr:"\u21DA",Lacute:"\u0139",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",Lambda:"\u039B",lambda:"\u03BB",lang:"\u27E8",Lang:"\u27EA",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",Laplacetrf:"\u2112",laquo:"\xAB",larrb:"\u21E4",larrbfs:"\u291F",larr:"\u2190",Larr:"\u219E",lArr:"\u21D0",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",latail:"\u2919",lAtail:"\u291B",lat:"\u2AAB",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lBarr:"\u290E",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",Lcaron:"\u013D",lcaron:"\u013E",Lcedil:"\u013B",lcedil:"\u013C",lceil:"\u2308",lcub:"{",Lcy:"\u041B",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",lE:"\u2266",LeftAngleBracket:"\u27E8",LeftArrowBar:"\u21E4",leftarrow:"\u2190",LeftArrow:"\u2190",Leftarrow:"\u21D0",LeftArrowRightArrow:"\u21C6",leftarrowtail:"\u21A2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVectorBar:"\u2959",LeftDownVector:"\u21C3",LeftFloor:"\u230A",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",LeftRightArrow:"\u2194",Leftrightarrow:"\u21D4",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",LeftRightVector:"\u294E",LeftTeeArrow:"\u21A4",LeftTee:"\u22A3",LeftTeeVector:"\u295A",leftthreetimes:"\u22CB",LeftTriangleBar:"\u29CF",LeftTriangle:"\u22B2",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVectorBar:"\u2958",LeftUpVector:"\u21BF",LeftVectorBar:"\u2952",LeftVector:"\u21BC",lEg:"\u2A8B",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",lescc:"\u2AA8",les:"\u2A7D",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2AA1",lesssim:"\u2272",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",lfisht:"\u297C",lfloor:"\u230A",Lfr:"\u{1D50F}",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lHar:"\u2962",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",llarr:"\u21C7",ll:"\u226A",Ll:"\u22D8",llcorner:"\u231E",Lleftarrow:"\u21DA",llhard:"\u296B",lltri:"\u25FA",Lmidot:"\u013F",lmidot:"\u0140",lmoustache:"\u23B0",lmoust:"\u23B0",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lnE:"\u2268",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",LongLeftArrow:"\u27F5",Longleftarrow:"\u27F8",longleftrightarrow:"\u27F7",LongLeftRightArrow:"\u27F7",Longleftrightarrow:"\u27FA",longmapsto:"\u27FC",longrightarrow:"\u27F6",LongRightArrow:"\u27F6",Longrightarrow:"\u27F9",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",Lopf:"\u{1D543}",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",Lscr:"\u2112",lsh:"\u21B0",Lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",Lstrok:"\u0141",lstrok:"\u0142",ltcc:"\u2AA6",ltcir:"\u2A79",lt:"<",LT:"<",Lt:"\u226A",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",ltrPar:"\u2996",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",Mcy:"\u041C",mcy:"\u043C",mdash:"\u2014",mDDot:"\u223A",measuredangle:"\u2221",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",midast:"*",midcir:"\u2AF0",mid:"\u2223",middot:"\xB7",minusb:"\u229F",minus:"\u2212",minusd:"\u2238",minusdu:"\u2A2A",MinusPlus:"\u2213",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",Mopf:"\u{1D544}",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",Mscr:"\u2133",mstpos:"\u223E",Mu:"\u039C",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266E",naturals:"\u2115",natur:"\u266E",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",Ncy:"\u041D",ncy:"\u043D",ndash:"\u2013",nearhk:"\u2924",nearr:"\u2197",neArr:"\u21D7",nearrow:"\u2197",ne:"\u2260",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,nexist:"\u2204",nexists:"\u2204",Nfr:"\u{1D511}",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",nGg:"\u22D9\u0338",ngsim:"\u2275",nGt:"\u226B\u20D2",ngt:"\u226F",ngtr:"\u226F",nGtv:"\u226B\u0338",nharr:"\u21AE",nhArr:"\u21CE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",NJcy:"\u040A",njcy:"\u045A",nlarr:"\u219A",nlArr:"\u21CD",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nleftarrow:"\u219A",nLeftarrow:"\u21CD",nleftrightarrow:"\u21AE",nLeftrightarrow:"\u21CE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nLl:"\u22D8\u0338",nlsim:"\u2274",nLt:"\u226A\u20D2",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nLtv:"\u226A\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xA0",nopf:"\u{1D55F}",Nopf:"\u2115",Not:"\u2AEC",not:"\xAC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",notin:"\u2209",notindot:"\u22F5\u0338",notinE:"\u22F9\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangle:"\u22EB",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",nparallel:"\u2226",npar:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",nprec:"\u2280",npreceq:"\u2AAF\u0338",npre:"\u2AAF\u0338",nrarrc:"\u2933\u0338",nrarr:"\u219B",nrArr:"\u21CF",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nRightarrow:"\u21CF",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",Nscr:"\u{1D4A9}",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",Ntilde:"\xD1",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",Nu:"\u039D",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvDash:"\u22AD",nVdash:"\u22AE",nVDash:"\u22AF",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvHarr:"\u2904",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwarhk:"\u2923",nwarr:"\u2196",nwArr:"\u21D6",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xD3",oacute:"\xF3",oast:"\u229B",Ocirc:"\xD4",ocirc:"\xF4",ocir:"\u229A",Ocy:"\u041E",ocy:"\u043E",odash:"\u229D",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29BF",Ofr:"\u{1D512}",ofr:"\u{1D52C}",ogon:"\u02DB",Ograve:"\xD2",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",Omacr:"\u014C",omacr:"\u014D",Omega:"\u03A9",omega:"\u03C9",Omicron:"\u039F",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",Oopf:"\u{1D546}",oopf:"\u{1D560}",opar:"\u29B7",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",operp:"\u29B9",oplus:"\u2295",orarr:"\u21BB",Or:"\u2A54",or:"\u2228",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oS:"\u24C8",Oscr:"\u{1D4AA}",oscr:"\u2134",Oslash:"\xD8",oslash:"\xF8",osol:"\u2298",Otilde:"\xD5",otilde:"\xF5",otimesas:"\u2A36",Otimes:"\u2A37",otimes:"\u2297",Ouml:"\xD6",ouml:"\xF6",ovbar:"\u233D",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",para:"\xB6",parallel:"\u2225",par:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",PartialD:"\u2202",Pcy:"\u041F",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",Pfr:"\u{1D513}",pfr:"\u{1D52D}",Phi:"\u03A6",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",Pi:"\u03A0",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plus:"+",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",PlusMinus:"\xB1",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",Poincareplane:"\u210C",pointint:"\u2A15",popf:"\u{1D561}",Popf:"\u2119",pound:"\xA3",prap:"\u2AB7",Pr:"\u2ABB",pr:"\u227A",prcue:"\u227C",precapprox:"\u2AB7",prec:"\u227A",preccurlyeq:"\u227C",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",pre:"\u2AAF",prE:"\u2AB3",precsim:"\u227E",prime:"\u2032",Prime:"\u2033",primes:"\u2119",prnap:"\u2AB9",prnE:"\u2AB5",prnsim:"\u22E8",prod:"\u220F",Product:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",Proportional:"\u221D",Proportion:"\u2237",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",Pscr:"\u{1D4AB}",pscr:"\u{1D4C5}",Psi:"\u03A8",psi:"\u03C8",puncsp:"\u2008",Qfr:"\u{1D514}",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",Qopf:"\u211A",qprime:"\u2057",Qscr:"\u{1D4AC}",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quot:'"',QUOT:'"',rAarr:"\u21DB",race:"\u223D\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",Rang:"\u27EB",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarr:"\u2192",Rarr:"\u21A0",rArr:"\u21D2",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",rAtail:"\u291C",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rBarr:"\u290F",RBarr:"\u2910",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",Re:"\u211C",rect:"\u25AD",reg:"\xAE",REG:"\xAE",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",Rfr:"\u211C",rHar:"\u2964",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",Rho:"\u03A1",rho:"\u03C1",rhov:"\u03F1",RightAngleBracket:"\u27E9",RightArrowBar:"\u21E5",rightarrow:"\u2192",RightArrow:"\u2192",Rightarrow:"\u21D2",RightArrowLeftArrow:"\u21C4",rightarrowtail:"\u21A3",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVectorBar:"\u2955",RightDownVector:"\u21C2",RightFloor:"\u230B",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",RightTeeArrow:"\u21A6",RightTee:"\u22A2",RightTeeVector:"\u295B",rightthreetimes:"\u22CC",RightTriangleBar:"\u29D0",RightTriangle:"\u22B3",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVectorBar:"\u2954",RightUpVector:"\u21BE",RightVectorBar:"\u2953",RightVector:"\u21C0",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoustache:"\u23B1",rmoust:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",Ropf:"\u211D",roplus:"\u2A2E",rotimes:"\u2A35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",Rrightarrow:"\u21DB",rsaquo:"\u203A",rscr:"\u{1D4C7}",Rscr:"\u211B",rsh:"\u21B1",Rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",RuleDelayed:"\u29F4",ruluhar:"\u2968",rx:"\u211E",Sacute:"\u015A",sacute:"\u015B",sbquo:"\u201A",scap:"\u2AB8",Scaron:"\u0160",scaron:"\u0161",Sc:"\u2ABC",sc:"\u227B",sccue:"\u227D",sce:"\u2AB0",scE:"\u2AB4",Scedil:"\u015E",scedil:"\u015F",Scirc:"\u015C",scirc:"\u015D",scnap:"\u2ABA",scnE:"\u2AB6",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",Scy:"\u0421",scy:"\u0441",sdotb:"\u22A1",sdot:"\u22C5",sdote:"\u2A66",searhk:"\u2925",searr:"\u2198",seArr:"\u21D8",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\u{1D516}",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xAD",Sigma:"\u03A3",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",SOFTcy:"\u042C",softcy:"\u044C",solbar:"\u233F",solb:"\u29C4",sol:"/",Sopf:"\u{1D54A}",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",Sqrt:"\u221A",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",square:"\u25A1",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25AA",squ:"\u25A1",squf:"\u25AA",srarr:"\u2192",Sscr:"\u{1D4AE}",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",Star:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",Sub:"\u22D0",subdot:"\u2ABD",subE:"\u2AC5",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",Subset:"\u22D0",subseteq:"\u2286",subseteqq:"\u2AC5",SubsetEqual:"\u2286",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succapprox:"\u2AB8",succ:"\u227B",succcurlyeq:"\u227D",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",SuchThat:"\u220B",sum:"\u2211",Sum:"\u2211",sung:"\u266A",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",sup:"\u2283",Sup:"\u22D1",supdot:"\u2ABE",supdsub:"\u2AD8",supE:"\u2AC6",supe:"\u2287",supedot:"\u2AC4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",Supset:"\u22D1",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swarhk:"\u2926",swarr:"\u2199",swArr:"\u21D9",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",Tab:"	",target:"\u2316",Tau:"\u03A4",tau:"\u03C4",tbrk:"\u23B4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",Tfr:"\u{1D517}",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",Therefore:"\u2234",Theta:"\u0398",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",THORN:"\xDE",thorn:"\xFE",tilde:"\u02DC",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",timesbar:"\u2A31",timesb:"\u22A0",times:"\xD7",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",topbot:"\u2336",topcir:"\u2AF1",top:"\u22A4",Topf:"\u{1D54B}",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",TRADE:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",TripleDot:"\u20DB",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",Tscr:"\u{1D4AF}",tscr:"\u{1D4C9}",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040B",tshcy:"\u045B",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",Uacute:"\xDA",uacute:"\xFA",uarr:"\u2191",Uarr:"\u219F",uArr:"\u21D1",Uarrocir:"\u2949",Ubrcy:"\u040E",ubrcy:"\u045E",Ubreve:"\u016C",ubreve:"\u016D",Ucirc:"\xDB",ucirc:"\xFB",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21C5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",Ufr:"\u{1D518}",ufr:"\u{1D532}",Ugrave:"\xD9",ugrave:"\xF9",uHar:"\u2963",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",Umacr:"\u016A",umacr:"\u016B",uml:"\xA8",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",uogon:"\u0173",Uopf:"\u{1D54C}",uopf:"\u{1D566}",UpArrowBar:"\u2912",uparrow:"\u2191",UpArrow:"\u2191",Uparrow:"\u21D1",UpArrowDownArrow:"\u21C5",updownarrow:"\u2195",UpDownArrow:"\u2195",Updownarrow:"\u21D5",UpEquilibrium:"\u296E",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",upsi:"\u03C5",Upsi:"\u03D2",upsih:"\u03D2",Upsilon:"\u03A5",upsilon:"\u03C5",UpTeeArrow:"\u21A5",UpTee:"\u22A5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",Uring:"\u016E",uring:"\u016F",urtri:"\u25F9",Uscr:"\u{1D4B0}",uscr:"\u{1D4CA}",utdot:"\u22F0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",Uuml:"\xDC",uuml:"\xFC",uwangle:"\u29A7",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",vArr:"\u21D5",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vBar:"\u2AE8",Vbar:"\u2AEB",vBarv:"\u2AE9",Vcy:"\u0412",vcy:"\u0432",vdash:"\u22A2",vDash:"\u22A8",Vdash:"\u22A9",VDash:"\u22AB",Vdashl:"\u2AE6",veebar:"\u22BB",vee:"\u2228",Vee:"\u22C1",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",Verbar:"\u2016",vert:"|",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",Vopf:"\u{1D54D}",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",Vscr:"\u{1D4B1}",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",Vvdash:"\u22AA",vzigzag:"\u299A",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",Wedge:"\u22C0",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\u{1D51A}",wfr:"\u{1D534}",Wopf:"\u{1D54E}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\u{1D4B2}",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",Xfr:"\u{1D51B}",xfr:"\u{1D535}",xharr:"\u27F7",xhArr:"\u27FA",Xi:"\u039E",xi:"\u03BE",xlarr:"\u27F5",xlArr:"\u27F8",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",Xopf:"\u{1D54F}",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrarr:"\u27F6",xrArr:"\u27F9",Xscr:"\u{1D4B3}",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",Yacute:"\xDD",yacute:"\xFD",YAcy:"\u042F",yacy:"\u044F",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042B",ycy:"\u044B",yen:"\xA5",Yfr:"\u{1D51C}",yfr:"\u{1D536}",YIcy:"\u0407",yicy:"\u0457",Yopf:"\u{1D550}",yopf:"\u{1D56A}",Yscr:"\u{1D4B4}",yscr:"\u{1D4CE}",YUcy:"\u042E",yucy:"\u044E",yuml:"\xFF",Yuml:"\u0178",Zacute:"\u0179",zacute:"\u017A",Zcaron:"\u017D",zcaron:"\u017E",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017B",zdot:"\u017C",zeetrf:"\u2128",ZeroWidthSpace:"\u200B",Zeta:"\u0396",zeta:"\u03B6",zfr:"\u{1D537}",Zfr:"\u2128",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",Zopf:"\u2124",Zscr:"\u{1D4B5}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"}});var Sr=b((Zl,Ht)=>{"use strict";Ht.exports=Ut()});var Je=b((Yl,Gt)=>{Gt.exports=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061E\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166D\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4E\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDF55-\uDF59]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDF3C-\uDF3E]|\uD806[\uDC3B\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8]|\uD809[\uDC70-\uDC74]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/});var Wt=b((Xl,jt)=>{"use strict";var Vt={};function Fs(t){var e,r,n=Vt[t];if(n)return n;for(n=Vt[t]=[],e=0;e<128;e++)r=String.fromCharCode(e),/^[0-9a-z]$/i.test(r)?n.push(r):n.push("%"+("0"+e.toString(16).toUpperCase()).slice(-2));for(e=0;e<t.length;e++)n[t.charCodeAt(e)]=t[e];return n}function Ke(t,e,r){var n,i,o,a,s,l="";for(typeof e!="string"&&(r=e,e=Ke.defaultChars),typeof r>"u"&&(r=!0),s=Fs(e),n=0,i=t.length;n<i;n++){if(o=t.charCodeAt(n),r&&o===37&&n+2<i&&/^[0-9a-f]{2}$/i.test(t.slice(n+1,n+3))){l+=t.slice(n,n+3),n+=2;continue}if(o<128){l+=s[o];continue}if(o>=55296&&o<=57343){if(o>=55296&&o<=56319&&n+1<i&&(a=t.charCodeAt(n+1),a>=56320&&a<=57343)){l+=encodeURIComponent(t[n]+t[n+1]),n++;continue}l+="%EF%BF%BD";continue}l+=encodeURIComponent(t[n])}return l}Ke.defaultChars=";/?:@&=+$,-_.!~*'()#";Ke.componentChars="-_.!~*'()";jt.exports=Ke});var Yt=b((Jl,Zt)=>{"use strict";var $t={};function Rs(t){var e,r,n=$t[t];if(n)return n;for(n=$t[t]=[],e=0;e<128;e++)r=String.fromCharCode(e),n.push(r);for(e=0;e<t.length;e++)r=t.charCodeAt(e),n[r]="%"+("0"+r.toString(16).toUpperCase()).slice(-2);return n}function Qe(t,e){var r;return typeof e!="string"&&(e=Qe.defaultChars),r=Rs(e),t.replace(/(%[a-f0-9]{2})+/gi,function(n){var i,o,a,s,l,c,f,p="";for(i=0,o=n.length;i<o;i+=3){if(a=parseInt(n.slice(i+1,i+3),16),a<128){p+=r[a];continue}if((a&224)===192&&i+3<o&&(s=parseInt(n.slice(i+4,i+6),16),(s&192)===128)){f=a<<6&1984|s&63,f<128?p+="\uFFFD\uFFFD":p+=String.fromCharCode(f),i+=3;continue}if((a&240)===224&&i+6<o&&(s=parseInt(n.slice(i+4,i+6),16),l=parseInt(n.slice(i+7,i+9),16),(s&192)===128&&(l&192)===128)){f=a<<12&61440|s<<6&4032|l&63,f<2048||f>=55296&&f<=57343?p+="\uFFFD\uFFFD\uFFFD":p+=String.fromCharCode(f),i+=6;continue}if((a&248)===240&&i+9<o&&(s=parseInt(n.slice(i+4,i+6),16),l=parseInt(n.slice(i+7,i+9),16),c=parseInt(n.slice(i+10,i+12),16),(s&192)===128&&(l&192)===128&&(c&192)===128)){f=a<<18&1835008|s<<12&258048|l<<6&4032|c&63,f<65536||f>1114111?p+="\uFFFD\uFFFD\uFFFD\uFFFD":(f-=65536,p+=String.fromCharCode(55296+(f>>10),56320+(f&1023))),i+=9;continue}p+="\uFFFD"}return p})}Qe.defaultChars=";/?:@&=+$,#";Qe.componentChars="";Zt.exports=Qe});var Jt=b((Kl,Xt)=>{"use strict";Xt.exports=function(e){var r="";return r+=e.protocol||"",r+=e.slashes?"//":"",r+=e.auth?e.auth+"@":"",e.hostname&&e.hostname.indexOf(":")!==-1?r+="["+e.hostname+"]":r+=e.hostname||"",r+=e.port?":"+e.port:"",r+=e.pathname||"",r+=e.search||"",r+=e.hash||"",r}});var on=b((Ql,nn)=>{"use strict";function er(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}var Is=/^([a-z0-9.+-]+:)/i,Ms=/:[0-9]*$/,Ls=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,Os=["<",">",'"',"`"," ","\r",`
`,"	"],Ns=["{","}","|","\\","^","`"].concat(Os),zs=["'"].concat(Ns),Kt=["%","/","?",";","#"].concat(zs),Qt=["/","?","#"],Ps=255,en=/^[+a-z0-9A-Z_-]{0,63}$/,Bs=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,rn={javascript:!0,"javascript:":!0},tn={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function Us(t,e){if(t&&t instanceof er)return t;var r=new er;return r.parse(t,e),r}er.prototype.parse=function(t,e){var r,n,i,o,a,s=t;if(s=s.trim(),!e&&t.split("#").length===1){var l=Ls.exec(s);if(l)return this.pathname=l[1],l[2]&&(this.search=l[2]),this}var c=Is.exec(s);if(c&&(c=c[0],i=c.toLowerCase(),this.protocol=c,s=s.substr(c.length)),(e||c||s.match(/^\/\/[^@\/]+@[^@\/]+/))&&(a=s.substr(0,2)==="//",a&&!(c&&rn[c])&&(s=s.substr(2),this.slashes=!0)),!rn[c]&&(a||c&&!tn[c])){var f=-1;for(r=0;r<Qt.length;r++)o=s.indexOf(Qt[r]),o!==-1&&(f===-1||o<f)&&(f=o);var p,d;for(f===-1?d=s.lastIndexOf("@"):d=s.lastIndexOf("@",f),d!==-1&&(p=s.slice(0,d),s=s.slice(d+1),this.auth=p),f=-1,r=0;r<Kt.length;r++)o=s.indexOf(Kt[r]),o!==-1&&(f===-1||o<f)&&(f=o);f===-1&&(f=s.length),s[f-1]===":"&&f--;var h=s.slice(0,f);s=s.slice(f),this.parseHost(h),this.hostname=this.hostname||"";var g=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!g){var k=this.hostname.split(/\./);for(r=0,n=k.length;r<n;r++){var w=k[r];if(w&&!w.match(en)){for(var C="",_=0,y=w.length;_<y;_++)w.charCodeAt(_)>127?C+="x":C+=w[_];if(!C.match(en)){var v=k.slice(0,r),D=k.slice(r+1),x=w.match(Bs);x&&(v.push(x[1]),D.unshift(x[2])),D.length&&(s=D.join(".")+s),this.hostname=v.join(".");break}}}}this.hostname.length>Ps&&(this.hostname=""),g&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}var R=s.indexOf("#");R!==-1&&(this.hash=s.substr(R),s=s.slice(0,R));var L=s.indexOf("?");return L!==-1&&(this.search=s.substr(L),s=s.slice(0,L)),s&&(this.pathname=s),tn[i]&&this.hostname&&!this.pathname&&(this.pathname=""),this};er.prototype.parseHost=function(t){var e=Ms.exec(t);e&&(e=e[0],e!==":"&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)};nn.exports=Us});var qr=b((ec,Ne)=>{"use strict";Ne.exports.encode=Wt();Ne.exports.decode=Yt();Ne.exports.format=Jt();Ne.exports.parse=on()});var Fr=b((rc,sn)=>{sn.exports=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/});var Rr=b((tc,an)=>{an.exports=/[\0-\x1F\x7F-\x9F]/});var cn=b((nc,ln)=>{ln.exports=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/});var Ir=b((ic,un)=>{un.exports=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/});var fn=b(Ae=>{"use strict";Ae.Any=Fr();Ae.Cc=Rr();Ae.Cf=cn();Ae.P=Je();Ae.Z=Ir()});var F=b(O=>{"use strict";function Hs(t){return Object.prototype.toString.call(t)}function Gs(t){return Hs(t)==="[object String]"}var Vs=Object.prototype.hasOwnProperty;function hn(t,e){return Vs.call(t,e)}function js(t){var e=Array.prototype.slice.call(arguments,1);return e.forEach(function(r){if(r){if(typeof r!="object")throw new TypeError(r+"must be object");Object.keys(r).forEach(function(n){t[n]=r[n]})}}),t}function Ws(t,e,r){return[].concat(t.slice(0,e),r,t.slice(e+1))}function dn(t){return!(t>=55296&&t<=57343||t>=64976&&t<=65007||(t&65535)===65535||(t&65535)===65534||t>=0&&t<=8||t===11||t>=14&&t<=31||t>=127&&t<=159||t>1114111)}function mn(t){if(t>65535){t-=65536;var e=55296+(t>>10),r=56320+(t&1023);return String.fromCharCode(e,r)}return String.fromCharCode(t)}var gn=/\\([!"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~])/g,$s=/&([a-z#][a-z0-9]{1,31});/gi,Zs=new RegExp(gn.source+"|"+$s.source,"gi"),Ys=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i,pn=Sr();function Xs(t,e){var r=0;return hn(pn,e)?pn[e]:e.charCodeAt(0)===35&&Ys.test(e)&&(r=e[1].toLowerCase()==="x"?parseInt(e.slice(2),16):parseInt(e.slice(1),10),dn(r))?mn(r):t}function Js(t){return t.indexOf("\\")<0?t:t.replace(gn,"$1")}function Ks(t){return t.indexOf("\\")<0&&t.indexOf("&")<0?t:t.replace(Zs,function(e,r,n){return r||Xs(e,n)})}var Qs=/[&<>"]/,ea=/[&<>"]/g,ra={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function ta(t){return ra[t]}function na(t){return Qs.test(t)?t.replace(ea,ta):t}var ia=/[.?*+^$[\]\\(){}|-]/g;function oa(t){return t.replace(ia,"\\$&")}function sa(t){switch(t){case 9:case 32:return!0}return!1}function aa(t){if(t>=8192&&t<=8202)return!0;switch(t){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}var la=Je();function ca(t){return la.test(t)}function ua(t){switch(t){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function fa(t){return t=t.trim().replace(/\s+/g," "),"\u1E9E".toLowerCase()==="\u1E7E"&&(t=t.replace(/ẞ/g,"\xDF")),t.toLowerCase().toUpperCase()}O.lib={};O.lib.mdurl=qr();O.lib.ucmicro=fn();O.assign=js;O.isString=Gs;O.has=hn;O.unescapeMd=Js;O.unescapeAll=Ks;O.isValidEntityCode=dn;O.fromCodePoint=mn;O.escapeHtml=na;O.arrayReplaceAt=Ws;O.isSpace=sa;O.isWhiteSpace=aa;O.isMdAsciiPunct=ua;O.isPunctChar=ca;O.escapeRE=oa;O.normalizeReference=fa});var xn=b((ac,_n)=>{"use strict";_n.exports=function(e,r,n){var i,o,a,s,l=-1,c=e.posMax,f=e.pos;for(e.pos=r+1,i=1;e.pos<c;){if(a=e.src.charCodeAt(e.pos),a===93&&(i--,i===0)){o=!0;break}if(s=e.pos,e.md.inline.skipToken(e),a===91){if(s===e.pos-1)i++;else if(n)return e.pos=f,-1}}return o&&(l=e.pos),e.pos=f,l}});var An=b((lc,kn)=>{"use strict";var bn=F().unescapeAll;kn.exports=function(e,r,n){var i,o,a=0,s=r,l={ok:!1,pos:0,lines:0,str:""};if(e.charCodeAt(r)===60){for(r++;r<n;){if(i=e.charCodeAt(r),i===10||i===60)return l;if(i===62)return l.pos=r+1,l.str=bn(e.slice(s+1,r)),l.ok=!0,l;if(i===92&&r+1<n){r+=2;continue}r++}return l}for(o=0;r<n&&(i=e.charCodeAt(r),!(i===32||i<32||i===127));){if(i===92&&r+1<n){if(e.charCodeAt(r+1)===32)break;r+=2;continue}if(i===40&&(o++,o>32))return l;if(i===41){if(o===0)break;o--}r++}return s===r||o!==0||(l.str=bn(e.slice(s,r)),l.lines=a,l.pos=r,l.ok=!0),l}});var vn=b((cc,Cn)=>{"use strict";var pa=F().unescapeAll;Cn.exports=function(e,r,n){var i,o,a=0,s=r,l={ok:!1,pos:0,lines:0,str:""};if(r>=n||(o=e.charCodeAt(r),o!==34&&o!==39&&o!==40))return l;for(r++,o===40&&(o=41);r<n;){if(i=e.charCodeAt(r),i===o)return l.pos=r+1,l.lines=a,l.str=pa(e.slice(s+1,r)),l.ok=!0,l;if(i===40&&o===41)return l;i===10?a++:i===92&&r+1<n&&(r++,e.charCodeAt(r)===10&&a++),r++}return l}});var En=b(rr=>{"use strict";rr.parseLinkLabel=xn();rr.parseLinkDestination=An();rr.parseLinkTitle=vn()});var wn=b((fc,yn)=>{"use strict";var ha=F().assign,da=F().unescapeAll,pe=F().escapeHtml,re={};re.code_inline=function(t,e,r,n,i){var o=t[e];return"<code"+i.renderAttrs(o)+">"+pe(t[e].content)+"</code>"};re.code_block=function(t,e,r,n,i){var o=t[e];return"<pre"+i.renderAttrs(o)+"><code>"+pe(t[e].content)+`</code></pre>
`};re.fence=function(t,e,r,n,i){var o=t[e],a=o.info?da(o.info).trim():"",s="",l="",c,f,p,d,h;return a&&(p=a.split(/(\s+)/g),s=p[0],l=p.slice(2).join("")),r.highlight?c=r.highlight(o.content,s,l)||pe(o.content):c=pe(o.content),c.indexOf("<pre")===0?c+`
`:a?(f=o.attrIndex("class"),d=o.attrs?o.attrs.slice():[],f<0?d.push(["class",r.langPrefix+s]):(d[f]=d[f].slice(),d[f][1]+=" "+r.langPrefix+s),h={attrs:d},"<pre><code"+i.renderAttrs(h)+">"+c+`</code></pre>
`):"<pre><code"+i.renderAttrs(o)+">"+c+`</code></pre>
`};re.image=function(t,e,r,n,i){var o=t[e];return o.attrs[o.attrIndex("alt")][1]=i.renderInlineAsText(o.children,r,n),i.renderToken(t,e,r)};re.hardbreak=function(t,e,r){return r.xhtmlOut?`<br />
`:`<br>
`};re.softbreak=function(t,e,r){return r.breaks?r.xhtmlOut?`<br />
`:`<br>
`:`
`};re.text=function(t,e){return pe(t[e].content)};re.html_block=function(t,e){return t[e].content};re.html_inline=function(t,e){return t[e].content};function Ce(){this.rules=ha({},re)}Ce.prototype.renderAttrs=function(e){var r,n,i;if(!e.attrs)return"";for(i="",r=0,n=e.attrs.length;r<n;r++)i+=" "+pe(e.attrs[r][0])+'="'+pe(e.attrs[r][1])+'"';return i};Ce.prototype.renderToken=function(e,r,n){var i,o="",a=!1,s=e[r];return s.hidden?"":(s.block&&s.nesting!==-1&&r&&e[r-1].hidden&&(o+=`
`),o+=(s.nesting===-1?"</":"<")+s.tag,o+=this.renderAttrs(s),s.nesting===0&&n.xhtmlOut&&(o+=" /"),s.block&&(a=!0,s.nesting===1&&r+1<e.length&&(i=e[r+1],(i.type==="inline"||i.hidden||i.nesting===-1&&i.tag===s.tag)&&(a=!1))),o+=a?`>
`:">",o)};Ce.prototype.renderInline=function(t,e,r){for(var n,i="",o=this.rules,a=0,s=t.length;a<s;a++)n=t[a].type,typeof o[n]<"u"?i+=o[n](t,a,e,r,this):i+=this.renderToken(t,a,e);return i};Ce.prototype.renderInlineAsText=function(t,e,r){for(var n="",i=0,o=t.length;i<o;i++)t[i].type==="text"?n+=t[i].content:t[i].type==="image"?n+=this.renderInlineAsText(t[i].children,e,r):t[i].type==="softbreak"&&(n+=`
`);return n};Ce.prototype.render=function(t,e,r){var n,i,o,a="",s=this.rules;for(n=0,i=t.length;n<i;n++)o=t[n].type,o==="inline"?a+=this.renderInline(t[n].children,e,r):typeof s[o]<"u"?a+=s[t[n].type](t,n,e,r,this):a+=this.renderToken(t,n,e,r);return a};yn.exports=Ce});var tr=b((pc,Dn)=>{"use strict";function K(){this.__rules__=[],this.__cache__=null}K.prototype.__find__=function(t){for(var e=0;e<this.__rules__.length;e++)if(this.__rules__[e].name===t)return e;return-1};K.prototype.__compile__=function(){var t=this,e=[""];t.__rules__.forEach(function(r){r.enabled&&r.alt.forEach(function(n){e.indexOf(n)<0&&e.push(n)})}),t.__cache__={},e.forEach(function(r){t.__cache__[r]=[],t.__rules__.forEach(function(n){n.enabled&&(r&&n.alt.indexOf(r)<0||t.__cache__[r].push(n.fn))})})};K.prototype.at=function(t,e,r){var n=this.__find__(t),i=r||{};if(n===-1)throw new Error("Parser rule not found: "+t);this.__rules__[n].fn=e,this.__rules__[n].alt=i.alt||[],this.__cache__=null};K.prototype.before=function(t,e,r,n){var i=this.__find__(t),o=n||{};if(i===-1)throw new Error("Parser rule not found: "+t);this.__rules__.splice(i,0,{name:e,enabled:!0,fn:r,alt:o.alt||[]}),this.__cache__=null};K.prototype.after=function(t,e,r,n){var i=this.__find__(t),o=n||{};if(i===-1)throw new Error("Parser rule not found: "+t);this.__rules__.splice(i+1,0,{name:e,enabled:!0,fn:r,alt:o.alt||[]}),this.__cache__=null};K.prototype.push=function(t,e,r){var n=r||{};this.__rules__.push({name:t,enabled:!0,fn:e,alt:n.alt||[]}),this.__cache__=null};K.prototype.enable=function(t,e){Array.isArray(t)||(t=[t]);var r=[];return t.forEach(function(n){var i=this.__find__(n);if(i<0){if(e)return;throw new Error("Rules manager: invalid rule name "+n)}this.__rules__[i].enabled=!0,r.push(n)},this),this.__cache__=null,r};K.prototype.enableOnly=function(t,e){Array.isArray(t)||(t=[t]),this.__rules__.forEach(function(r){r.enabled=!1}),this.enable(t,e)};K.prototype.disable=function(t,e){Array.isArray(t)||(t=[t]);var r=[];return t.forEach(function(n){var i=this.__find__(n);if(i<0){if(e)return;throw new Error("Rules manager: invalid rule name "+n)}this.__rules__[i].enabled=!1,r.push(n)},this),this.__cache__=null,r};K.prototype.getRules=function(t){return this.__cache__===null&&this.__compile__(),this.__cache__[t]||[]};Dn.exports=K});var Sn=b((hc,Tn)=>{"use strict";var ma=/\r\n?|\n/g,ga=/\0/g;Tn.exports=function(e){var r;r=e.src.replace(ma,`
`),r=r.replace(ga,"\uFFFD"),e.src=r}});var Fn=b((dc,qn)=>{"use strict";qn.exports=function(e){var r;e.inlineMode?(r=new e.Token("inline","",0),r.content=e.src,r.map=[0,1],r.children=[],e.tokens.push(r)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}});var In=b((mc,Rn)=>{"use strict";Rn.exports=function(e){var r=e.tokens,n,i,o;for(i=0,o=r.length;i<o;i++)n=r[i],n.type==="inline"&&e.md.inline.parse(n.content,e.md,e.env,n.children)}});var Ln=b((gc,Mn)=>{"use strict";var _a=F().arrayReplaceAt;function xa(t){return/^<a[>\s]/i.test(t)}function ba(t){return/^<\/a\s*>/i.test(t)}Mn.exports=function(e){var r,n,i,o,a,s,l,c,f,p,d,h,g,k,w,C,_=e.tokens,y;if(e.md.options.linkify){for(n=0,i=_.length;n<i;n++)if(!(_[n].type!=="inline"||!e.md.linkify.pretest(_[n].content)))for(o=_[n].children,g=0,r=o.length-1;r>=0;r--){if(s=o[r],s.type==="link_close"){for(r--;o[r].level!==s.level&&o[r].type!=="link_open";)r--;continue}if(s.type==="html_inline"&&(xa(s.content)&&g>0&&g--,ba(s.content)&&g++),!(g>0)&&s.type==="text"&&e.md.linkify.test(s.content)){for(f=s.content,y=e.md.linkify.match(f),l=[],h=s.level,d=0,c=0;c<y.length;c++)k=y[c].url,w=e.md.normalizeLink(k),e.md.validateLink(w)&&(C=y[c].text,y[c].schema?y[c].schema==="mailto:"&&!/^mailto:/i.test(C)?C=e.md.normalizeLinkText("mailto:"+C).replace(/^mailto:/,""):C=e.md.normalizeLinkText(C):C=e.md.normalizeLinkText("http://"+C).replace(/^http:\/\//,""),p=y[c].index,p>d&&(a=new e.Token("text","",0),a.content=f.slice(d,p),a.level=h,l.push(a)),a=new e.Token("link_open","a",1),a.attrs=[["href",w]],a.level=h++,a.markup="linkify",a.info="auto",l.push(a),a=new e.Token("text","",0),a.content=C,a.level=h,l.push(a),a=new e.Token("link_close","a",-1),a.level=--h,a.markup="linkify",a.info="auto",l.push(a),d=y[c].lastIndex);d<f.length&&(a=new e.Token("text","",0),a.content=f.slice(d),a.level=h,l.push(a)),_[n].children=o=_a(o,r,l)}}}}});var zn=b((_c,Nn)=>{"use strict";var On=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,ka=/\((c|tm|r|p)\)/i,Aa=/\((c|tm|r|p)\)/ig,Ca={c:"\xA9",r:"\xAE",p:"\xA7",tm:"\u2122"};function va(t,e){return Ca[e.toLowerCase()]}function Ea(t){var e,r,n=0;for(e=t.length-1;e>=0;e--)r=t[e],r.type==="text"&&!n&&(r.content=r.content.replace(Aa,va)),r.type==="link_open"&&r.info==="auto"&&n--,r.type==="link_close"&&r.info==="auto"&&n++}function ya(t){var e,r,n=0;for(e=t.length-1;e>=0;e--)r=t[e],r.type==="text"&&!n&&On.test(r.content)&&(r.content=r.content.replace(/\+-/g,"\xB1").replace(/\.{2,}/g,"\u2026").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/mg,"$1\u2014").replace(/(^|\s)--(?=\s|$)/mg,"$1\u2013").replace(/(^|[^-\s])--(?=[^-\s]|$)/mg,"$1\u2013")),r.type==="link_open"&&r.info==="auto"&&n--,r.type==="link_close"&&r.info==="auto"&&n++}Nn.exports=function(e){var r;if(e.md.options.typographer)for(r=e.tokens.length-1;r>=0;r--)e.tokens[r].type==="inline"&&(ka.test(e.tokens[r].content)&&Ea(e.tokens[r].children),On.test(e.tokens[r].content)&&ya(e.tokens[r].children))}});var jn=b((xc,Vn)=>{"use strict";var Pn=F().isWhiteSpace,Bn=F().isPunctChar,Un=F().isMdAsciiPunct,wa=/['"]/,Hn=/['"]/g,Gn="\u2019";function nr(t,e,r){return t.substr(0,e)+r+t.substr(e+1)}function Da(t,e){var r,n,i,o,a,s,l,c,f,p,d,h,g,k,w,C,_,y,v,D,x;for(v=[],r=0;r<t.length;r++){for(n=t[r],l=t[r].level,_=v.length-1;_>=0&&!(v[_].level<=l);_--);if(v.length=_+1,n.type==="text"){i=n.content,a=0,s=i.length;e:for(;a<s&&(Hn.lastIndex=a,o=Hn.exec(i),!!o);){if(w=C=!0,a=o.index+1,y=o[0]==="'",f=32,o.index-1>=0)f=i.charCodeAt(o.index-1);else for(_=r-1;_>=0&&!(t[_].type==="softbreak"||t[_].type==="hardbreak");_--)if(t[_].content){f=t[_].content.charCodeAt(t[_].content.length-1);break}if(p=32,a<s)p=i.charCodeAt(a);else for(_=r+1;_<t.length&&!(t[_].type==="softbreak"||t[_].type==="hardbreak");_++)if(t[_].content){p=t[_].content.charCodeAt(0);break}if(d=Un(f)||Bn(String.fromCharCode(f)),h=Un(p)||Bn(String.fromCharCode(p)),g=Pn(f),k=Pn(p),k?w=!1:h&&(g||d||(w=!1)),g?C=!1:d&&(k||h||(C=!1)),p===34&&o[0]==='"'&&f>=48&&f<=57&&(C=w=!1),w&&C&&(w=d,C=h),!w&&!C){y&&(n.content=nr(n.content,o.index,Gn));continue}if(C){for(_=v.length-1;_>=0&&(c=v[_],!(v[_].level<l));_--)if(c.single===y&&v[_].level===l){c=v[_],y?(D=e.md.options.quotes[2],x=e.md.options.quotes[3]):(D=e.md.options.quotes[0],x=e.md.options.quotes[1]),n.content=nr(n.content,o.index,x),t[c.token].content=nr(t[c.token].content,c.pos,D),a+=x.length-1,c.token===r&&(a+=D.length-1),i=n.content,s=i.length,v.length=_;continue e}}w?v.push({token:r,pos:o.index,single:y,level:l}):C&&y&&(n.content=nr(n.content,o.index,Gn))}}}}Vn.exports=function(e){var r;if(e.md.options.typographer)for(r=e.tokens.length-1;r>=0;r--)e.tokens[r].type!=="inline"||!wa.test(e.tokens[r].content)||Da(e.tokens[r].children,e)}});var ir=b((bc,Wn)=>{"use strict";function ve(t,e,r){this.type=t,this.tag=e,this.attrs=null,this.map=null,this.nesting=r,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}ve.prototype.attrIndex=function(e){var r,n,i;if(!this.attrs)return-1;for(r=this.attrs,n=0,i=r.length;n<i;n++)if(r[n][0]===e)return n;return-1};ve.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]};ve.prototype.attrSet=function(e,r){var n=this.attrIndex(e),i=[e,r];n<0?this.attrPush(i):this.attrs[n]=i};ve.prototype.attrGet=function(e){var r=this.attrIndex(e),n=null;return r>=0&&(n=this.attrs[r][1]),n};ve.prototype.attrJoin=function(e,r){var n=this.attrIndex(e);n<0?this.attrPush([e,r]):this.attrs[n][1]=this.attrs[n][1]+" "+r};Wn.exports=ve});var Yn=b((kc,Zn)=>{"use strict";var Ta=ir();function $n(t,e,r){this.src=t,this.env=r,this.tokens=[],this.inlineMode=!1,this.md=e}$n.prototype.Token=Ta;Zn.exports=$n});var Jn=b((Ac,Xn)=>{"use strict";var Sa=tr(),Mr=[["normalize",Sn()],["block",Fn()],["inline",In()],["linkify",Ln()],["replacements",zn()],["smartquotes",jn()]];function Lr(){this.ruler=new Sa;for(var t=0;t<Mr.length;t++)this.ruler.push(Mr[t][0],Mr[t][1])}Lr.prototype.process=function(t){var e,r,n;for(n=this.ruler.getRules(""),e=0,r=n.length;e<r;e++)n[e](t)};Lr.prototype.State=Yn();Xn.exports=Lr});var ei=b((Cc,Qn)=>{"use strict";var Or=F().isSpace;function Nr(t,e){var r=t.bMarks[e]+t.tShift[e],n=t.eMarks[e];return t.src.substr(r,n-r)}function Kn(t){var e=[],r=0,n=t.length,i,o=!1,a=0,s="";for(i=t.charCodeAt(r);r<n;)i===124&&(o?(s+=t.substring(a,r-1),a=r):(e.push(s+t.substring(a,r)),s="",a=r+1)),o=i===92,r++,i=t.charCodeAt(r);return e.push(s+t.substring(a)),e}Qn.exports=function(e,r,n,i){var o,a,s,l,c,f,p,d,h,g,k,w,C,_,y,v,D,x;if(r+2>n||(f=r+1,e.sCount[f]<e.blkIndent)||e.sCount[f]-e.blkIndent>=4||(s=e.bMarks[f]+e.tShift[f],s>=e.eMarks[f])||(D=e.src.charCodeAt(s++),D!==124&&D!==45&&D!==58)||s>=e.eMarks[f]||(x=e.src.charCodeAt(s++),x!==124&&x!==45&&x!==58&&!Or(x))||D===45&&Or(x))return!1;for(;s<e.eMarks[f];){if(o=e.src.charCodeAt(s),o!==124&&o!==45&&o!==58&&!Or(o))return!1;s++}for(a=Nr(e,r+1),p=a.split("|"),g=[],l=0;l<p.length;l++){if(k=p[l].trim(),!k){if(l===0||l===p.length-1)continue;return!1}if(!/^:?-+:?$/.test(k))return!1;k.charCodeAt(k.length-1)===58?g.push(k.charCodeAt(0)===58?"center":"right"):k.charCodeAt(0)===58?g.push("left"):g.push("")}if(a=Nr(e,r).trim(),a.indexOf("|")===-1||e.sCount[r]-e.blkIndent>=4||(p=Kn(a),p.length&&p[0]===""&&p.shift(),p.length&&p[p.length-1]===""&&p.pop(),d=p.length,d===0||d!==g.length))return!1;if(i)return!0;for(_=e.parentType,e.parentType="table",v=e.md.block.ruler.getRules("blockquote"),h=e.push("table_open","table",1),h.map=w=[r,0],h=e.push("thead_open","thead",1),h.map=[r,r+1],h=e.push("tr_open","tr",1),h.map=[r,r+1],l=0;l<p.length;l++)h=e.push("th_open","th",1),g[l]&&(h.attrs=[["style","text-align:"+g[l]]]),h=e.push("inline","",0),h.content=p[l].trim(),h.children=[],h=e.push("th_close","th",-1);for(h=e.push("tr_close","tr",-1),h=e.push("thead_close","thead",-1),f=r+2;f<n&&!(e.sCount[f]<e.blkIndent);f++){for(y=!1,l=0,c=v.length;l<c;l++)if(v[l](e,f,n,!0)){y=!0;break}if(y||(a=Nr(e,f).trim(),!a)||e.sCount[f]-e.blkIndent>=4)break;for(p=Kn(a),p.length&&p[0]===""&&p.shift(),p.length&&p[p.length-1]===""&&p.pop(),f===r+2&&(h=e.push("tbody_open","tbody",1),h.map=C=[r+2,0]),h=e.push("tr_open","tr",1),h.map=[f,f+1],l=0;l<d;l++)h=e.push("td_open","td",1),g[l]&&(h.attrs=[["style","text-align:"+g[l]]]),h=e.push("inline","",0),h.content=p[l]?p[l].trim():"",h.children=[],h=e.push("td_close","td",-1);h=e.push("tr_close","tr",-1)}return C&&(h=e.push("tbody_close","tbody",-1),C[1]=f),h=e.push("table_close","table",-1),w[1]=f,e.parentType=_,e.line=f,!0}});var ti=b((vc,ri)=>{"use strict";ri.exports=function(e,r,n){var i,o,a;if(e.sCount[r]-e.blkIndent<4)return!1;for(o=i=r+1;i<n;){if(e.isEmpty(i)){i++;continue}if(e.sCount[i]-e.blkIndent>=4){i++,o=i;continue}break}return e.line=o,a=e.push("code_block","code",0),a.content=e.getLines(r,o,4+e.blkIndent,!1)+`
`,a.map=[r,e.line],!0}});var ii=b((Ec,ni)=>{"use strict";ni.exports=function(e,r,n,i){var o,a,s,l,c,f,p,d=!1,h=e.bMarks[r]+e.tShift[r],g=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4||h+3>g||(o=e.src.charCodeAt(h),o!==126&&o!==96)||(c=h,h=e.skipChars(h,o),a=h-c,a<3)||(p=e.src.slice(c,h),s=e.src.slice(h,g),o===96&&s.indexOf(String.fromCharCode(o))>=0))return!1;if(i)return!0;for(l=r;l++,!(l>=n||(h=c=e.bMarks[l]+e.tShift[l],g=e.eMarks[l],h<g&&e.sCount[l]<e.blkIndent));)if(e.src.charCodeAt(h)===o&&!(e.sCount[l]-e.blkIndent>=4)&&(h=e.skipChars(h,o),!(h-c<a)&&(h=e.skipSpaces(h),!(h<g)))){d=!0;break}return a=e.sCount[r],e.line=l+(d?1:0),f=e.push("fence","code",0),f.info=s,f.content=e.getLines(r+1,l,a,!0),f.markup=p,f.map=[r,e.line],!0}});var ai=b((yc,si)=>{"use strict";var oi=F().isSpace;si.exports=function(e,r,n,i){var o,a,s,l,c,f,p,d,h,g,k,w,C,_,y,v,D,x,R,L,de=e.lineMax,q=e.bMarks[r]+e.tShift[r],S=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4||e.src.charCodeAt(q++)!==62)return!1;if(i)return!0;for(l=h=e.sCount[r]+1,e.src.charCodeAt(q)===32?(q++,l++,h++,o=!1,v=!0):e.src.charCodeAt(q)===9?(v=!0,(e.bsCount[r]+h)%4===3?(q++,l++,h++,o=!1):o=!0):v=!1,g=[e.bMarks[r]],e.bMarks[r]=q;q<S&&(a=e.src.charCodeAt(q),oi(a));){a===9?h+=4-(h+e.bsCount[r]+(o?1:0))%4:h++;q++}for(k=[e.bsCount[r]],e.bsCount[r]=e.sCount[r]+1+(v?1:0),f=q>=S,_=[e.sCount[r]],e.sCount[r]=h-l,y=[e.tShift[r]],e.tShift[r]=q-e.bMarks[r],x=e.md.block.ruler.getRules("blockquote"),C=e.parentType,e.parentType="blockquote",d=r+1;d<n&&(L=e.sCount[d]<e.blkIndent,q=e.bMarks[d]+e.tShift[d],S=e.eMarks[d],!(q>=S));d++){if(e.src.charCodeAt(q++)===62&&!L){for(l=h=e.sCount[d]+1,e.src.charCodeAt(q)===32?(q++,l++,h++,o=!1,v=!0):e.src.charCodeAt(q)===9?(v=!0,(e.bsCount[d]+h)%4===3?(q++,l++,h++,o=!1):o=!0):v=!1,g.push(e.bMarks[d]),e.bMarks[d]=q;q<S&&(a=e.src.charCodeAt(q),oi(a));){a===9?h+=4-(h+e.bsCount[d]+(o?1:0))%4:h++;q++}f=q>=S,k.push(e.bsCount[d]),e.bsCount[d]=e.sCount[d]+1+(v?1:0),_.push(e.sCount[d]),e.sCount[d]=h-l,y.push(e.tShift[d]),e.tShift[d]=q-e.bMarks[d];continue}if(f)break;for(D=!1,s=0,c=x.length;s<c;s++)if(x[s](e,d,n,!0)){D=!0;break}if(D){e.lineMax=d,e.blkIndent!==0&&(g.push(e.bMarks[d]),k.push(e.bsCount[d]),y.push(e.tShift[d]),_.push(e.sCount[d]),e.sCount[d]-=e.blkIndent);break}g.push(e.bMarks[d]),k.push(e.bsCount[d]),y.push(e.tShift[d]),_.push(e.sCount[d]),e.sCount[d]=-1}for(w=e.blkIndent,e.blkIndent=0,R=e.push("blockquote_open","blockquote",1),R.markup=">",R.map=p=[r,0],e.md.block.tokenize(e,r,d),R=e.push("blockquote_close","blockquote",-1),R.markup=">",e.lineMax=de,e.parentType=C,p[1]=e.line,s=0;s<y.length;s++)e.bMarks[s+r]=g[s],e.tShift[s+r]=y[s],e.sCount[s+r]=_[s],e.bsCount[s+r]=k[s];return e.blkIndent=w,!0}});var ci=b((wc,li)=>{"use strict";var qa=F().isSpace;li.exports=function(e,r,n,i){var o,a,s,l,c=e.bMarks[r]+e.tShift[r],f=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4||(o=e.src.charCodeAt(c++),o!==42&&o!==45&&o!==95))return!1;for(a=1;c<f;){if(s=e.src.charCodeAt(c++),s!==o&&!qa(s))return!1;s===o&&a++}return a<3?!1:(i||(e.line=r+1,l=e.push("hr","hr",0),l.map=[r,e.line],l.markup=Array(a+1).join(String.fromCharCode(o))),!0)}});var di=b((Dc,hi)=>{"use strict";var pi=F().isSpace;function ui(t,e){var r,n,i,o;return n=t.bMarks[e]+t.tShift[e],i=t.eMarks[e],r=t.src.charCodeAt(n++),r!==42&&r!==45&&r!==43||n<i&&(o=t.src.charCodeAt(n),!pi(o))?-1:n}function fi(t,e){var r,n=t.bMarks[e]+t.tShift[e],i=n,o=t.eMarks[e];if(i+1>=o||(r=t.src.charCodeAt(i++),r<48||r>57))return-1;for(;;){if(i>=o)return-1;if(r=t.src.charCodeAt(i++),r>=48&&r<=57){if(i-n>=10)return-1;continue}if(r===41||r===46)break;return-1}return i<o&&(r=t.src.charCodeAt(i),!pi(r))?-1:i}function Fa(t,e){var r,n,i=t.level+2;for(r=e+2,n=t.tokens.length-2;r<n;r++)t.tokens[r].level===i&&t.tokens[r].type==="paragraph_open"&&(t.tokens[r+2].hidden=!0,t.tokens[r].hidden=!0,r+=2)}hi.exports=function(e,r,n,i){var o,a,s,l,c,f,p,d,h,g,k,w,C,_,y,v,D,x,R,L,de,q,S,le,se,ce,ye,U,we=!1,De=!0;if(e.sCount[r]-e.blkIndent>=4||e.listIndent>=0&&e.sCount[r]-e.listIndent>=4&&e.sCount[r]<e.blkIndent)return!1;if(i&&e.parentType==="paragraph"&&e.sCount[r]>=e.blkIndent&&(we=!0),(S=fi(e,r))>=0){if(p=!0,se=e.bMarks[r]+e.tShift[r],C=Number(e.src.slice(se,S-1)),we&&C!==1)return!1}else if((S=ui(e,r))>=0)p=!1;else return!1;if(we&&e.skipSpaces(S)>=e.eMarks[r])return!1;if(w=e.src.charCodeAt(S-1),i)return!0;for(k=e.tokens.length,p?(U=e.push("ordered_list_open","ol",1),C!==1&&(U.attrs=[["start",C]])):U=e.push("bullet_list_open","ul",1),U.map=g=[r,0],U.markup=String.fromCharCode(w),y=r,le=!1,ye=e.md.block.ruler.getRules("list"),x=e.parentType,e.parentType="list";y<n;){for(q=S,_=e.eMarks[y],f=v=e.sCount[y]+S-(e.bMarks[r]+e.tShift[r]);q<_;){if(o=e.src.charCodeAt(q),o===9)v+=4-(v+e.bsCount[y])%4;else if(o===32)v++;else break;q++}if(a=q,a>=_?c=1:c=v-f,c>4&&(c=1),l=f+c,U=e.push("list_item_open","li",1),U.markup=String.fromCharCode(w),U.map=d=[r,0],p&&(U.info=e.src.slice(se,S-1)),de=e.tight,L=e.tShift[r],R=e.sCount[r],D=e.listIndent,e.listIndent=e.blkIndent,e.blkIndent=l,e.tight=!0,e.tShift[r]=a-e.bMarks[r],e.sCount[r]=v,a>=_&&e.isEmpty(r+1)?e.line=Math.min(e.line+2,n):e.md.block.tokenize(e,r,n,!0),(!e.tight||le)&&(De=!1),le=e.line-r>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=D,e.tShift[r]=L,e.sCount[r]=R,e.tight=de,U=e.push("list_item_close","li",-1),U.markup=String.fromCharCode(w),y=r=e.line,d[1]=y,a=e.bMarks[r],y>=n||e.sCount[y]<e.blkIndent||e.sCount[r]-e.blkIndent>=4)break;for(ce=!1,s=0,h=ye.length;s<h;s++)if(ye[s](e,y,n,!0)){ce=!0;break}if(ce)break;if(p){if(S=fi(e,y),S<0)break;se=e.bMarks[y]+e.tShift[y]}else if(S=ui(e,y),S<0)break;if(w!==e.src.charCodeAt(S-1))break}return p?U=e.push("ordered_list_close","ol",-1):U=e.push("bullet_list_close","ul",-1),U.markup=String.fromCharCode(w),g[1]=y,e.line=y,e.parentType=x,De&&Fa(e,k),!0}});var gi=b((Tc,mi)=>{"use strict";var Ra=F().normalizeReference,or=F().isSpace;mi.exports=function(e,r,n,i){var o,a,s,l,c,f,p,d,h,g,k,w,C,_,y,v,D=0,x=e.bMarks[r]+e.tShift[r],R=e.eMarks[r],L=r+1;if(e.sCount[r]-e.blkIndent>=4||e.src.charCodeAt(x)!==91)return!1;for(;++x<R;)if(e.src.charCodeAt(x)===93&&e.src.charCodeAt(x-1)!==92){if(x+1===R||e.src.charCodeAt(x+1)!==58)return!1;break}for(l=e.lineMax,y=e.md.block.ruler.getRules("reference"),g=e.parentType,e.parentType="reference";L<l&&!e.isEmpty(L);L++)if(!(e.sCount[L]-e.blkIndent>3)&&!(e.sCount[L]<0)){for(_=!1,f=0,p=y.length;f<p;f++)if(y[f](e,L,l,!0)){_=!0;break}if(_)break}for(C=e.getLines(r,L,e.blkIndent,!1).trim(),R=C.length,x=1;x<R;x++){if(o=C.charCodeAt(x),o===91)return!1;if(o===93){h=x;break}else o===10?D++:o===92&&(x++,x<R&&C.charCodeAt(x)===10&&D++)}if(h<0||C.charCodeAt(h+1)!==58)return!1;for(x=h+2;x<R;x++)if(o=C.charCodeAt(x),o===10)D++;else if(!or(o))break;if(k=e.md.helpers.parseLinkDestination(C,x,R),!k.ok||(c=e.md.normalizeLink(k.str),!e.md.validateLink(c)))return!1;for(x=k.pos,D+=k.lines,a=x,s=D,w=x;x<R;x++)if(o=C.charCodeAt(x),o===10)D++;else if(!or(o))break;for(k=e.md.helpers.parseLinkTitle(C,x,R),x<R&&w!==x&&k.ok?(v=k.str,x=k.pos,D+=k.lines):(v="",x=a,D=s);x<R&&(o=C.charCodeAt(x),!!or(o));)x++;if(x<R&&C.charCodeAt(x)!==10&&v)for(v="",x=a,D=s;x<R&&(o=C.charCodeAt(x),!!or(o));)x++;return x<R&&C.charCodeAt(x)!==10||(d=Ra(C.slice(1,h)),!d)?!1:(i||(typeof e.env.references>"u"&&(e.env.references={}),typeof e.env.references[d]>"u"&&(e.env.references[d]={title:v,href:c}),e.parentType=g,e.line=r+D+1),!0)}});var xi=b((Sc,_i)=>{"use strict";_i.exports=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","section","source","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]});var Pr=b((qc,zr)=>{"use strict";var Ia="[a-zA-Z_:][a-zA-Z0-9:._-]*",Ma="[^\"'=<>`\\x00-\\x20]+",La="'[^']*'",Oa='"[^"]*"',Na="(?:"+Ma+"|"+La+"|"+Oa+")",za="(?:\\s+"+Ia+"(?:\\s*=\\s*"+Na+")?)",bi="<[A-Za-z][A-Za-z0-9\\-]*"+za+"*\\s*\\/?>",ki="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Pa="<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->",Ba="<[?][\\s\\S]*?[?]>",Ua="<![A-Z]+\\s+[^>]*>",Ha="<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",Ga=new RegExp("^(?:"+bi+"|"+ki+"|"+Pa+"|"+Ba+"|"+Ua+"|"+Ha+")"),Va=new RegExp("^(?:"+bi+"|"+ki+")");zr.exports.HTML_TAG_RE=Ga;zr.exports.HTML_OPEN_CLOSE_TAG_RE=Va});var Ci=b((Fc,Ai)=>{"use strict";var ja=xi(),Wa=Pr().HTML_OPEN_CLOSE_TAG_RE,Ee=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+ja.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(Wa.source+"\\s*$"),/^$/,!1]];Ai.exports=function(e,r,n,i){var o,a,s,l,c=e.bMarks[r]+e.tShift[r],f=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4||!e.md.options.html||e.src.charCodeAt(c)!==60)return!1;for(l=e.src.slice(c,f),o=0;o<Ee.length&&!Ee[o][0].test(l);o++);if(o===Ee.length)return!1;if(i)return Ee[o][2];if(a=r+1,!Ee[o][1].test(l)){for(;a<n&&!(e.sCount[a]<e.blkIndent);a++)if(c=e.bMarks[a]+e.tShift[a],f=e.eMarks[a],l=e.src.slice(c,f),Ee[o][1].test(l)){l.length!==0&&a++;break}}return e.line=a,s=e.push("html_block","",0),s.map=[r,a],s.content=e.getLines(r,a,e.blkIndent,!0),!0}});var yi=b((Rc,Ei)=>{"use strict";var vi=F().isSpace;Ei.exports=function(e,r,n,i){var o,a,s,l,c=e.bMarks[r]+e.tShift[r],f=e.eMarks[r];if(e.sCount[r]-e.blkIndent>=4||(o=e.src.charCodeAt(c),o!==35||c>=f))return!1;for(a=1,o=e.src.charCodeAt(++c);o===35&&c<f&&a<=6;)a++,o=e.src.charCodeAt(++c);return a>6||c<f&&!vi(o)?!1:(i||(f=e.skipSpacesBack(f,c),s=e.skipCharsBack(f,35,c),s>c&&vi(e.src.charCodeAt(s-1))&&(f=s),e.line=r+1,l=e.push("heading_open","h"+String(a),1),l.markup="########".slice(0,a),l.map=[r,e.line],l=e.push("inline","",0),l.content=e.src.slice(c,f).trim(),l.map=[r,e.line],l.children=[],l=e.push("heading_close","h"+String(a),-1),l.markup="########".slice(0,a)),!0)}});var Di=b((Ic,wi)=>{"use strict";wi.exports=function(e,r,n){var i,o,a,s,l,c,f,p,d,h=r+1,g,k=e.md.block.ruler.getRules("paragraph");if(e.sCount[r]-e.blkIndent>=4)return!1;for(g=e.parentType,e.parentType="paragraph";h<n&&!e.isEmpty(h);h++)if(!(e.sCount[h]-e.blkIndent>3)){if(e.sCount[h]>=e.blkIndent&&(c=e.bMarks[h]+e.tShift[h],f=e.eMarks[h],c<f&&(d=e.src.charCodeAt(c),(d===45||d===61)&&(c=e.skipChars(c,d),c=e.skipSpaces(c),c>=f)))){p=d===61?1:2;break}if(!(e.sCount[h]<0)){for(o=!1,a=0,s=k.length;a<s;a++)if(k[a](e,h,n,!0)){o=!0;break}if(o)break}}return p?(i=e.getLines(r,h,e.blkIndent,!1).trim(),e.line=h+1,l=e.push("heading_open","h"+String(p),1),l.markup=String.fromCharCode(d),l.map=[r,e.line],l=e.push("inline","",0),l.content=i,l.map=[r,e.line-1],l.children=[],l=e.push("heading_close","h"+String(p),-1),l.markup=String.fromCharCode(d),e.parentType=g,!0):!1}});var Si=b((Mc,Ti)=>{"use strict";Ti.exports=function(e,r){var n,i,o,a,s,l,c=r+1,f=e.md.block.ruler.getRules("paragraph"),p=e.lineMax;for(l=e.parentType,e.parentType="paragraph";c<p&&!e.isEmpty(c);c++)if(!(e.sCount[c]-e.blkIndent>3)&&!(e.sCount[c]<0)){for(i=!1,o=0,a=f.length;o<a;o++)if(f[o](e,c,p,!0)){i=!0;break}if(i)break}return n=e.getLines(r,c,e.blkIndent,!1).trim(),e.line=c,s=e.push("paragraph_open","p",1),s.map=[r,e.line],s=e.push("inline","",0),s.content=n,s.map=[r,e.line],s.children=[],s=e.push("paragraph_close","p",-1),e.parentType=l,!0}});var Ri=b((Lc,Fi)=>{"use strict";var qi=ir(),sr=F().isSpace;function te(t,e,r,n){var i,o,a,s,l,c,f,p;for(this.src=t,this.md=e,this.env=r,this.tokens=n,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0,this.result="",o=this.src,p=!1,a=s=c=f=0,l=o.length;s<l;s++){if(i=o.charCodeAt(s),!p)if(sr(i)){c++,i===9?f+=4-f%4:f++;continue}else p=!0;(i===10||s===l-1)&&(i!==10&&s++,this.bMarks.push(a),this.eMarks.push(s),this.tShift.push(c),this.sCount.push(f),this.bsCount.push(0),p=!1,c=0,f=0,a=s+1)}this.bMarks.push(o.length),this.eMarks.push(o.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}te.prototype.push=function(t,e,r){var n=new qi(t,e,r);return n.block=!0,r<0&&this.level--,n.level=this.level,r>0&&this.level++,this.tokens.push(n),n};te.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]};te.prototype.skipEmptyLines=function(e){for(var r=this.lineMax;e<r&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e};te.prototype.skipSpaces=function(e){for(var r,n=this.src.length;e<n&&(r=this.src.charCodeAt(e),!!sr(r));e++);return e};te.prototype.skipSpacesBack=function(e,r){if(e<=r)return e;for(;e>r;)if(!sr(this.src.charCodeAt(--e)))return e+1;return e};te.prototype.skipChars=function(e,r){for(var n=this.src.length;e<n&&this.src.charCodeAt(e)===r;e++);return e};te.prototype.skipCharsBack=function(e,r,n){if(e<=n)return e;for(;e>n;)if(r!==this.src.charCodeAt(--e))return e+1;return e};te.prototype.getLines=function(e,r,n,i){var o,a,s,l,c,f,p,d=e;if(e>=r)return"";for(f=new Array(r-e),o=0;d<r;d++,o++){for(a=0,p=l=this.bMarks[d],d+1<r||i?c=this.eMarks[d]+1:c=this.eMarks[d];l<c&&a<n;){if(s=this.src.charCodeAt(l),sr(s))s===9?a+=4-(a+this.bsCount[d])%4:a++;else if(l-p<this.tShift[d])a++;else break;l++}a>n?f[o]=new Array(a-n+1).join(" ")+this.src.slice(l,c):f[o]=this.src.slice(l,c)}return f.join("")};te.prototype.Token=qi;Fi.exports=te});var Mi=b((Oc,Ii)=>{"use strict";var $a=tr(),ar=[["table",ei(),["paragraph","reference"]],["code",ti()],["fence",ii(),["paragraph","reference","blockquote","list"]],["blockquote",ai(),["paragraph","reference","blockquote","list"]],["hr",ci(),["paragraph","reference","blockquote","list"]],["list",di(),["paragraph","reference","blockquote"]],["reference",gi()],["html_block",Ci(),["paragraph","reference","blockquote"]],["heading",yi(),["paragraph","reference","blockquote"]],["lheading",Di()],["paragraph",Si()]];function lr(){this.ruler=new $a;for(var t=0;t<ar.length;t++)this.ruler.push(ar[t][0],ar[t][1],{alt:(ar[t][2]||[]).slice()})}lr.prototype.tokenize=function(t,e,r){for(var n,i,o=this.ruler.getRules(""),a=o.length,s=e,l=!1,c=t.md.options.maxNesting;s<r&&(t.line=s=t.skipEmptyLines(s),!(s>=r||t.sCount[s]<t.blkIndent));){if(t.level>=c){t.line=r;break}for(i=0;i<a&&(n=o[i](t,s,r,!1),!n);i++);t.tight=!l,t.isEmpty(t.line-1)&&(l=!0),s=t.line,s<r&&t.isEmpty(s)&&(l=!0,s++,t.line=s)}};lr.prototype.parse=function(t,e,r,n){var i;t&&(i=new this.State(t,e,r,n),this.tokenize(i,i.line,i.lineMax))};lr.prototype.State=Ri();Ii.exports=lr});var Oi=b((Nc,Li)=>{"use strict";function Za(t){switch(t){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}Li.exports=function(e,r){for(var n=e.pos;n<e.posMax&&!Za(e.src.charCodeAt(n));)n++;return n===e.pos?!1:(r||(e.pending+=e.src.slice(e.pos,n)),e.pos=n,!0)}});var zi=b((zc,Ni)=>{"use strict";var Ya=F().isSpace;Ni.exports=function(e,r){var n,i,o,a=e.pos;if(e.src.charCodeAt(a)!==10)return!1;if(n=e.pending.length-1,i=e.posMax,!r)if(n>=0&&e.pending.charCodeAt(n)===32)if(n>=1&&e.pending.charCodeAt(n-1)===32){for(o=n-1;o>=1&&e.pending.charCodeAt(o-1)===32;)o--;e.pending=e.pending.slice(0,o),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(a++;a<i&&Ya(e.src.charCodeAt(a));)a++;return e.pos=a,!0}});var Bi=b((Pc,Pi)=>{"use strict";var Xa=F().isSpace,Ur=[];for(Br=0;Br<256;Br++)Ur.push(0);var Br;"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(t){Ur[t.charCodeAt(0)]=1});Pi.exports=function(e,r){var n,i=e.pos,o=e.posMax;if(e.src.charCodeAt(i)!==92)return!1;if(i++,i<o){if(n=e.src.charCodeAt(i),n<256&&Ur[n]!==0)return r||(e.pending+=e.src[i]),e.pos+=2,!0;if(n===10){for(r||e.push("hardbreak","br",0),i++;i<o&&(n=e.src.charCodeAt(i),!!Xa(n));)i++;return e.pos=i,!0}}return r||(e.pending+="\\"),e.pos++,!0}});var Hi=b((Bc,Ui)=>{"use strict";Ui.exports=function(e,r){var n,i,o,a,s,l,c,f,p=e.pos,d=e.src.charCodeAt(p);if(d!==96)return!1;for(n=p,p++,i=e.posMax;p<i&&e.src.charCodeAt(p)===96;)p++;if(o=e.src.slice(n,p),c=o.length,e.backticksScanned&&(e.backticks[c]||0)<=n)return r||(e.pending+=o),e.pos+=c,!0;for(s=l=p;(s=e.src.indexOf("`",l))!==-1;){for(l=s+1;l<i&&e.src.charCodeAt(l)===96;)l++;if(f=l-s,f===c)return r||(a=e.push("code_inline","code",0),a.markup=o,a.content=e.src.slice(p,s).replace(/\n/g," ").replace(/^ (.+) $/,"$1")),e.pos=l,!0;e.backticks[f]=s}return e.backticksScanned=!0,r||(e.pending+=o),e.pos+=c,!0}});var Gr=b((Uc,Hr)=>{"use strict";Hr.exports.tokenize=function(e,r){var n,i,o,a,s,l=e.pos,c=e.src.charCodeAt(l);if(r||c!==126||(i=e.scanDelims(e.pos,!0),a=i.length,s=String.fromCharCode(c),a<2))return!1;for(a%2&&(o=e.push("text","",0),o.content=s,a--),n=0;n<a;n+=2)o=e.push("text","",0),o.content=s+s,e.delimiters.push({marker:c,length:0,token:e.tokens.length-1,end:-1,open:i.can_open,close:i.can_close});return e.pos+=i.length,!0};function Gi(t,e){var r,n,i,o,a,s=[],l=e.length;for(r=0;r<l;r++)i=e[r],i.marker===126&&i.end!==-1&&(o=e[i.end],a=t.tokens[i.token],a.type="s_open",a.tag="s",a.nesting=1,a.markup="~~",a.content="",a=t.tokens[o.token],a.type="s_close",a.tag="s",a.nesting=-1,a.markup="~~",a.content="",t.tokens[o.token-1].type==="text"&&t.tokens[o.token-1].content==="~"&&s.push(o.token-1));for(;s.length;){for(r=s.pop(),n=r+1;n<t.tokens.length&&t.tokens[n].type==="s_close";)n++;n--,r!==n&&(a=t.tokens[n],t.tokens[n]=t.tokens[r],t.tokens[r]=a)}}Hr.exports.postProcess=function(e){var r,n=e.tokens_meta,i=e.tokens_meta.length;for(Gi(e,e.delimiters),r=0;r<i;r++)n[r]&&n[r].delimiters&&Gi(e,n[r].delimiters)}});var jr=b((Hc,Vr)=>{"use strict";Vr.exports.tokenize=function(e,r){var n,i,o,a=e.pos,s=e.src.charCodeAt(a);if(r||s!==95&&s!==42)return!1;for(i=e.scanDelims(e.pos,s===42),n=0;n<i.length;n++)o=e.push("text","",0),o.content=String.fromCharCode(s),e.delimiters.push({marker:s,length:i.length,token:e.tokens.length-1,end:-1,open:i.can_open,close:i.can_close});return e.pos+=i.length,!0};function Vi(t,e){var r,n,i,o,a,s,l=e.length;for(r=l-1;r>=0;r--)n=e[r],!(n.marker!==95&&n.marker!==42)&&n.end!==-1&&(i=e[n.end],s=r>0&&e[r-1].end===n.end+1&&e[r-1].marker===n.marker&&e[r-1].token===n.token-1&&e[n.end+1].token===i.token+1,a=String.fromCharCode(n.marker),o=t.tokens[n.token],o.type=s?"strong_open":"em_open",o.tag=s?"strong":"em",o.nesting=1,o.markup=s?a+a:a,o.content="",o=t.tokens[i.token],o.type=s?"strong_close":"em_close",o.tag=s?"strong":"em",o.nesting=-1,o.markup=s?a+a:a,o.content="",s&&(t.tokens[e[r-1].token].content="",t.tokens[e[n.end+1].token].content="",r--))}Vr.exports.postProcess=function(e){var r,n=e.tokens_meta,i=e.tokens_meta.length;for(Vi(e,e.delimiters),r=0;r<i;r++)n[r]&&n[r].delimiters&&Vi(e,n[r].delimiters)}});var Wi=b((Gc,ji)=>{"use strict";var Ja=F().normalizeReference,Wr=F().isSpace;ji.exports=function(e,r){var n,i,o,a,s,l,c,f,p,d="",h="",g=e.pos,k=e.posMax,w=e.pos,C=!0;if(e.src.charCodeAt(e.pos)!==91||(s=e.pos+1,a=e.md.helpers.parseLinkLabel(e,e.pos,!0),a<0))return!1;if(l=a+1,l<k&&e.src.charCodeAt(l)===40){for(C=!1,l++;l<k&&(i=e.src.charCodeAt(l),!(!Wr(i)&&i!==10));l++);if(l>=k)return!1;if(w=l,c=e.md.helpers.parseLinkDestination(e.src,l,e.posMax),c.ok){for(d=e.md.normalizeLink(c.str),e.md.validateLink(d)?l=c.pos:d="",w=l;l<k&&(i=e.src.charCodeAt(l),!(!Wr(i)&&i!==10));l++);if(c=e.md.helpers.parseLinkTitle(e.src,l,e.posMax),l<k&&w!==l&&c.ok)for(h=c.str,l=c.pos;l<k&&(i=e.src.charCodeAt(l),!(!Wr(i)&&i!==10));l++);}(l>=k||e.src.charCodeAt(l)!==41)&&(C=!0),l++}if(C){if(typeof e.env.references>"u")return!1;if(l<k&&e.src.charCodeAt(l)===91?(w=l+1,l=e.md.helpers.parseLinkLabel(e,l),l>=0?o=e.src.slice(w,l++):l=a+1):l=a+1,o||(o=e.src.slice(s,a)),f=e.env.references[Ja(o)],!f)return e.pos=g,!1;d=f.href,h=f.title}return r||(e.pos=s,e.posMax=a,p=e.push("link_open","a",1),p.attrs=n=[["href",d]],h&&n.push(["title",h]),e.md.inline.tokenize(e),p=e.push("link_close","a",-1)),e.pos=l,e.posMax=k,!0}});var Zi=b((Vc,$i)=>{"use strict";var Ka=F().normalizeReference,$r=F().isSpace;$i.exports=function(e,r){var n,i,o,a,s,l,c,f,p,d,h,g,k,w="",C=e.pos,_=e.posMax;if(e.src.charCodeAt(e.pos)!==33||e.src.charCodeAt(e.pos+1)!==91||(l=e.pos+2,s=e.md.helpers.parseLinkLabel(e,e.pos+1,!1),s<0))return!1;if(c=s+1,c<_&&e.src.charCodeAt(c)===40){for(c++;c<_&&(i=e.src.charCodeAt(c),!(!$r(i)&&i!==10));c++);if(c>=_)return!1;for(k=c,p=e.md.helpers.parseLinkDestination(e.src,c,e.posMax),p.ok&&(w=e.md.normalizeLink(p.str),e.md.validateLink(w)?c=p.pos:w=""),k=c;c<_&&(i=e.src.charCodeAt(c),!(!$r(i)&&i!==10));c++);if(p=e.md.helpers.parseLinkTitle(e.src,c,e.posMax),c<_&&k!==c&&p.ok)for(d=p.str,c=p.pos;c<_&&(i=e.src.charCodeAt(c),!(!$r(i)&&i!==10));c++);else d="";if(c>=_||e.src.charCodeAt(c)!==41)return e.pos=C,!1;c++}else{if(typeof e.env.references>"u")return!1;if(c<_&&e.src.charCodeAt(c)===91?(k=c+1,c=e.md.helpers.parseLinkLabel(e,c),c>=0?a=e.src.slice(k,c++):c=s+1):c=s+1,a||(a=e.src.slice(l,s)),f=e.env.references[Ka(a)],!f)return e.pos=C,!1;w=f.href,d=f.title}return r||(o=e.src.slice(l,s),e.md.inline.parse(o,e.md,e.env,g=[]),h=e.push("image","img",0),h.attrs=n=[["src",w],["alt",""]],h.children=g,h.content=o,d&&n.push(["title",d])),e.pos=c,e.posMax=_,!0}});var Xi=b((jc,Yi)=>{"use strict";var Qa=/^([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,el=/^([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)$/;Yi.exports=function(e,r){var n,i,o,a,s,l,c=e.pos;if(e.src.charCodeAt(c)!==60)return!1;for(s=e.pos,l=e.posMax;;){if(++c>=l||(a=e.src.charCodeAt(c),a===60))return!1;if(a===62)break}return n=e.src.slice(s+1,c),el.test(n)?(i=e.md.normalizeLink(n),e.md.validateLink(i)?(r||(o=e.push("link_open","a",1),o.attrs=[["href",i]],o.markup="autolink",o.info="auto",o=e.push("text","",0),o.content=e.md.normalizeLinkText(n),o=e.push("link_close","a",-1),o.markup="autolink",o.info="auto"),e.pos+=n.length+2,!0):!1):Qa.test(n)?(i=e.md.normalizeLink("mailto:"+n),e.md.validateLink(i)?(r||(o=e.push("link_open","a",1),o.attrs=[["href",i]],o.markup="autolink",o.info="auto",o=e.push("text","",0),o.content=e.md.normalizeLinkText(n),o=e.push("link_close","a",-1),o.markup="autolink",o.info="auto"),e.pos+=n.length+2,!0):!1):!1}});var Ki=b((Wc,Ji)=>{"use strict";var rl=Pr().HTML_TAG_RE;function tl(t){var e=t|32;return e>=97&&e<=122}Ji.exports=function(e,r){var n,i,o,a,s=e.pos;return!e.md.options.html||(o=e.posMax,e.src.charCodeAt(s)!==60||s+2>=o)||(n=e.src.charCodeAt(s+1),n!==33&&n!==63&&n!==47&&!tl(n))||(i=e.src.slice(s).match(rl),!i)?!1:(r||(a=e.push("html_inline","",0),a.content=e.src.slice(s,s+i[0].length)),e.pos+=i[0].length,!0)}});var to=b(($c,ro)=>{"use strict";var Qi=Sr(),nl=F().has,il=F().isValidEntityCode,eo=F().fromCodePoint,ol=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,sl=/^&([a-z][a-z0-9]{1,31});/i;ro.exports=function(e,r){var n,i,o,a=e.pos,s=e.posMax;if(e.src.charCodeAt(a)!==38)return!1;if(a+1<s){if(n=e.src.charCodeAt(a+1),n===35){if(o=e.src.slice(a).match(ol),o)return r||(i=o[1][0].toLowerCase()==="x"?parseInt(o[1].slice(1),16):parseInt(o[1],10),e.pending+=il(i)?eo(i):eo(65533)),e.pos+=o[0].length,!0}else if(o=e.src.slice(a).match(sl),o&&nl(Qi,o[1]))return r||(e.pending+=Qi[o[1]]),e.pos+=o[0].length,!0}return r||(e.pending+="&"),e.pos++,!0}});var oo=b((Zc,io)=>{"use strict";function no(t,e){var r,n,i,o,a,s,l,c,f={},p=e.length;if(p){var d=0,h=-2,g=[];for(r=0;r<p;r++)if(i=e[r],g.push(0),(e[d].marker!==i.marker||h!==i.token-1)&&(d=r),h=i.token,i.length=i.length||0,!!i.close){for(f.hasOwnProperty(i.marker)||(f[i.marker]=[-1,-1,-1,-1,-1,-1]),a=f[i.marker][(i.open?3:0)+i.length%3],n=d-g[d]-1,s=n;n>a;n-=g[n]+1)if(o=e[n],o.marker===i.marker&&o.open&&o.end<0&&(l=!1,(o.close||i.open)&&(o.length+i.length)%3===0&&(o.length%3!==0||i.length%3!==0)&&(l=!0),!l)){c=n>0&&!e[n-1].open?g[n-1]+1:0,g[r]=r-n+c,g[n]=c,i.open=!1,o.end=r,o.close=!1,s=-1,h=-2;break}s!==-1&&(f[i.marker][(i.open?3:0)+(i.length||0)%3]=s)}}}io.exports=function(e){var r,n=e.tokens_meta,i=e.tokens_meta.length;for(no(e,e.delimiters),r=0;r<i;r++)n[r]&&n[r].delimiters&&no(e,n[r].delimiters)}});var ao=b((Yc,so)=>{"use strict";so.exports=function(e){var r,n,i=0,o=e.tokens,a=e.tokens.length;for(r=n=0;r<a;r++)o[r].nesting<0&&i--,o[r].level=i,o[r].nesting>0&&i++,o[r].type==="text"&&r+1<a&&o[r+1].type==="text"?o[r+1].content=o[r].content+o[r+1].content:(r!==n&&(o[n]=o[r]),n++);r!==n&&(o.length=n)}});var po=b((Xc,fo)=>{"use strict";var Zr=ir(),lo=F().isWhiteSpace,co=F().isPunctChar,uo=F().isMdAsciiPunct;function ze(t,e,r,n){this.src=t,this.env=r,this.md=e,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1}ze.prototype.pushPending=function(){var t=new Zr("text","",0);return t.content=this.pending,t.level=this.pendingLevel,this.tokens.push(t),this.pending="",t};ze.prototype.push=function(t,e,r){this.pending&&this.pushPending();var n=new Zr(t,e,r),i=null;return r<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),n.level=this.level,r>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],i={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(n),this.tokens_meta.push(i),n};ze.prototype.scanDelims=function(t,e){var r=t,n,i,o,a,s,l,c,f,p,d=!0,h=!0,g=this.posMax,k=this.src.charCodeAt(t);for(n=t>0?this.src.charCodeAt(t-1):32;r<g&&this.src.charCodeAt(r)===k;)r++;return o=r-t,i=r<g?this.src.charCodeAt(r):32,c=uo(n)||co(String.fromCharCode(n)),p=uo(i)||co(String.fromCharCode(i)),l=lo(n),f=lo(i),f?d=!1:p&&(l||c||(d=!1)),l?h=!1:c&&(f||p||(h=!1)),e?(a=d,s=h):(a=d&&(!h||c),s=h&&(!d||p)),{can_open:a,can_close:s,length:o}};ze.prototype.Token=Zr;fo.exports=ze});var go=b((Jc,mo)=>{"use strict";var ho=tr(),Yr=[["text",Oi()],["newline",zi()],["escape",Bi()],["backticks",Hi()],["strikethrough",Gr().tokenize],["emphasis",jr().tokenize],["link",Wi()],["image",Zi()],["autolink",Xi()],["html_inline",Ki()],["entity",to()]],Xr=[["balance_pairs",oo()],["strikethrough",Gr().postProcess],["emphasis",jr().postProcess],["text_collapse",ao()]];function Pe(){var t;for(this.ruler=new ho,t=0;t<Yr.length;t++)this.ruler.push(Yr[t][0],Yr[t][1]);for(this.ruler2=new ho,t=0;t<Xr.length;t++)this.ruler2.push(Xr[t][0],Xr[t][1])}Pe.prototype.skipToken=function(t){var e,r,n=t.pos,i=this.ruler.getRules(""),o=i.length,a=t.md.options.maxNesting,s=t.cache;if(typeof s[n]<"u"){t.pos=s[n];return}if(t.level<a)for(r=0;r<o&&(t.level++,e=i[r](t,!0),t.level--,!e);r++);else t.pos=t.posMax;e||t.pos++,s[n]=t.pos};Pe.prototype.tokenize=function(t){for(var e,r,n=this.ruler.getRules(""),i=n.length,o=t.posMax,a=t.md.options.maxNesting;t.pos<o;){if(t.level<a)for(r=0;r<i&&(e=n[r](t,!1),!e);r++);if(e){if(t.pos>=o)break;continue}t.pending+=t.src[t.pos++]}t.pending&&t.pushPending()};Pe.prototype.parse=function(t,e,r,n){var i,o,a,s=new this.State(t,e,r,n);for(this.tokenize(s),o=this.ruler2.getRules(""),a=o.length,i=0;i<a;i++)o[i](s)};Pe.prototype.State=po();mo.exports=Pe});var xo=b((Kc,_o)=>{"use strict";_o.exports=function(t){var e={};e.src_Any=Fr().source,e.src_Cc=Rr().source,e.src_Z=Ir().source,e.src_P=Je().source,e.src_ZPCc=[e.src_Z,e.src_P,e.src_Cc].join("|"),e.src_ZCc=[e.src_Z,e.src_Cc].join("|");var r="[><\uFF5C]";return e.src_pseudo_letter="(?:(?!"+r+"|"+e.src_ZPCc+")"+e.src_Any+")",e.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",e.src_auth="(?:(?:(?!"+e.src_ZCc+"|[@/\\[\\]()]).)+@)?",e.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",e.src_host_terminator="(?=$|"+r+"|"+e.src_ZPCc+")(?!-|_|:\\d|\\.-|\\.(?!$|"+e.src_ZPCc+"))",e.src_path="(?:[/?#](?:(?!"+e.src_ZCc+"|"+r+`|[()[\\]{}.,"'?!\\-;]).|\\[(?:(?!`+e.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+e.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+e.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+e.src_ZCc+`|["]).)+\\"|\\'(?:(?!`+e.src_ZCc+"|[']).)+\\'|\\'(?="+e.src_pseudo_letter+"|[-]).|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+e.src_ZCc+"|[.]).|"+(t&&t["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+e.src_ZCc+").|;(?!"+e.src_ZCc+").|\\!+(?!"+e.src_ZCc+"|[!]).|\\?(?!"+e.src_ZCc+"|[?]).)+|\\/)?",e.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',e.src_xn="xn--[a-z0-9\\-]{1,59}",e.src_domain_root="(?:"+e.src_xn+"|"+e.src_pseudo_letter+"{1,63})",e.src_domain="(?:"+e.src_xn+"|(?:"+e.src_pseudo_letter+")|(?:"+e.src_pseudo_letter+"(?:-|"+e.src_pseudo_letter+"){0,61}"+e.src_pseudo_letter+"))",e.src_host="(?:(?:(?:(?:"+e.src_domain+")\\.)*"+e.src_domain+"))",e.tpl_host_fuzzy="(?:"+e.src_ip4+"|(?:(?:(?:"+e.src_domain+")\\.)+(?:%TLDS%)))",e.tpl_host_no_ip_fuzzy="(?:(?:(?:"+e.src_domain+")\\.)+(?:%TLDS%))",e.src_host_strict=e.src_host+e.src_host_terminator,e.tpl_host_fuzzy_strict=e.tpl_host_fuzzy+e.src_host_terminator,e.src_host_port_strict=e.src_host+e.src_port+e.src_host_terminator,e.tpl_host_port_fuzzy_strict=e.tpl_host_fuzzy+e.src_port+e.src_host_terminator,e.tpl_host_port_no_ip_fuzzy_strict=e.tpl_host_no_ip_fuzzy+e.src_port+e.src_host_terminator,e.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+e.src_ZPCc+"|>|$))",e.tpl_email_fuzzy="(^|"+r+'|"|\\(|'+e.src_ZCc+")("+e.src_email_name+"@"+e.tpl_host_fuzzy_strict+")",e.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|\uFF5C]|"+e.src_ZPCc+"))((?![$+<=>^`|\uFF5C])"+e.tpl_host_port_fuzzy_strict+e.src_path+")",e.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|\uFF5C]|"+e.src_ZPCc+"))((?![$+<=>^`|\uFF5C])"+e.tpl_host_port_no_ip_fuzzy_strict+e.src_path+")",e}});var Eo=b((Qc,vo)=>{"use strict";function Jr(t){var e=Array.prototype.slice.call(arguments,1);return e.forEach(function(r){r&&Object.keys(r).forEach(function(n){t[n]=r[n]})}),t}function ur(t){return Object.prototype.toString.call(t)}function al(t){return ur(t)==="[object String]"}function ll(t){return ur(t)==="[object Object]"}function cl(t){return ur(t)==="[object RegExp]"}function bo(t){return ur(t)==="[object Function]"}function ul(t){return t.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}var Co={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};function fl(t){return Object.keys(t||{}).reduce(function(e,r){return e||Co.hasOwnProperty(r)},!1)}var pl={"http:":{validate:function(t,e,r){var n=t.slice(e);return r.re.http||(r.re.http=new RegExp("^\\/\\/"+r.re.src_auth+r.re.src_host_port_strict+r.re.src_path,"i")),r.re.http.test(n)?n.match(r.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(t,e,r){var n=t.slice(e);return r.re.no_http||(r.re.no_http=new RegExp("^"+r.re.src_auth+"(?:localhost|(?:(?:"+r.re.src_domain+")\\.)+"+r.re.src_domain_root+")"+r.re.src_port+r.re.src_host_terminator+r.re.src_path,"i")),r.re.no_http.test(n)?e>=3&&t[e-3]===":"||e>=3&&t[e-3]==="/"?0:n.match(r.re.no_http)[0].length:0}},"mailto:":{validate:function(t,e,r){var n=t.slice(e);return r.re.mailto||(r.re.mailto=new RegExp("^"+r.re.src_email_name+"@"+r.re.src_host_strict,"i")),r.re.mailto.test(n)?n.match(r.re.mailto)[0].length:0}}},hl="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",dl="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|\u0440\u0444".split("|");function ml(t){t.__index__=-1,t.__text_cache__=""}function gl(t){return function(e,r){var n=e.slice(r);return t.test(n)?n.match(t)[0].length:0}}function ko(){return function(t,e){e.normalize(t)}}function cr(t){var e=t.re=xo()(t.__opts__),r=t.__tlds__.slice();t.onCompile(),t.__tlds_replaced__||r.push(hl),r.push(e.src_xn),e.src_tlds=r.join("|");function n(s){return s.replace("%TLDS%",e.src_tlds)}e.email_fuzzy=RegExp(n(e.tpl_email_fuzzy),"i"),e.link_fuzzy=RegExp(n(e.tpl_link_fuzzy),"i"),e.link_no_ip_fuzzy=RegExp(n(e.tpl_link_no_ip_fuzzy),"i"),e.host_fuzzy_test=RegExp(n(e.tpl_host_fuzzy_test),"i");var i=[];t.__compiled__={};function o(s,l){throw new Error('(LinkifyIt) Invalid schema "'+s+'": '+l)}Object.keys(t.__schemas__).forEach(function(s){var l=t.__schemas__[s];if(l!==null){var c={validate:null,link:null};if(t.__compiled__[s]=c,ll(l)){cl(l.validate)?c.validate=gl(l.validate):bo(l.validate)?c.validate=l.validate:o(s,l),bo(l.normalize)?c.normalize=l.normalize:l.normalize?o(s,l):c.normalize=ko();return}if(al(l)){i.push(s);return}o(s,l)}}),i.forEach(function(s){t.__compiled__[t.__schemas__[s]]&&(t.__compiled__[s].validate=t.__compiled__[t.__schemas__[s]].validate,t.__compiled__[s].normalize=t.__compiled__[t.__schemas__[s]].normalize)}),t.__compiled__[""]={validate:null,normalize:ko()};var a=Object.keys(t.__compiled__).filter(function(s){return s.length>0&&t.__compiled__[s]}).map(ul).join("|");t.re.schema_test=RegExp("(^|(?!_)(?:[><\uFF5C]|"+e.src_ZPCc+"))("+a+")","i"),t.re.schema_search=RegExp("(^|(?!_)(?:[><\uFF5C]|"+e.src_ZPCc+"))("+a+")","ig"),t.re.pretest=RegExp("("+t.re.schema_test.source+")|("+t.re.host_fuzzy_test.source+")|@","i"),ml(t)}function _l(t,e){var r=t.__index__,n=t.__last_index__,i=t.__text_cache__.slice(r,n);this.schema=t.__schema__.toLowerCase(),this.index=r+e,this.lastIndex=n+e,this.raw=i,this.text=i,this.url=i}function Ao(t,e){var r=new _l(t,e);return t.__compiled__[r.schema].normalize(r,t),r}function Y(t,e){if(!(this instanceof Y))return new Y(t,e);e||fl(t)&&(e=t,t={}),this.__opts__=Jr({},Co,e),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=Jr({},pl,t),this.__compiled__={},this.__tlds__=dl,this.__tlds_replaced__=!1,this.re={},cr(this)}Y.prototype.add=function(e,r){return this.__schemas__[e]=r,cr(this),this};Y.prototype.set=function(e){return this.__opts__=Jr(this.__opts__,e),this};Y.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;var r,n,i,o,a,s,l,c,f;if(this.re.schema_test.test(e)){for(l=this.re.schema_search,l.lastIndex=0;(r=l.exec(e))!==null;)if(o=this.testSchemaAt(e,r[2],l.lastIndex),o){this.__schema__=r[2],this.__index__=r.index+r[1].length,this.__last_index__=r.index+r[0].length+o;break}}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=e.search(this.re.host_fuzzy_test),c>=0&&(this.__index__<0||c<this.__index__)&&(n=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))!==null&&(a=n.index+n[1].length,(this.__index__<0||a<this.__index__)&&(this.__schema__="",this.__index__=a,this.__last_index__=n.index+n[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(f=e.indexOf("@"),f>=0&&(i=e.match(this.re.email_fuzzy))!==null&&(a=i.index+i[1].length,s=i.index+i[0].length,(this.__index__<0||a<this.__index__||a===this.__index__&&s>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=a,this.__last_index__=s))),this.__index__>=0};Y.prototype.pretest=function(e){return this.re.pretest.test(e)};Y.prototype.testSchemaAt=function(e,r,n){return this.__compiled__[r.toLowerCase()]?this.__compiled__[r.toLowerCase()].validate(e,n,this):0};Y.prototype.match=function(e){var r=0,n=[];this.__index__>=0&&this.__text_cache__===e&&(n.push(Ao(this,r)),r=this.__last_index__);for(var i=r?e.slice(r):e;this.test(i);)n.push(Ao(this,r)),i=i.slice(this.__last_index__),r+=this.__last_index__;return n.length?n:null};Y.prototype.tlds=function(e,r){return e=Array.isArray(e)?e:[e],r?(this.__tlds__=this.__tlds__.concat(e).sort().filter(function(n,i,o){return n!==o[i-1]}).reverse(),cr(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,cr(this),this)};Y.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),e.schema==="mailto:"&&!/^mailto:/i.test(e.url)&&(e.url="mailto:"+e.url)};Y.prototype.onCompile=function(){};vo.exports=Y});var Io=b((eu,Ro)=>{"use strict";var wo="-",xl=/^xn--/,bl=/[^\0-\x7F]/,kl=/[\x2E\u3002\uFF0E\uFF61]/g,Al={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},Kr=35,ne=Math.floor,Qr=String.fromCharCode;function ae(t){throw new RangeError(Al[t])}function Cl(t,e){let r=[],n=t.length;for(;n--;)r[n]=e(t[n]);return r}function Do(t,e){let r=t.split("@"),n="";r.length>1&&(n=r[0]+"@",t=r[1]),t=t.replace(kl,".");let i=t.split("."),o=Cl(i,e).join(".");return n+o}function To(t){let e=[],r=0,n=t.length;for(;r<n;){let i=t.charCodeAt(r++);if(i>=55296&&i<=56319&&r<n){let o=t.charCodeAt(r++);(o&64512)==56320?e.push(((i&1023)<<10)+(o&1023)+65536):(e.push(i),r--)}else e.push(i)}return e}var vl=t=>String.fromCodePoint(...t),El=function(t){return t>=48&&t<58?26+(t-48):t>=65&&t<91?t-65:t>=97&&t<123?t-97:36},yo=function(t,e){return t+22+75*(t<26)-((e!=0)<<5)},So=function(t,e,r){let n=0;for(t=r?ne(t/700):t>>1,t+=ne(t/e);t>Kr*26>>1;n+=36)t=ne(t/Kr);return ne(n+(Kr+1)*t/(t+38))},qo=function(t){let e=[],r=t.length,n=0,i=128,o=72,a=t.lastIndexOf(wo);a<0&&(a=0);for(let s=0;s<a;++s)t.charCodeAt(s)>=128&&ae("not-basic"),e.push(t.charCodeAt(s));for(let s=a>0?a+1:0;s<r;){let l=n;for(let f=1,p=36;;p+=36){s>=r&&ae("invalid-input");let d=El(t.charCodeAt(s++));d>=36&&ae("invalid-input"),d>ne((2147483647-n)/f)&&ae("overflow"),n+=d*f;let h=p<=o?1:p>=o+26?26:p-o;if(d<h)break;let g=36-h;f>ne(2147483647/g)&&ae("overflow"),f*=g}let c=e.length+1;o=So(n-l,c,l==0),ne(n/c)>2147483647-i&&ae("overflow"),i+=ne(n/c),n%=c,e.splice(n++,0,i)}return String.fromCodePoint(...e)},Fo=function(t){let e=[];t=To(t);let r=t.length,n=128,i=0,o=72;for(let l of t)l<128&&e.push(Qr(l));let a=e.length,s=a;for(a&&e.push(wo);s<r;){let l=2147483647;for(let f of t)f>=n&&f<l&&(l=f);let c=s+1;l-n>ne((2147483647-i)/c)&&ae("overflow"),i+=(l-n)*c,n=l;for(let f of t)if(f<n&&++i>2147483647&&ae("overflow"),f===n){let p=i;for(let d=36;;d+=36){let h=d<=o?1:d>=o+26?26:d-o;if(p<h)break;let g=p-h,k=36-h;e.push(Qr(yo(h+g%k,0))),p=ne(g/k)}e.push(Qr(yo(p,0))),o=So(i,c,s===a),i=0,++s}++i,++n}return e.join("")},yl=function(t){return Do(t,function(e){return xl.test(e)?qo(e.slice(4).toLowerCase()):e})},wl=function(t){return Do(t,function(e){return bl.test(e)?"xn--"+Fo(e):e})},Dl={version:"2.3.1",ucs2:{decode:To,encode:vl},decode:qo,encode:Fo,toASCII:wl,toUnicode:yl};Ro.exports=Dl});var Lo=b((ru,Mo)=>{"use strict";Mo.exports={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}}});var No=b((tu,Oo)=>{"use strict";Oo.exports={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","text_collapse"]}}}});var Po=b((nu,zo)=>{"use strict";zo.exports={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"\u201C\u201D\u2018\u2019",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","text_collapse"]}}}});var Go=b((iu,Ho)=>{"use strict";var Be=F(),Tl=En(),Sl=wn(),ql=Jn(),Fl=Mi(),Rl=go(),Il=Eo(),he=qr(),Bo=Io(),Ml={default:Lo(),zero:No(),commonmark:Po()},Ll=/^(vbscript|javascript|file|data):/,Ol=/^data:image\/(gif|png|jpeg|webp);/;function Nl(t){var e=t.trim().toLowerCase();return Ll.test(e)?!!Ol.test(e):!0}var Uo=["http:","https:","mailto:"];function zl(t){var e=he.parse(t,!0);if(e.hostname&&(!e.protocol||Uo.indexOf(e.protocol)>=0))try{e.hostname=Bo.toASCII(e.hostname)}catch{}return he.encode(he.format(e))}function Pl(t){var e=he.parse(t,!0);if(e.hostname&&(!e.protocol||Uo.indexOf(e.protocol)>=0))try{e.hostname=Bo.toUnicode(e.hostname)}catch{}return he.decode(he.format(e),he.decode.defaultChars+"%")}function X(t,e){if(!(this instanceof X))return new X(t,e);e||Be.isString(t)||(e=t||{},t="default"),this.inline=new Rl,this.block=new Fl,this.core=new ql,this.renderer=new Sl,this.linkify=new Il,this.validateLink=Nl,this.normalizeLink=zl,this.normalizeLinkText=Pl,this.utils=Be,this.helpers=Be.assign({},Tl),this.options={},this.configure(t),e&&this.set(e)}X.prototype.set=function(t){return Be.assign(this.options,t),this};X.prototype.configure=function(t){var e=this,r;if(Be.isString(t)&&(r=t,t=Ml[r],!t))throw new Error('Wrong `markdown-it` preset "'+r+'", check name');if(!t)throw new Error("Wrong `markdown-it` preset, can't be empty");return t.options&&e.set(t.options),t.components&&Object.keys(t.components).forEach(function(n){t.components[n].rules&&e[n].ruler.enableOnly(t.components[n].rules),t.components[n].rules2&&e[n].ruler2.enableOnly(t.components[n].rules2)}),this};X.prototype.enable=function(t,e){var r=[];Array.isArray(t)||(t=[t]),["core","block","inline"].forEach(function(i){r=r.concat(this[i].ruler.enable(t,!0))},this),r=r.concat(this.inline.ruler2.enable(t,!0));var n=t.filter(function(i){return r.indexOf(i)<0});if(n.length&&!e)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+n);return this};X.prototype.disable=function(t,e){var r=[];Array.isArray(t)||(t=[t]),["core","block","inline"].forEach(function(i){r=r.concat(this[i].ruler.disable(t,!0))},this),r=r.concat(this.inline.ruler2.disable(t,!0));var n=t.filter(function(i){return r.indexOf(i)<0});if(n.length&&!e)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+n);return this};X.prototype.use=function(t){var e=[this].concat(Array.prototype.slice.call(arguments,1));return t.apply(t,e),this};X.prototype.parse=function(t,e){if(typeof t!="string")throw new Error("Input data should be a String");var r=new this.core.State(t,this,e);return this.core.process(r),r.tokens};X.prototype.render=function(t,e){return e=e||{},this.renderer.render(this.parse(t,e),this.options,e)};X.prototype.parseInline=function(t,e){var r=new this.core.State(t,this,e);return r.inlineMode=!0,this.core.process(r),r.tokens};X.prototype.renderInline=function(t,e){return e=e||{},this.renderer.render(this.parseInline(t,e),this.options,e)};Ho.exports=X});var jo=b((ou,Vo)=>{"use strict";Vo.exports=Go()});var{entries:Lt,setPrototypeOf:wt,isFrozen:cs,getPrototypeOf:us,getOwnPropertyDescriptor:fs}=Object,{freeze:j,seal:Z,create:Ot}=Object,{apply:Dr,construct:Tr}=typeof Reflect<"u"&&Reflect;j||(j=function(e){return e});Z||(Z=function(e){return e});Dr||(Dr=function(e,r,n){return e.apply(r,n)});Tr||(Tr=function(e,r){return new e(...r)});var Ze=W(Array.prototype.forEach),ps=W(Array.prototype.lastIndexOf),Dt=W(Array.prototype.pop),Re=W(Array.prototype.push),hs=W(Array.prototype.splice),Xe=W(String.prototype.toLowerCase),Cr=W(String.prototype.toString),Tt=W(String.prototype.match),Ie=W(String.prototype.replace),ds=W(String.prototype.indexOf),ms=W(String.prototype.trim),J=W(Object.prototype.hasOwnProperty),V=W(RegExp.prototype.test),Me=gs(TypeError);function W(t){return function(e){for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];return Dr(t,e,n)}}function gs(t){return function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return Tr(t,r)}}function T(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Xe;wt&&wt(t,null);let n=e.length;for(;n--;){let i=e[n];if(typeof i=="string"){let o=r(i);o!==i&&(cs(e)||(e[n]=o),i=o)}t[i]=!0}return t}function _s(t){for(let e=0;e<t.length;e++)J(t,e)||(t[e]=null);return t}function fe(t){let e=Ot(null);for(let[r,n]of Lt(t))J(t,r)&&(Array.isArray(n)?e[r]=_s(n):n&&typeof n=="object"&&n.constructor===Object?e[r]=fe(n):e[r]=n);return e}function Le(t,e){for(;t!==null;){let n=fs(t,e);if(n){if(n.get)return W(n.get);if(typeof n.value=="function")return W(n.value)}t=us(t)}function r(){return null}return r}var St=j(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),vr=j(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Er=j(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),xs=j(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),yr=j(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),bs=j(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),qt=j(["#text"]),Ft=j(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),wr=j(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Rt=j(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Ye=j(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),ks=Z(/\{\{[\w\W]*|[\w\W]*\}\}/gm),As=Z(/<%[\w\W]*|[\w\W]*%>/gm),Cs=Z(/\$\{[\w\W]*/gm),vs=Z(/^data-[\-\w.\u00B7-\uFFFF]+$/),Es=Z(/^aria-[\-\w]+$/),Nt=Z(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),ys=Z(/^(?:\w+script|data):/i),ws=Z(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),zt=Z(/^html$/i),Ds=Z(/^[a-z][.\w]*(-[.\w]+)+$/i),It=Object.freeze({__proto__:null,ARIA_ATTR:Es,ATTR_WHITESPACE:ws,CUSTOM_ELEMENT:Ds,DATA_ATTR:vs,DOCTYPE_NAME:zt,ERB_EXPR:As,IS_ALLOWED_URI:Nt,IS_SCRIPT_OR_DATA:ys,MUSTACHE_EXPR:ks,TMPLIT_EXPR:Cs}),Oe={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},Ts=function(){return typeof window>"u"?null:window},Ss=function(e,r){if(typeof e!="object"||typeof e.createPolicy!="function")return null;let n=null,i="data-tt-policy-suffix";r&&r.hasAttribute(i)&&(n=r.getAttribute(i));let o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML(a){return a},createScriptURL(a){return a}})}catch{return console.warn("TrustedTypes policy "+o+" could not be created."),null}},Mt=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Pt(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Ts(),e=E=>Pt(E);if(e.version="3.2.4",e.removed=[],!t||!t.document||t.document.nodeType!==Oe.document||!t.Element)return e.isSupported=!1,e;let{document:r}=t,n=r,i=n.currentScript,{DocumentFragment:o,HTMLTemplateElement:a,Node:s,Element:l,NodeFilter:c,NamedNodeMap:f=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:p,DOMParser:d,trustedTypes:h}=t,g=l.prototype,k=Le(g,"cloneNode"),w=Le(g,"remove"),C=Le(g,"nextSibling"),_=Le(g,"childNodes"),y=Le(g,"parentNode");if(typeof a=="function"){let E=r.createElement("template");E.content&&E.content.ownerDocument&&(r=E.content.ownerDocument)}let v,D="",{implementation:x,createNodeIterator:R,createDocumentFragment:L,getElementsByTagName:de}=r,{importNode:q}=n,S=Mt();e.isSupported=typeof Lt=="function"&&typeof y=="function"&&x&&x.createHTMLDocument!==void 0;let{MUSTACHE_EXPR:le,ERB_EXPR:se,TMPLIT_EXPR:ce,DATA_ATTR:ye,ARIA_ATTR:U,IS_SCRIPT_OR_DATA:we,ATTR_WHITESPACE:De,CUSTOM_ELEMENT:$o}=It,{IS_ALLOWED_URI:rt}=It,N=null,tt=T({},[...St,...vr,...Er,...yr,...qt]),P=null,nt=T({},[...Ft,...wr,...Rt,...Ye]),M=Object.seal(Ot(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Te=null,fr=null,it=!0,pr=!0,ot=!1,st=!0,me=!1,hr=!0,ue=!1,dr=!1,mr=!1,ge=!1,Ue=!1,He=!1,at=!0,lt=!1,Zo="user-content-",gr=!0,Se=!1,_e={},xe=null,ct=T({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),ut=null,ft=T({},["audio","video","img","source","image","track"]),_r=null,pt=T({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ge="http://www.w3.org/1998/Math/MathML",Ve="http://www.w3.org/2000/svg",ie="http://www.w3.org/1999/xhtml",be=ie,xr=!1,br=null,Yo=T({},[Ge,Ve,ie],Cr),je=T({},["mi","mo","mn","ms","mtext"]),We=T({},["annotation-xml"]),Xo=T({},["title","style","font","a","script"]),qe=null,Jo=["application/xhtml+xml","text/html"],Ko="text/html",z=null,ke=null,Qo=r.createElement("form"),ht=function(u){return u instanceof RegExp||u instanceof Function},kr=function(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(ke&&ke===u)){if((!u||typeof u!="object")&&(u={}),u=fe(u),qe=Jo.indexOf(u.PARSER_MEDIA_TYPE)===-1?Ko:u.PARSER_MEDIA_TYPE,z=qe==="application/xhtml+xml"?Cr:Xe,N=J(u,"ALLOWED_TAGS")?T({},u.ALLOWED_TAGS,z):tt,P=J(u,"ALLOWED_ATTR")?T({},u.ALLOWED_ATTR,z):nt,br=J(u,"ALLOWED_NAMESPACES")?T({},u.ALLOWED_NAMESPACES,Cr):Yo,_r=J(u,"ADD_URI_SAFE_ATTR")?T(fe(pt),u.ADD_URI_SAFE_ATTR,z):pt,ut=J(u,"ADD_DATA_URI_TAGS")?T(fe(ft),u.ADD_DATA_URI_TAGS,z):ft,xe=J(u,"FORBID_CONTENTS")?T({},u.FORBID_CONTENTS,z):ct,Te=J(u,"FORBID_TAGS")?T({},u.FORBID_TAGS,z):{},fr=J(u,"FORBID_ATTR")?T({},u.FORBID_ATTR,z):{},_e=J(u,"USE_PROFILES")?u.USE_PROFILES:!1,it=u.ALLOW_ARIA_ATTR!==!1,pr=u.ALLOW_DATA_ATTR!==!1,ot=u.ALLOW_UNKNOWN_PROTOCOLS||!1,st=u.ALLOW_SELF_CLOSE_IN_ATTR!==!1,me=u.SAFE_FOR_TEMPLATES||!1,hr=u.SAFE_FOR_XML!==!1,ue=u.WHOLE_DOCUMENT||!1,ge=u.RETURN_DOM||!1,Ue=u.RETURN_DOM_FRAGMENT||!1,He=u.RETURN_TRUSTED_TYPE||!1,mr=u.FORCE_BODY||!1,at=u.SANITIZE_DOM!==!1,lt=u.SANITIZE_NAMED_PROPS||!1,gr=u.KEEP_CONTENT!==!1,Se=u.IN_PLACE||!1,rt=u.ALLOWED_URI_REGEXP||Nt,be=u.NAMESPACE||ie,je=u.MATHML_TEXT_INTEGRATION_POINTS||je,We=u.HTML_INTEGRATION_POINTS||We,M=u.CUSTOM_ELEMENT_HANDLING||{},u.CUSTOM_ELEMENT_HANDLING&&ht(u.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(M.tagNameCheck=u.CUSTOM_ELEMENT_HANDLING.tagNameCheck),u.CUSTOM_ELEMENT_HANDLING&&ht(u.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(M.attributeNameCheck=u.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),u.CUSTOM_ELEMENT_HANDLING&&typeof u.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(M.allowCustomizedBuiltInElements=u.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),me&&(pr=!1),Ue&&(ge=!0),_e&&(N=T({},qt),P=[],_e.html===!0&&(T(N,St),T(P,Ft)),_e.svg===!0&&(T(N,vr),T(P,wr),T(P,Ye)),_e.svgFilters===!0&&(T(N,Er),T(P,wr),T(P,Ye)),_e.mathMl===!0&&(T(N,yr),T(P,Rt),T(P,Ye))),u.ADD_TAGS&&(N===tt&&(N=fe(N)),T(N,u.ADD_TAGS,z)),u.ADD_ATTR&&(P===nt&&(P=fe(P)),T(P,u.ADD_ATTR,z)),u.ADD_URI_SAFE_ATTR&&T(_r,u.ADD_URI_SAFE_ATTR,z),u.FORBID_CONTENTS&&(xe===ct&&(xe=fe(xe)),T(xe,u.FORBID_CONTENTS,z)),gr&&(N["#text"]=!0),ue&&T(N,["html","head","body"]),N.table&&(T(N,["tbody"]),delete Te.tbody),u.TRUSTED_TYPES_POLICY){if(typeof u.TRUSTED_TYPES_POLICY.createHTML!="function")throw Me('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof u.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Me('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');v=u.TRUSTED_TYPES_POLICY,D=v.createHTML("")}else v===void 0&&(v=Ss(h,i)),v!==null&&typeof D=="string"&&(D=v.createHTML(""));j&&j(u),ke=u}},dt=T({},[...vr,...Er,...xs]),mt=T({},[...yr,...bs]),es=function(u){let m=y(u);(!m||!m.tagName)&&(m={namespaceURI:be,tagName:"template"});let A=Xe(u.tagName),I=Xe(m.tagName);return br[u.namespaceURI]?u.namespaceURI===Ve?m.namespaceURI===ie?A==="svg":m.namespaceURI===Ge?A==="svg"&&(I==="annotation-xml"||je[I]):!!dt[A]:u.namespaceURI===Ge?m.namespaceURI===ie?A==="math":m.namespaceURI===Ve?A==="math"&&We[I]:!!mt[A]:u.namespaceURI===ie?m.namespaceURI===Ve&&!We[I]||m.namespaceURI===Ge&&!je[I]?!1:!mt[A]&&(Xo[A]||!dt[A]):!!(qe==="application/xhtml+xml"&&br[u.namespaceURI]):!1},Q=function(u){Re(e.removed,{element:u});try{y(u).removeChild(u)}catch{w(u)}},$e=function(u,m){try{Re(e.removed,{attribute:m.getAttributeNode(u),from:m})}catch{Re(e.removed,{attribute:null,from:m})}if(m.removeAttribute(u),u==="is")if(ge||Ue)try{Q(m)}catch{}else try{m.setAttribute(u,"")}catch{}},gt=function(u){let m=null,A=null;if(mr)u="<remove></remove>"+u;else{let B=Tt(u,/^[\r\n\t ]+/);A=B&&B[0]}qe==="application/xhtml+xml"&&be===ie&&(u='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+u+"</body></html>");let I=v?v.createHTML(u):u;if(be===ie)try{m=new d().parseFromString(I,qe)}catch{}if(!m||!m.documentElement){m=x.createDocument(be,"template",null);try{m.documentElement.innerHTML=xr?D:I}catch{}}let H=m.body||m.documentElement;return u&&A&&H.insertBefore(r.createTextNode(A),H.childNodes[0]||null),be===ie?de.call(m,ue?"html":"body")[0]:ue?m.documentElement:H},_t=function(u){return R.call(u.ownerDocument||u,u,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},Ar=function(u){return u instanceof p&&(typeof u.nodeName!="string"||typeof u.textContent!="string"||typeof u.removeChild!="function"||!(u.attributes instanceof f)||typeof u.removeAttribute!="function"||typeof u.setAttribute!="function"||typeof u.namespaceURI!="string"||typeof u.insertBefore!="function"||typeof u.hasChildNodes!="function")},xt=function(u){return typeof s=="function"&&u instanceof s};function oe(E,u,m){Ze(E,A=>{A.call(e,u,m,ke)})}let bt=function(u){let m=null;if(oe(S.beforeSanitizeElements,u,null),Ar(u))return Q(u),!0;let A=z(u.nodeName);if(oe(S.uponSanitizeElement,u,{tagName:A,allowedTags:N}),u.hasChildNodes()&&!xt(u.firstElementChild)&&V(/<[/\w]/g,u.innerHTML)&&V(/<[/\w]/g,u.textContent)||u.nodeType===Oe.progressingInstruction||hr&&u.nodeType===Oe.comment&&V(/<[/\w]/g,u.data))return Q(u),!0;if(!N[A]||Te[A]){if(!Te[A]&&At(A)&&(M.tagNameCheck instanceof RegExp&&V(M.tagNameCheck,A)||M.tagNameCheck instanceof Function&&M.tagNameCheck(A)))return!1;if(gr&&!xe[A]){let I=y(u)||u.parentNode,H=_(u)||u.childNodes;if(H&&I){let B=H.length;for(let $=B-1;$>=0;--$){let ee=k(H[$],!0);ee.__removalCount=(u.__removalCount||0)+1,I.insertBefore(ee,C(u))}}}return Q(u),!0}return u instanceof l&&!es(u)||(A==="noscript"||A==="noembed"||A==="noframes")&&V(/<\/no(script|embed|frames)/i,u.innerHTML)?(Q(u),!0):(me&&u.nodeType===Oe.text&&(m=u.textContent,Ze([le,se,ce],I=>{m=Ie(m,I," ")}),u.textContent!==m&&(Re(e.removed,{element:u.cloneNode()}),u.textContent=m)),oe(S.afterSanitizeElements,u,null),!1)},kt=function(u,m,A){if(at&&(m==="id"||m==="name")&&(A in r||A in Qo))return!1;if(!(pr&&!fr[m]&&V(ye,m))){if(!(it&&V(U,m))){if(!P[m]||fr[m]){if(!(At(u)&&(M.tagNameCheck instanceof RegExp&&V(M.tagNameCheck,u)||M.tagNameCheck instanceof Function&&M.tagNameCheck(u))&&(M.attributeNameCheck instanceof RegExp&&V(M.attributeNameCheck,m)||M.attributeNameCheck instanceof Function&&M.attributeNameCheck(m))||m==="is"&&M.allowCustomizedBuiltInElements&&(M.tagNameCheck instanceof RegExp&&V(M.tagNameCheck,A)||M.tagNameCheck instanceof Function&&M.tagNameCheck(A))))return!1}else if(!_r[m]){if(!V(rt,Ie(A,De,""))){if(!((m==="src"||m==="xlink:href"||m==="href")&&u!=="script"&&ds(A,"data:")===0&&ut[u])){if(!(ot&&!V(we,Ie(A,De,"")))){if(A)return!1}}}}}}return!0},At=function(u){return u!=="annotation-xml"&&Tt(u,$o)},Ct=function(u){oe(S.beforeSanitizeAttributes,u,null);let{attributes:m}=u;if(!m||Ar(u))return;let A={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:P,forceKeepAttr:void 0},I=m.length;for(;I--;){let H=m[I],{name:B,namespaceURI:$,value:ee}=H,Fe=z(B),G=B==="value"?ee:ms(ee);if(A.attrName=Fe,A.attrValue=G,A.keepAttr=!0,A.forceKeepAttr=void 0,oe(S.uponSanitizeAttribute,u,A),G=A.attrValue,lt&&(Fe==="id"||Fe==="name")&&($e(B,u),G=Zo+G),hr&&V(/((--!?|])>)|<\/(style|title)/i,G)){$e(B,u);continue}if(A.forceKeepAttr||($e(B,u),!A.keepAttr))continue;if(!st&&V(/\/>/i,G)){$e(B,u);continue}me&&Ze([le,se,ce],Et=>{G=Ie(G,Et," ")});let vt=z(u.nodeName);if(kt(vt,Fe,G)){if(v&&typeof h=="object"&&typeof h.getAttributeType=="function"&&!$)switch(h.getAttributeType(vt,Fe)){case"TrustedHTML":{G=v.createHTML(G);break}case"TrustedScriptURL":{G=v.createScriptURL(G);break}}try{$?u.setAttributeNS($,B,G):u.setAttribute(B,G),Ar(u)?Q(u):Dt(e.removed)}catch{}}}oe(S.afterSanitizeAttributes,u,null)},rs=function E(u){let m=null,A=_t(u);for(oe(S.beforeSanitizeShadowDOM,u,null);m=A.nextNode();)oe(S.uponSanitizeShadowNode,m,null),bt(m),Ct(m),m.content instanceof o&&E(m.content);oe(S.afterSanitizeShadowDOM,u,null)};return e.sanitize=function(E){let u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=null,A=null,I=null,H=null;if(xr=!E,xr&&(E="<!-->"),typeof E!="string"&&!xt(E))if(typeof E.toString=="function"){if(E=E.toString(),typeof E!="string")throw Me("dirty is not a string, aborting")}else throw Me("toString is not a function");if(!e.isSupported)return E;if(dr||kr(u),e.removed=[],typeof E=="string"&&(Se=!1),Se){if(E.nodeName){let ee=z(E.nodeName);if(!N[ee]||Te[ee])throw Me("root node is forbidden and cannot be sanitized in-place")}}else if(E instanceof s)m=gt("<!---->"),A=m.ownerDocument.importNode(E,!0),A.nodeType===Oe.element&&A.nodeName==="BODY"||A.nodeName==="HTML"?m=A:m.appendChild(A);else{if(!ge&&!me&&!ue&&E.indexOf("<")===-1)return v&&He?v.createHTML(E):E;if(m=gt(E),!m)return ge?null:He?D:""}m&&mr&&Q(m.firstChild);let B=_t(Se?E:m);for(;I=B.nextNode();)bt(I),Ct(I),I.content instanceof o&&rs(I.content);if(Se)return E;if(ge){if(Ue)for(H=L.call(m.ownerDocument);m.firstChild;)H.appendChild(m.firstChild);else H=m;return(P.shadowroot||P.shadowrootmode)&&(H=q.call(n,H,!0)),H}let $=ue?m.outerHTML:m.innerHTML;return ue&&N["!doctype"]&&m.ownerDocument&&m.ownerDocument.doctype&&m.ownerDocument.doctype.name&&V(zt,m.ownerDocument.doctype.name)&&($="<!DOCTYPE "+m.ownerDocument.doctype.name+`>
`+$),me&&Ze([le,se,ce],ee=>{$=Ie($,ee," ")}),v&&He?v.createHTML($):$},e.setConfig=function(){let E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};kr(E),dr=!0},e.clearConfig=function(){ke=null,dr=!1},e.isValidAttribute=function(E,u,m){ke||kr({});let A=z(E),I=z(u);return kt(A,I,m)},e.addHook=function(E,u){typeof u=="function"&&Re(S[E],u)},e.removeHook=function(E,u){if(u!==void 0){let m=ps(S[E],u);return m===-1?void 0:hs(S[E],m,1)[0]}return Dt(S[E])},e.removeHooks=function(E){S[E]=[]},e.removeAllHooks=function(){S=Mt()},e}var Bt=Pt();var Wo=ls(jo()),Bl=Object.freeze(["a","abbr","b","bdo","blockquote","br","caption","cite","code","col","colgroup","dd","del","details","dfn","div","dl","dt","em","figcaption","figure","h1","h2","h3","h4","h5","h6","hr","i","img","ins","kbd","label","li","mark","ol","p","pre","q","rp","rt","ruby","samp","small","small","source","span","strike","strong","sub","summary","sup","table","tbody","td","tfoot","th","thead","time","tr","tt","u","ul","var","video","wbr"]),Ul=Object.freeze(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Hl={ALLOWED_TAGS:[...Bl,...Ul]},au=t=>{let e=new Wo.default({html:!0,linkify:!0,highlight:(i,o)=>o?`<div class="vscode-code-block" data-vscode-code-block-lang="${e.utils.escapeHtml(o)}">${e.utils.escapeHtml(i)}</div>`:e.utils.escapeHtml(i)});e.linkify.set({fuzzyLink:!1}),Gl(e),Vl(e);let r=document.createElement("style");r.textContent=`
		.emptyMarkdownCell::before {
			content: "${document.documentElement.style.getPropertyValue("--notebook-cell-markup-empty-content")}";
			font-style: italic;
			opacity: 0.6;
		}

		img {
			max-width: 100%;
			max-height: 100%;
		}

		a {
			text-decoration: none;
		}

		a:hover {
			text-decoration: underline;
		}

		a:focus,
		input:focus,
		select:focus,
		textarea:focus {
			outline: 1px solid -webkit-focus-ring-color;
			outline-offset: -1px;
		}

		hr {
			border: 0;
			height: 2px;
			border-bottom: 2px solid;
		}

		h2, h3, h4, h5, h6 {
			font-weight: normal;
		}

		h1 {
			font-size: 2.3em;
		}

		h2 {
			font-size: 2em;
		}

		h3 {
			font-size: 1.7em;
		}

		h3 {
			font-size: 1.5em;
		}

		h4 {
			font-size: 1.3em;
		}

		h5 {
			font-size: 1.2em;
		}

		h1,
		h2,
		h3 {
			font-weight: normal;
		}

		div {
			width: 100%;
		}

		/* Adjust margin of first item in markdown cell */
		*:first-child {
			margin-top: 0px;
		}

		/* h1 tags don't need top margin */
		h1:first-child {
			margin-top: 0;
		}

		/* Removes bottom margin when only one item exists in markdown cell */
		#preview > *:only-child,
		#preview > *:last-child {
			margin-bottom: 0;
			padding-bottom: 0;
		}

		/* makes all markdown cells consistent */
		div {
			min-height: var(--notebook-markdown-min-height);
		}

		table {
			border-collapse: collapse;
			border-spacing: 0;
		}

		table th,
		table td {
			border: 1px solid;
		}

		table > thead > tr > th {
			text-align: left;
			border-bottom: 1px solid;
		}

		table > thead > tr > th,
		table > thead > tr > td,
		table > tbody > tr > th,
		table > tbody > tr > td {
			padding: 5px 10px;
		}

		table > tbody > tr + tr > td {
			border-top: 1px solid;
		}

		blockquote {
			margin: 0 7px 0 5px;
			padding: 0 16px 0 10px;
			border-left-width: 5px;
			border-left-style: solid;
		}

		code {
			font-size: 1em;
			font-family: var(--vscode-editor-font-family);
		}

		pre code {
			line-height: 1.357em;
			white-space: pre-wrap;
			padding: 0;
		}

		li p {
			margin-bottom: 0.7em;
		}

		ul,
		ol {
			margin-bottom: 0.7em;
		}
	`;let n=document.createElement("template");return n.classList.add("markdown-style"),n.content.appendChild(r),document.head.appendChild(n),{renderOutputItem:(i,o)=>{let a;if(o.shadowRoot)a=o.shadowRoot.getElementById("preview");else{let l=o.attachShadow({mode:"open"}),c=document.getElementById("_defaultStyles");l.appendChild(c.cloneNode(!0));for(let f of document.getElementsByClassName("markdown-style"))f instanceof HTMLTemplateElement?l.appendChild(f.content.cloneNode(!0)):l.appendChild(f.cloneNode(!0));a=document.createElement("div"),a.id="preview",l.appendChild(a)}let s=i.text();if(s.trim().length===0)a.innerText="",a.classList.add("emptyMarkdownCell");else{a.classList.remove("emptyMarkdownCell");let l=i.mime.startsWith("text/x-")?`\`\`\`${i.mime.substr(7)}
${s}
\`\`\``:i.mime.startsWith("application/")?`\`\`\`${i.mime.substr(12)}
${s}
\`\`\``:s,c=e.render(l,{outputItem:i});a.innerHTML=t.workspace.isTrusted?c:Bt.sanitize(c,Hl)}},extendMarkdownIt:i=>{try{i(e)}catch(o){console.error("Error extending markdown-it",o)}}}};function Gl(t){let e=new Map,r=t.renderer.rules.heading_open;t.renderer.rules.heading_open=(i,o,a,s,l)=>{let c=i[o+1].children.reduce((p,d)=>p+d.content,""),f=et(c);if(e.has(f)){let p=e.get(f);e.set(f,p+1),f=et(f+"-"+(p+1))}else e.set(f,0);return i[o].attrSet("id",f),r?r(i,o,a,s,l):l.renderToken(i,o,a)};let n=t.render;t.render=function(){return e.clear(),n.apply(this,arguments)}}function Vl(t){let e=t.renderer.rules.link_open;t.renderer.rules.link_open=(r,n,i,o,a)=>{let s=r[n],l=s.attrGet("href");return typeof l=="string"&&l.startsWith("#")&&s.attrSet("href","#"+et(l.slice(1))),e?e(r,n,i,o,a):a.renderToken(r,n,i)}}function et(t){return encodeURI(t.trim().toLowerCase().replace(/\s+/g,"-").replace(/[\]\[\!\/\'\"\#\$\%\&\(\)\*\+\,\.\/\:\;\<\=\>\?\@\\\^\{\|\}\~\`。，、；：？！…—·ˉ¨‘’“”々～‖∶＂＇｀｜〃〔〕〈〉《》「」『』．〖〗【】（）［］｛｝]/g,"").replace(/^\-+/,"").replace(/\-+$/,""))}export{au as activate};
/*! Bundled license information:

dompurify/dist/purify.es.mjs:
  (*! @license DOMPurify 3.2.4 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE *)
*/
