/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/(async function(){performance.mark("code/didStartRenderer");const p=window.vscode,h=p.process;function y(o){performance.mark("code/willShowPartsSplash");let i=o.partsSplash;i&&(o.autoDetectHighContrast&&o.colorScheme.highContrast?(o.colorScheme.dark&&i.baseTheme!=="hc-black"||!o.colorScheme.dark&&i.baseTheme!=="hc-light")&&(i=void 0):o.autoDetectColorScheme&&(o.colorScheme.dark&&i.baseTheme!=="vs-dark"||!o.colorScheme.dark&&i.baseTheme!=="vs")&&(i=void 0)),i&&o.extensionDevelopmentPath&&(i.layoutInfo=void 0);let l,d,n;i?(l=i.baseTheme,d=i.colorInfo.editorBackground,n=i.colorInfo.foreground):o.autoDetectHighContrast&&o.colorScheme.highContrast?o.colorScheme.dark?(l="hc-black",d="#000000",n="#FFFFFF"):(l="hc-light",d="#FFFFFF",n="#000000"):o.autoDetectColorScheme&&(o.colorScheme.dark?(l="vs-dark",d="#1E1E1E",n="#CCCCCC"):(l="vs",d="#FFFFFF",n="#000000"));const c=document.createElement("style");if(c.className="initialShellColors",window.document.head.appendChild(c),c.textContent=`body {	background-color: ${d}; color: ${n}; margin: 0; padding: 0; }`,typeof i?.zoomLevel=="number"&&typeof p?.webFrame?.setZoomLevel=="function"&&p.webFrame.setZoomLevel(i.zoomLevel),i?.layoutInfo){const{layoutInfo:t,colorInfo:s}=i,a=document.createElement("div");if(a.id="monaco-parts-splash",a.className=l??"vs-dark",t.windowBorder&&s.windowBorder){const e=document.createElement("div");e.style.position="absolute",e.style.width="calc(100vw - 2px)",e.style.height="calc(100vh - 2px)",e.style.zIndex="1",e.style.border="1px solid var(--window-border-color)",e.style.setProperty("--window-border-color",s.windowBorder),t.windowBorderRadius&&(e.style.borderRadius=t.windowBorderRadius),a.appendChild(e)}if(t.auxiliarySideBarWidth=Math.min(t.auxiliarySideBarWidth,window.innerWidth-(t.activityBarWidth+t.editorPartMinWidth+t.sideBarWidth)),t.sideBarWidth=Math.min(t.sideBarWidth,window.innerWidth-(t.activityBarWidth+t.editorPartMinWidth+t.auxiliarySideBarWidth)),t.titleBarHeight>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width="100%",e.style.height=`${t.titleBarHeight}px`,e.style.left="0",e.style.top="0",e.style.backgroundColor=`${s.titleBarBackground}`,e.style["-webkit-app-region"]="drag",a.appendChild(e),s.titleBarBorder){const r=document.createElement("div");r.style.position="absolute",r.style.width="100%",r.style.height="1px",r.style.left="0",r.style.bottom="0",r.style.borderBottom=`1px solid ${s.titleBarBorder}`,e.appendChild(r)}}if(t.activityBarWidth>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width=`${t.activityBarWidth}px`,e.style.height=`calc(100% - ${t.titleBarHeight+t.statusBarHeight}px)`,e.style.top=`${t.titleBarHeight}px`,t.sideBarSide==="left"?e.style.left="0":e.style.right="0",e.style.backgroundColor=`${s.activityBarBackground}`,a.appendChild(e),s.activityBarBorder){const r=document.createElement("div");r.style.position="absolute",r.style.width="1px",r.style.height="100%",r.style.top="0",t.sideBarSide==="left"?(r.style.right="0",r.style.borderRight=`1px solid ${s.activityBarBorder}`):(r.style.left="0",r.style.borderLeft=`1px solid ${s.activityBarBorder}`),e.appendChild(r)}}if(t.sideBarWidth>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width=`${t.sideBarWidth}px`,e.style.height=`calc(100% - ${t.titleBarHeight+t.statusBarHeight}px)`,e.style.top=`${t.titleBarHeight}px`,t.sideBarSide==="left"?e.style.left=`${t.activityBarWidth}px`:e.style.right=`${t.activityBarWidth}px`,e.style.backgroundColor=`${s.sideBarBackground}`,a.appendChild(e),s.sideBarBorder){const r=document.createElement("div");r.style.position="absolute",r.style.width="1px",r.style.height="100%",r.style.top="0",r.style.right="0",t.sideBarSide==="left"?r.style.borderRight=`1px solid ${s.sideBarBorder}`:(r.style.left="0",r.style.borderLeft=`1px solid ${s.sideBarBorder}`),e.appendChild(r)}}if(t.auxiliarySideBarWidth>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width=`${t.auxiliarySideBarWidth}px`,e.style.height=`calc(100% - ${t.titleBarHeight+t.statusBarHeight}px)`,e.style.top=`${t.titleBarHeight}px`,t.sideBarSide==="left"?e.style.right="0":e.style.left="0",e.style.backgroundColor=`${s.sideBarBackground}`,a.appendChild(e),s.sideBarBorder){const r=document.createElement("div");r.style.position="absolute",r.style.width="1px",r.style.height="100%",r.style.top="0",t.sideBarSide==="left"?(r.style.left="0",r.style.borderLeft=`1px solid ${s.sideBarBorder}`):(r.style.right="0",r.style.borderRight=`1px solid ${s.sideBarBorder}`),e.appendChild(r)}}if(t.statusBarHeight>0){const e=document.createElement("div");if(e.style.position="absolute",e.style.width="100%",e.style.height=`${t.statusBarHeight}px`,e.style.bottom="0",e.style.left="0",o.workspace&&s.statusBarBackground?e.style.backgroundColor=s.statusBarBackground:!o.workspace&&s.statusBarNoFolderBackground&&(e.style.backgroundColor=s.statusBarNoFolderBackground),a.appendChild(e),s.statusBarBorder){const r=document.createElement("div");r.style.position="absolute",r.style.width="100%",r.style.height="1px",r.style.top="0",r.style.borderTop=`1px solid ${s.statusBarBorder}`,e.appendChild(r)}}window.document.body.appendChild(a)}performance.mark("code/didShowPartsSplash")}async function m(o,i){const l=await u();i?.beforeImport?.(l);const{enableDeveloperKeybindings:d,removeDeveloperKeybindingsAfterLoad:n,developerDeveloperKeybindingsDisposable:c,forceDisableShowDevtoolsOnError:t}=f(l,i);b(l);const s=new URL(`${w(l.appRoot,{isWindows:h.platform==="win32",scheme:"vscode-file",fallbackAuthority:"vscode-app"})}/out/`);globalThis._VSCODE_FILE_ROOT=s.toString(),B(l,s);try{const a=await import(new URL(`${o}.js`,s).href);return c&&n&&c(),{result:a,configuration:l}}catch(a){throw g(a,d&&!t),a}}async function u(){const o=setTimeout(()=>{console.error("[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...")},1e4);performance.mark("code/willWaitForWindowConfig");const i=await p.context.resolveConfiguration();return performance.mark("code/didWaitForWindowConfig"),clearTimeout(o),i}function f(o,i){const{forceEnableDeveloperKeybindings:l,disallowReloadKeybinding:d,removeDeveloperKeybindingsAfterLoad:n,forceDisableShowDevtoolsOnError:c}=typeof i?.configureDeveloperSettings=="function"?i.configureDeveloperSettings(o):{forceEnableDeveloperKeybindings:!1,disallowReloadKeybinding:!1,removeDeveloperKeybindingsAfterLoad:!1,forceDisableShowDevtoolsOnError:!1},s=!!(!!h.env.VSCODE_DEV||l);let a;return s&&(a=v(d)),{enableDeveloperKeybindings:s,removeDeveloperKeybindingsAfterLoad:n,developerDeveloperKeybindingsDisposable:a,forceDisableShowDevtoolsOnError:c}}function v(o){const i=p.ipcRenderer,l=function(s){return[s.ctrlKey?"ctrl-":"",s.metaKey?"meta-":"",s.altKey?"alt-":"",s.shiftKey?"shift-":"",s.keyCode].join("")},d=h.platform==="darwin"?"meta-alt-73":"ctrl-shift-73",n="123",c=h.platform==="darwin"?"meta-82":"ctrl-82";let t=function(s){const a=l(s);a===d||a===n?i.send("vscode:toggleDevTools"):a===c&&!o&&i.send("vscode:reloadWindow")};return window.addEventListener("keydown",t),function(){t&&(window.removeEventListener("keydown",t),t=void 0)}}function b(o){globalThis._VSCODE_NLS_MESSAGES=o.nls.messages,globalThis._VSCODE_NLS_LANGUAGE=o.nls.language;let i=o.nls.language||"en";i==="zh-tw"?i="zh-Hant":i==="zh-cn"&&(i="zh-Hans"),window.document.documentElement.setAttribute("lang",i)}function g(o,i){i&&p.ipcRenderer.send("vscode:openDevTools"),console.error(`[uncaught exception]: ${o}`),o&&typeof o!="string"&&o.stack&&console.error(o.stack)}function w(o,i){let l=o.replace(/\\/g,"/");l.length>0&&l.charAt(0)!=="/"&&(l=`/${l}`);let d;return i.isWindows&&l.startsWith("//")?d=encodeURI(`${i.scheme||"file"}:${l}`):d=encodeURI(`${i.scheme||"file"}://${i.fallbackAuthority||""}${l}`),d.replace(/#/g,"%23")}function B(o,i){if(Array.isArray(o.cssModules)&&o.cssModules.length>0){performance.mark("code/willAddCssLoader");const l=document.createElement("style");l.type="text/css",l.media="screen",l.id="vscode-css-loading",window.document.head.appendChild(l),globalThis._VSCODE_CSS_LOAD=function(s){l.textContent+=`@import url(${s});
`};const d={imports:{}};for(const s of o.cssModules){const a=new URL(s,i).href,e=`globalThis._VSCODE_CSS_LOAD('${a}');
`,r=new Blob([e],{type:"application/javascript"});d.imports[a]=URL.createObjectURL(r)}const n=window.trustedTypes?.createPolicy("vscode-bootstrapImportMap",{createScript(s){return s}}),c=JSON.stringify(d,void 0,2),t=document.createElement("script");t.type="importmap",t.setAttribute("nonce","0c6a828f1297"),t.textContent=n?.createScript(c)??c,window.document.head.appendChild(t),performance.mark("code/didAddCssLoader")}}const{result:S,configuration:D}=await m("vs/workbench/workbench.desktop.main",{configureDeveloperSettings:function(o){return{forceDisableShowDevtoolsOnError:typeof o.extensionTestsPath=="string"||o["enable-smoke-test-driver"]===!0,forceEnableDeveloperKeybindings:Array.isArray(o.extensionDevelopmentPath)&&o.extensionDevelopmentPath.length>0,removeDeveloperKeybindingsAfterLoad:!0}},beforeImport:function(o){y(o),Object.defineProperty(window,"vscodeWindowId",{get:()=>o.windowId}),window.requestIdleCallback(()=>{const i=document.createElement("canvas");i.getContext("2d")?.clearRect(0,0,i.width,i.height),i.remove()},{timeout:50}),performance.mark("code/willLoadWorkbenchMain")}});performance.mark("code/didLoadWorkbenchMain"),S.main(D)})();

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/18e3a1ec544e6907be1e944a94c496e302073435/core/vs/code/electron-sandbox/workbench/workbench.js.map
