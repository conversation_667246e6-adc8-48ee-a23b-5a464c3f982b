<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>pfm_app_url</key>
    <string>https://code.visualstudio.com/</string>
    <key>pfm_description</key>
    <string>Visual Studio Code Managed Settings</string>
    <key>pfm_documentation_url</key>
    <string>https://code.visualstudio.com/docs/setup/enterprise</string>
    <key>pfm_domain</key>
    <string>com.microsoft.VSCode</string>
    <key>pfm_format_version</key>
    <integer>1</integer>
    <key>pfm_interaction</key>
    <string>combined</string>
    <key>pfm_last_modified</key>
    <date>2025-06-18T13:51:24Z</date>
    <key>pfm_platforms</key>
    <array>
        <string>macOS</string>
    </array>
    <key>pfm_subkeys</key>
    <array>
	
		<dict>
			<key>pfm_default</key>
			<string>Configure Visual Studio Code</string>
			<key>pfm_name</key>
			<string>PayloadDescription</string>
			<key>pfm_title</key>
			<string>Payload Description</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>Visual Studio Code</string>
			<key>pfm_name</key>
			<string>PayloadDisplayName</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Display Name</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>com.microsoft.VSCode</string>
			<key>pfm_name</key>
			<string>PayloadIdentifier</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Identifier</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>com.microsoft.VSCode</string>
			<key>pfm_name</key>
			<string>PayloadType</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Type</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string></string>
			<key>pfm_name</key>
			<string>PayloadUUID</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload UUID</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<integer>1</integer>
			<key>pfm_name</key>
			<string>PayloadVersion</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
			</array>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Version</string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>Microsoft</string>
			<key>pfm_name</key>
			<string>PayloadOrganization</string>
			<key>pfm_title</key>
			<string>Payload Organization</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
	<dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>マーケットプレース サービスの URL を構成してから次に接続します:</string>
<key>pfm_name</key>
<string>ExtensionGalleryServiceUrl</string>
<key>pfm_title</key>
<string>ExtensionGalleryServiceUrl</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>ツールの使用を自動的に承認するかどうかを制御します。</string>
<key>pfm_name</key>
<string>ChatToolsAutoApprove</string>
<key>pfm_title</key>
<string>ChatToolsAutoApprove</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>モデル コンテキスト プロトコル サーバーとの統合を有効にし、追加のツールと機能を提供します。</string>
<key>pfm_name</key>
<string>ChatMCP</string>
<key>pfm_title</key>
<string>ChatMCP</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>サード パーティの拡張機能によって提供されるツールの使用を有効にします。</string>
<key>pfm_name</key>
<string>ChatAgentExtensionTools</string>
<key>pfm_title</key>
<string>ChatAgentExtensionTools</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>{0} のエージェント モードを有効にします。これを有効にすると、ビューのドロップダウンからエージェント モードをアクティブ化できます。</string>
<key>pfm_name</key>
<string>ChatAgentMode</string>
<key>pfm_title</key>
<string>ChatAgentMode</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>接続する MCP ギャラリー サービス URL を構成する</string>
<key>pfm_name</key>
<string>McpGalleryServiceUrl</string>
<key>pfm_title</key>
<string>McpGalleryServiceUrl</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>チャット、編集、インライン チャット セッションで再利用可能なプロンプト ファイルと指示ファイルを有効にします。</string>
<key>pfm_name</key>
<string>ChatPromptFiles</string>
<key>pfm_title</key>
<string>ChatPromptFiles</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>使用が許可される拡張機能のリストを指定します。これにより、承認されていない拡張機能の使用が制限され、安全で一貫性のある開発環境が維持されます。詳細情報: https://code.visualstudio.com/docs/setup/enterprise#_configure-allowed-extensions</string>
<key>pfm_name</key>
<string>AllowedExtensions</string>
<key>pfm_title</key>
<string>AllowedExtensions</string>
<key>pfm_type</key>
<string>string</string>

</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>テレメトリのレベルを制御します。</string>
<key>pfm_name</key>
<string>TelemetryLevel</string>
<key>pfm_title</key>
<string>TelemetryLevel</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Copilot Chat などの機能について、問題報告機能、アンケート、フィードバック オプションなどのフィードバック メカニズムを有効にします。</string>
<key>pfm_name</key>
<string>EnableFeedback</string>
<key>pfm_title</key>
<string>EnableFeedback</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string>none</string>
<key>pfm_description</key>
<string>自動更新を受け取るかどうかを構成します。変更後に再起動が必要です。更新プログラムは Microsoft のオンライン サービスから取得されます。</string>
<key>pfm_name</key>
<string>UpdateMode</string>
<key>pfm_title</key>
<string>UpdateMode</string>
<key>pfm_type</key>
<string>string</string>
<key>pfm_range_list</key>
<array>
	<string>none</string>
	<string>manual</string>
	<string>start</string>
	<string>default</string>
</array>
</dict>
    </array>
    <key>pfm_title</key>
    <string>Visual Studio Code</string>
    <key>pfm_unique</key>
    <true/>
    <key>pfm_version</key>
    <integer>1</integer>
</dict>
</plist>