<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>pfm_app_url</key>
    <string>https://code.visualstudio.com/</string>
    <key>pfm_description</key>
    <string>Visual Studio Code Managed Settings</string>
    <key>pfm_documentation_url</key>
    <string>https://code.visualstudio.com/docs/setup/enterprise</string>
    <key>pfm_domain</key>
    <string>com.microsoft.VSCode</string>
    <key>pfm_format_version</key>
    <integer>1</integer>
    <key>pfm_interaction</key>
    <string>combined</string>
    <key>pfm_last_modified</key>
    <date>2025-06-18T13:51:24Z</date>
    <key>pfm_platforms</key>
    <array>
        <string>macOS</string>
    </array>
    <key>pfm_subkeys</key>
    <array>
	
		<dict>
			<key>pfm_default</key>
			<string>Configure Visual Studio Code</string>
			<key>pfm_name</key>
			<string>PayloadDescription</string>
			<key>pfm_title</key>
			<string>Payload Description</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>Visual Studio Code</string>
			<key>pfm_name</key>
			<string>PayloadDisplayName</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Display Name</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>com.microsoft.VSCode</string>
			<key>pfm_name</key>
			<string>PayloadIdentifier</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Identifier</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>com.microsoft.VSCode</string>
			<key>pfm_name</key>
			<string>PayloadType</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Type</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string></string>
			<key>pfm_name</key>
			<string>PayloadUUID</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload UUID</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<integer>1</integer>
			<key>pfm_name</key>
			<string>PayloadVersion</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
			</array>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Version</string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>Microsoft</string>
			<key>pfm_name</key>
			<string>PayloadOrganization</string>
			<key>pfm_title</key>
			<string>Payload Organization</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
	<dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>Nakonfigurovat adresu URL služby Marketplace pro připojení k</string>
<key>pfm_name</key>
<string>ExtensionGalleryServiceUrl</string>
<key>pfm_title</key>
<string>ExtensionGalleryServiceUrl</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Určuje, jestli by mělo být použití nástroje automaticky schváleno.</string>
<key>pfm_name</key>
<string>ChatToolsAutoApprove</string>
<key>pfm_title</key>
<string>ChatToolsAutoApprove</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Umožňuje integraci se servery protokolu kontextu modelu, aby bylo možné poskytovat další nástroje a funkce.</string>
<key>pfm_name</key>
<string>ChatMCP</string>
<key>pfm_title</key>
<string>ChatMCP</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Umožňuje používat nástroje z rozšíření třetích stran.</string>
<key>pfm_name</key>
<string>ChatAgentExtensionTools</string>
<key>pfm_title</key>
<string>ChatAgentExtensionTools</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Povolit režim agenta pro {0}. Pokud je tato možnost povolená, můžete režim agenta aktivovat prostřednictvím rozevíracího seznamu v zobrazení.</string>
<key>pfm_name</key>
<string>ChatAgentMode</string>
<key>pfm_title</key>
<string>ChatAgentMode</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>Nakonfigurovat adresu URL služby Galerie MCP pro připojení k</string>
<key>pfm_name</key>
<string>McpGalleryServiceUrl</string>
<key>pfm_title</key>
<string>McpGalleryServiceUrl</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Povolí opakovaně použitelné soubory výzev a pokynů v relacích Chatu, Edits a vloženého chatu.</string>
<key>pfm_name</key>
<string>ChatPromptFiles</string>
<key>pfm_title</key>
<string>ChatPromptFiles</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>Zadejte seznam rozšíření, která mohou být použita. Pomáhá to udržovat zabezpečené a konzistentní vývojové prostředí tím, že je omezeno používání neautorizovaných rozšíření. Další informace: https://code.visualstudio.com/docs/setup/enterprise#_configure-allowed-extensions</string>
<key>pfm_name</key>
<string>AllowedExtensions</string>
<key>pfm_title</key>
<string>AllowedExtensions</string>
<key>pfm_type</key>
<string>string</string>

</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>Řídí úroveň telemetrie.</string>
<key>pfm_name</key>
<string>TelemetryLevel</string>
<key>pfm_title</key>
<string>TelemetryLevel</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Umožňuje povolit mechanismy zpětné vazby, jako jsou sestavy problémů, průzkumy a možnosti zpětné vazby, ve funkcích, jako je Copilot Chat.</string>
<key>pfm_name</key>
<string>EnableFeedback</string>
<key>pfm_title</key>
<string>EnableFeedback</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string>none</string>
<key>pfm_description</key>
<string>Umožňuje nakonfigurovat, jestli budete dostávat automatické aktualizace. Po změně vyžaduje restart. Aktualizace se načítají z online služby Microsoftu.</string>
<key>pfm_name</key>
<string>UpdateMode</string>
<key>pfm_title</key>
<string>UpdateMode</string>
<key>pfm_type</key>
<string>string</string>
<key>pfm_range_list</key>
<array>
	<string>none</string>
	<string>manual</string>
	<string>start</string>
	<string>default</string>
</array>
</dict>
    </array>
    <key>pfm_title</key>
    <string>Visual Studio Code</string>
    <key>pfm_unique</key>
    <true/>
    <key>pfm_version</key>
    <integer>1</integer>
</dict>
</plist>