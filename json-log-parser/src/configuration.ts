import * as vscode from 'vscode';

export class ConfigurationManager {
    private static readonly SECTION = 'jsonLogParser';

    public isEnabled(): boolean {
        return this.getConfig().get<boolean>('enabled', true);
    }

    public setEnabled(enabled: boolean): void {
        this.getConfig().update('enabled', enabled, vscode.ConfigurationTarget.Global);
    }

    public isAutoDetectEnabled(): boolean {
        return this.getConfig().get<boolean>('autoDetect', true);
    }

    public getHighlightColor(): string {
        return this.getConfig().get<string>('highlightColor', '#4CAF50');
    }

    public getMaxLogSize(): number {
        return this.getConfig().get<number>('maxLogSize', 10000);
    }

    public shouldShowInSidebar(): boolean {
        return this.getConfig().get<boolean>('showInSidebar', true);
    }

    public shouldAutoDetectInFiles(): boolean {
        return this.getConfig().get<boolean>('autoDetectInFiles', true);
    }

    public shouldAutoDetectClipboard(): boolean {
        return this.getConfig().get<boolean>('autoDetectClipboard', true);
    }

    public shouldAutoDetectFiles(): boolean {
        return this.getConfig().get<boolean>('autoDetectFiles', true);
    }



    public reload(): void {
        // Configuration is automatically reloaded by VS Code
        // This method can be used for any custom reload logic
    }

    private getConfig(): vscode.WorkspaceConfiguration {
        return vscode.workspace.getConfiguration(ConfigurationManager.SECTION);
    }
}
