import * as vscode from 'vscode';
import { ParsedJsonResult } from './jsonParser';

interface JsonLogEntry {
    id: string;
    timestamp: Date;
    source: string;
    results: ParsedJsonResult[];
}

export class JsonWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'jsonLogParser.parsedJsonView';
    
    private _view?: vscode.WebviewView;
    private _logs: JsonLogEntry[] = [];
    private _maxLogs = 100;

    constructor(private readonly _extensionUri: vscode.Uri) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(data => {
            switch (data.type) {
                case 'copy':
                    vscode.env.clipboard.writeText(data.text);
                    vscode.window.showInformationMessage('Copied to clipboard');
                    break;
                case 'clear':
                    this.clearContent();
                    break;
            }
        });
    }

    public addParsedJson(results: ParsedJsonResult[], source: string): void {
        const entry: JsonLogEntry = {
            id: Date.now().toString(),
            timestamp: new Date(),
            source,
            results
        };

        this._logs.unshift(entry); // Add to beginning

        // Keep only the most recent logs
        if (this._logs.length > this._maxLogs) {
            this._logs = this._logs.slice(0, this._maxLogs);
        }

        this._updateWebview();
    }

    public updateContent(results: ParsedJsonResult[]): void {
        this.addParsedJson(results, 'Manual Selection');
    }

    public clearContent(): void {
        this._logs = [];
        this._updateWebview();
    }

    private _updateWebview(): void {
        if (this._view) {
            this._view.webview.postMessage({
                type: 'update',
                logs: this._logs
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parsed JSON Logs</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 10px;
        }
        
        .log-entry {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        
        .log-header {
            background-color: var(--vscode-editor-lineHighlightBackground);
            padding: 8px 12px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .log-source {
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        
        .log-timestamp {
            font-size: 0.8em;
            color: var(--vscode-descriptionForeground);
        }
        
        .json-result {
            margin: 10px;
            border-left: 3px solid var(--vscode-textLink-foreground);
            padding-left: 10px;
        }
        
        .json-content {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 3px;
            padding: 10px;
            margin: 5px 0;
            font-family: var(--vscode-editor-font-family);
            font-size: 0.9em;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .json-original {
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }
        
        .json-formatted {
            color: var(--vscode-editor-foreground);
        }
        
        .actions {
            margin-top: 5px;
        }
        
        .btn {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 4px 8px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 0.8em;
            margin-right: 5px;
        }
        
        .btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .btn-secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        
        .btn-secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        
        .empty-state {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            padding: 20px;
            background-color: var(--vscode-editor-background);
            border-radius: 4px;
            margin: 10px;
        }

        .clear-all {
            position: sticky;
            top: 0;
            background-color: var(--vscode-editor-background);
            padding: 10px;
            border-bottom: 1px solid var(--vscode-panel-border);
            margin-bottom: 10px;
            text-align: center;
        }

        .panel-header {
            background-color: var(--vscode-titleBar-activeBackground);
            color: var(--vscode-titleBar-activeForeground);
            padding: 8px 12px;
            font-weight: bold;
            border-bottom: 1px solid var(--vscode-panel-border);
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="panel-header">
        📋 JSON Parser Results
    </div>

    <div class="clear-all">
        <button class="btn btn-secondary" onclick="clearAll()">Clear All Results</button>
    </div>

    <div id="content">
        <div class="empty-state">
            <h3>🔍 No JSON parsed yet</h3>
            <p>Select text containing escaped JSON and use "Parse Selected JSON" command.</p>
            <p>Results will appear here in the panel below the terminal.</p>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.type) {
                case 'update':
                    updateContent(message.logs);
                    break;
            }
        });
        
        function updateContent(logs) {
            const content = document.getElementById('content');
            
            if (logs.length === 0) {
                content.innerHTML = \`
                    <div class="empty-state">
                        <p>No parsed JSON logs yet.</p>
                        <p>JSON will appear here when detected in terminal output or when manually parsed.</p>
                    </div>
                \`;
                return;
            }
            
            content.innerHTML = logs.map(log => \`
                <div class="log-entry">
                    <div class="log-header">
                        <span class="log-source">\${log.source}</span>
                        <span class="log-timestamp">\${new Date(log.timestamp).toLocaleTimeString()}</span>
                    </div>
                    \${log.results.map((result, index) => \`
                        <div class="json-result">
                            <div class="json-content json-formatted">\${result.formatted}</div>
                            <div class="json-content json-original">\${result.original}</div>
                            <div class="actions">
                                <button class="btn" onclick="copyText('\${escapeHtml(result.formatted)}')">Copy Formatted</button>
                                <button class="btn btn-secondary" onclick="copyText('\${escapeHtml(result.original)}')">Copy Original</button>
                            </div>
                        </div>
                    \`).join('')}
                </div>
            \`).join('');
        }
        
        function copyText(text) {
            vscode.postMessage({
                type: 'copy',
                text: text
            });
        }
        
        function clearAll() {
            vscode.postMessage({
                type: 'clear'
            });
        }
        
        function escapeHtml(text) {
            return text.replace(/'/g, "\\\\'").replace(/"/g, '\\\\"');
        }
    </script>
</body>
</html>`;
    }
}
