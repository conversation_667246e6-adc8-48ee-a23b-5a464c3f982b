import * as vscode from 'vscode';
import { JsonParser } from './jsonParser';
import { TerminalMonitor } from './terminalMonitor';
import { OutputMonitor } from './outputMonitor';
import { DocumentMonitor } from './documentMonitor';
import { DecorationManager } from './decorationManager';
import { JsonWebviewProvider } from './webviewProvider';
import { ConfigurationManager } from './configuration';
import { DedicatedPanelManager } from './dedicatedPanelManager';
import { ClipboardMonitor } from './clipboardMonitor';
import { FileSystemMonitor } from './fileSystemMonitor';

export function activate(context: vscode.ExtensionContext) {
    console.log('JSON Log Parser extension is now active!');

    // Create output channel for displaying results
    const outputChannel = vscode.window.createOutputChannel('JSON Parser Results');

    // Initialize core components
    const config = new ConfigurationManager();
    const jsonParser = new JsonParser(config);
    const decorationManager = new DecorationManager(config);
    const webviewProvider = new JsonWebviewProvider(context.extensionUri);
    const dedicatedPanel = new DedicatedPanelManager(context.extensionUri);

    // Initialize all monitors with dedicated panel reference
    const terminalMonitor = new TerminalMonitor(jsonParser, decorationManager, webviewProvider, dedicatedPanel);
    const outputMonitor = new OutputMonitor(jsonParser, decorationManager, webviewProvider, dedicatedPanel);
    const documentMonitor = new DocumentMonitor(jsonParser, decorationManager, webviewProvider, config, dedicatedPanel);
    const clipboardMonitor = new ClipboardMonitor(jsonParser, webviewProvider, config, dedicatedPanel);
    const fileSystemMonitor = new FileSystemMonitor(jsonParser, webviewProvider, config, dedicatedPanel);

    // Register webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('jsonLogParser.parsedJsonView', webviewProvider),
        outputChannel
    );

    // Register commands
    const parseSelectionCommand = vscode.commands.registerCommand('jsonLogParser.parseSelection', () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        
        if (!selectedText) {
            vscode.window.showWarningMessage('No text selected');
            return;
        }

        console.log(`JSON Log Parser: Parsing selected text: ${selectedText.substring(0, 100)}...`);

        const parsedJson = jsonParser.parseEscapedJson(selectedText);
        console.log(`JSON Log Parser: Found ${parsedJson.length} JSON objects`);

        if (parsedJson.length > 0) {
            // Update both display methods
            webviewProvider.updateContent(parsedJson);
            dedicatedPanel.addParsedJson(parsedJson, 'Manual Selection');

            // Display results in output channel (secondary method)
            outputChannel.clear();
            outputChannel.appendLine('🔍 JSON Parser Results');
            outputChannel.appendLine('='.repeat(50));
            outputChannel.appendLine(`Found ${parsedJson.length} JSON object(s)\n`);

            parsedJson.forEach((result, index) => {
                outputChannel.appendLine(`📋 JSON Object ${index + 1}:`);
                outputChannel.appendLine('─'.repeat(30));
                outputChannel.appendLine('Original:');
                outputChannel.appendLine(result.original);
                outputChannel.appendLine('\nFormatted:');
                outputChannel.appendLine(result.formatted);
                outputChannel.appendLine('\n' + '='.repeat(50) + '\n');
            });

            // Show notification with both options
            vscode.window.showInformationMessage(
                `Found ${parsedJson.length} JSON object(s) - Check the dedicated "JSON Parser" tab or "JSON Parser Results" in Output panel`,
                'Show Dedicated Tab',
                'Show Output Panel'
            ).then(selection => {
                if (selection === 'Show Dedicated Tab') {
                    dedicatedPanel.showPanel();
                } else if (selection === 'Show Output Panel') {
                    outputChannel.show(true);
                }
            });
        } else {
            vscode.window.showInformationMessage('No escaped JSON found in selection');
        }
    });

    const toggleParsingCommand = vscode.commands.registerCommand('jsonLogParser.toggleParsing', () => {
        const currentState = config.isEnabled();
        config.setEnabled(!currentState);
        vscode.window.showInformationMessage(
            `JSON parsing ${!currentState ? 'enabled' : 'disabled'}`
        );
    });

    const clearCacheCommand = vscode.commands.registerCommand('jsonLogParser.clearCache', () => {
        webviewProvider.clearContent();
        vscode.window.showInformationMessage('Parsed JSON cache cleared');
    });

    const showResultsCommand = vscode.commands.registerCommand('jsonLogParser.showResults', () => {
        // Show the output channel with results
        outputChannel.show(true);
    });

    const openDedicatedPanelCommand = vscode.commands.registerCommand('jsonLogParser.openDedicatedPanel', () => {
        // Show the dedicated panel
        console.log('JSON Log Parser: openDedicatedPanel command called');
        dedicatedPanel.showPanel();
    });

    const testPanelCommand = vscode.commands.registerCommand('jsonLogParser.testPanel', () => {
        // Test the panel with sample data
        console.log('JSON Log Parser: testPanel command called');
        const testResults = [{
            original: '{"test":"value","number":123}',
            parsed: { test: "value", number: 123 },
            formatted: '{\n  "test": "value",\n  "number": 123\n}',
            startIndex: 0,
            endIndex: 25,
            isValid: true
        }];
        dedicatedPanel.addParsedJson(testResults, 'Test Command');
    });

    const parseClipboardCommand = vscode.commands.registerCommand('jsonLogParser.parseClipboard', async () => {
        try {
            const clipboardText = await vscode.env.clipboard.readText();
            if (!clipboardText || !clipboardText.trim()) {
                vscode.window.showWarningMessage('Clipboard is empty');
                return;
            }

            const parsedResults = jsonParser.parseEscapedJson(clipboardText);
            if (parsedResults.length > 0) {
                webviewProvider.addParsedJson(parsedResults, 'Clipboard Content');
                dedicatedPanel.addParsedJson(parsedResults, 'Clipboard Content');

                vscode.window.showInformationMessage(
                    `Found ${parsedResults.length} JSON object(s) in clipboard`,
                    'Show Dedicated Tab'
                ).then(selection => {
                    if (selection === 'Show Dedicated Tab') {
                        dedicatedPanel.showPanel();
                    }
                });
            } else {
                vscode.window.showInformationMessage('No JSON found in clipboard content');
            }
        } catch (error) {
            vscode.window.showErrorMessage('Failed to read clipboard content');
        }
    });

    const simulateTerminalJsonCommand = vscode.commands.registerCommand('jsonLogParser.simulateTerminalJson', () => {
        // Simulate terminal JSON output for testing
        const sampleJsonOutputs = [
            'API Request: "{\\"method\\":\\"POST\\",\\"url\\":\\"https://api.example.com\\",\\"data\\":{\\"user_id\\":123,\\"action\\":\\"login\\"}}"',
            'Response received: {"status":200,"data":{"token":"abc123","expires_in":3600,"user":{"id":123,"name":"John Doe","email":"<EMAIL>"}}}',
            'Error occurred: \'{"error":"validation_failed","details":{"field":"email","message":"Invalid email format"},"timestamp":"2025-06-21T13:45:00Z"}\'',
            'Log entry: [{"level":"info","message":"User authenticated","metadata":{"ip":"***********","user_agent":"Mozilla/5.0"}}]'
        ];

        sampleJsonOutputs.forEach((output, index) => {
            const parsedResults = jsonParser.parseEscapedJson(output);
            if (parsedResults.length > 0) {
                webviewProvider.addParsedJson(parsedResults, `Simulated Terminal Output ${index + 1}`);
                dedicatedPanel.addParsedJson(parsedResults, `Simulated Terminal Output ${index + 1}`);
            }
        });

        vscode.window.showInformationMessage(
            `Simulated ${sampleJsonOutputs.length} terminal outputs with JSON`,
            'Show Dedicated Tab'
        ).then(selection => {
            if (selection === 'Show Dedicated Tab') {
                dedicatedPanel.showPanel();
            }
        });
    });

    // Add subscriptions
    context.subscriptions.push(
        parseSelectionCommand,
        toggleParsingCommand,
        clearCacheCommand,
        showResultsCommand,
        openDedicatedPanelCommand,
        testPanelCommand,
        parseClipboardCommand,
        simulateTerminalJsonCommand,
        terminalMonitor,
        outputMonitor,
        documentMonitor,
        clipboardMonitor,
        fileSystemMonitor,
        decorationManager,
        dedicatedPanel
    );

    // Start monitoring if enabled
    if (config.isEnabled()) {
        terminalMonitor.startMonitoring();
        outputMonitor.startMonitoring();
        documentMonitor.startMonitoring();
        clipboardMonitor.startMonitoring();
        fileSystemMonitor.startMonitoring();

        // Show welcome message with automation info
        vscode.window.showInformationMessage(
            '🚀 JSON Log Parser: Auto-monitoring enabled for clipboard, files, and terminals!',
            'Show Settings',
            'Test Now'
        ).then(selection => {
            if (selection === 'Show Settings') {
                vscode.commands.executeCommand('workbench.action.openSettings', 'jsonLogParser');
            } else if (selection === 'Test Now') {
                vscode.commands.executeCommand('jsonLogParser.simulateTerminalJson');
            }
        });
    }

    // Listen for configuration changes
    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('jsonLogParser')) {
                config.reload();
                if (config.isEnabled()) {
                    terminalMonitor.startMonitoring();
                    outputMonitor.startMonitoring();
                    documentMonitor.startMonitoring();
                    clipboardMonitor.startMonitoring();
                    fileSystemMonitor.startMonitoring();
                } else {
                    terminalMonitor.stopMonitoring();
                    outputMonitor.stopMonitoring();
                    documentMonitor.stopMonitoring();
                    clipboardMonitor.stopMonitoring();
                    fileSystemMonitor.stopMonitoring();
                }
            }
        })
    );
}

export function deactivate() {
    console.log('JSON Log Parser extension is now deactivated');
}
