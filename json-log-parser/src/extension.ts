import * as vscode from 'vscode';
import { JsonParser } from './jsonParser';
import { TerminalMonitor } from './terminalMonitor';
import { OutputMonitor } from './outputMonitor';
import { DocumentMonitor } from './documentMonitor';
import { DecorationManager } from './decorationManager';
import { JsonWebviewProvider } from './webviewProvider';
import { ConfigurationManager } from './configuration';
import { DedicatedPanelManager } from './dedicatedPanelManager';

export function activate(context: vscode.ExtensionContext) {
    console.log('JSON Log Parser extension is now active!');

    // Create output channel for displaying results
    const outputChannel = vscode.window.createOutputChannel('JSON Parser Results');

    // Initialize core components
    const config = new ConfigurationManager();
    const jsonParser = new JsonParser(config);
    const decorationManager = new DecorationManager(config);
    const webviewProvider = new JsonWebviewProvider(context.extensionUri);
    const dedicatedPanel = new DedicatedPanelManager(context.extensionUri);

    // Initialize monitors with dedicated panel reference
    const terminalMonitor = new TerminalMonitor(jsonParser, decorationManager, webviewProvider, dedicatedPanel);
    const outputMonitor = new OutputMonitor(jsonParser, decorationManager, webviewProvider, dedicatedPanel);
    const documentMonitor = new DocumentMonitor(jsonParser, decorationManager, webviewProvider, config, dedicatedPanel);

    // Register webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('jsonLogParser.parsedJsonView', webviewProvider),
        outputChannel
    );

    // Register commands
    const parseSelectionCommand = vscode.commands.registerCommand('jsonLogParser.parseSelection', () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        
        if (!selectedText) {
            vscode.window.showWarningMessage('No text selected');
            return;
        }

        console.log(`JSON Log Parser: Parsing selected text: ${selectedText.substring(0, 100)}...`);

        const parsedJson = jsonParser.parseEscapedJson(selectedText);
        console.log(`JSON Log Parser: Found ${parsedJson.length} JSON objects`);

        if (parsedJson.length > 0) {
            // Update both display methods
            webviewProvider.updateContent(parsedJson);
            dedicatedPanel.addParsedJson(parsedJson, 'Manual Selection');

            // Display results in output channel (secondary method)
            outputChannel.clear();
            outputChannel.appendLine('🔍 JSON Parser Results');
            outputChannel.appendLine('='.repeat(50));
            outputChannel.appendLine(`Found ${parsedJson.length} JSON object(s)\n`);

            parsedJson.forEach((result, index) => {
                outputChannel.appendLine(`📋 JSON Object ${index + 1}:`);
                outputChannel.appendLine('─'.repeat(30));
                outputChannel.appendLine('Original:');
                outputChannel.appendLine(result.original);
                outputChannel.appendLine('\nFormatted:');
                outputChannel.appendLine(result.formatted);
                outputChannel.appendLine('\n' + '='.repeat(50) + '\n');
            });

            // Show notification with both options
            vscode.window.showInformationMessage(
                `Found ${parsedJson.length} JSON object(s) - Check the dedicated "JSON Parser" tab or "JSON Parser Results" in Output panel`,
                'Show Dedicated Tab',
                'Show Output Panel'
            ).then(selection => {
                if (selection === 'Show Dedicated Tab') {
                    dedicatedPanel.showPanel();
                } else if (selection === 'Show Output Panel') {
                    outputChannel.show(true);
                }
            });
        } else {
            vscode.window.showInformationMessage('No escaped JSON found in selection');
        }
    });

    const toggleParsingCommand = vscode.commands.registerCommand('jsonLogParser.toggleParsing', () => {
        const currentState = config.isEnabled();
        config.setEnabled(!currentState);
        vscode.window.showInformationMessage(
            `JSON parsing ${!currentState ? 'enabled' : 'disabled'}`
        );
    });

    const clearCacheCommand = vscode.commands.registerCommand('jsonLogParser.clearCache', () => {
        webviewProvider.clearContent();
        vscode.window.showInformationMessage('Parsed JSON cache cleared');
    });

    const showResultsCommand = vscode.commands.registerCommand('jsonLogParser.showResults', () => {
        // Show the output channel with results
        outputChannel.show(true);
    });

    const openDedicatedPanelCommand = vscode.commands.registerCommand('jsonLogParser.openDedicatedPanel', () => {
        // Show the dedicated panel
        console.log('JSON Log Parser: openDedicatedPanel command called');
        dedicatedPanel.showPanel();
    });

    const testPanelCommand = vscode.commands.registerCommand('jsonLogParser.testPanel', () => {
        // Test the panel with sample data
        console.log('JSON Log Parser: testPanel command called');
        const testResults = [{
            original: '{"test":"value","number":123}',
            parsed: { test: "value", number: 123 },
            formatted: '{\n  "test": "value",\n  "number": 123\n}',
            startIndex: 0,
            endIndex: 25,
            isValid: true
        }];
        dedicatedPanel.addParsedJson(testResults, 'Test Command');
    });

    // Add subscriptions
    context.subscriptions.push(
        parseSelectionCommand,
        toggleParsingCommand,
        clearCacheCommand,
        showResultsCommand,
        openDedicatedPanelCommand,
        testPanelCommand,
        terminalMonitor,
        outputMonitor,
        documentMonitor,
        decorationManager,
        dedicatedPanel
    );

    // Start monitoring if enabled
    if (config.isEnabled()) {
        terminalMonitor.startMonitoring();
        outputMonitor.startMonitoring();
        documentMonitor.startMonitoring();
    }

    // Listen for configuration changes
    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('jsonLogParser')) {
                config.reload();
                if (config.isEnabled()) {
                    terminalMonitor.startMonitoring();
                    outputMonitor.startMonitoring();
                    documentMonitor.startMonitoring();
                } else {
                    terminalMonitor.stopMonitoring();
                    outputMonitor.stopMonitoring();
                    documentMonitor.stopMonitoring();
                }
            }
        })
    );
}

export function deactivate() {
    console.log('JSON Log Parser extension is now deactivated');
}
