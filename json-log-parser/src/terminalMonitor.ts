import * as vscode from 'vscode';
import { JsonParser, ParsedJsonResult } from './jsonParser';
import { DecorationManager } from './decorationManager';
import { JsonWebviewProvider } from './webviewProvider';
import { DedicatedPanelManager } from './dedicatedPanelManager';

export class TerminalMonitor implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private isMonitoring = false;
    private terminalDataBuffer = new Map<vscode.Terminal, string>();
    private debounceTimers = new Map<vscode.Terminal, NodeJS.Timeout>();

    constructor(
        private jsonParser: JsonParser,
        private decorationManager: DecorationManager,
        private webviewProvider: JsonWebviewProvider,
        private dedicatedPanel?: DedicatedPanelManager
    ) {}

    public startMonitoring(): void {
        if (this.isMonitoring) {
            return;
        }

        this.isMonitoring = true;

        // Try multiple approaches for terminal monitoring
        let terminalDataDisposable: vscode.Disposable | undefined;

        // Method 1: Try onDidWriteTerminalData (VS Code 1.72+)
        try {
            if ('onDidWriteTerminalData' in vscode.window) {
                // @ts-ignore - This API might not be available in all VS Code versions
                terminalDataDisposable = vscode.window.onDidWriteTerminalData?.(e => {
                    this.handleTerminalData(e.terminal, e.data);
                });

                if (terminalDataDisposable) {
                    this.disposables.push(terminalDataDisposable);
                    console.log('JSON Log Parser: Using onDidWriteTerminalData API');
                }
            }
        } catch (error) {
            console.log('JSON Log Parser: onDidWriteTerminalData failed:', error);
        }

        // Method 2: Enhanced fallback with better terminal integration
        if (!terminalDataDisposable) {
            console.log('JSON Log Parser: Using enhanced fallback monitoring');
            this.setupEnhancedFallbackMonitoring();
        }

        // Monitor terminal close events to clean up
        const terminalCloseDisposable = vscode.window.onDidCloseTerminal(terminal => {
            this.cleanupTerminal(terminal);
        });

        this.disposables.push(terminalCloseDisposable);
    }

    public stopMonitoring(): void {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        this.dispose();
    }

    private setupEnhancedFallbackMonitoring(): void {
        // Enhanced fallback: Multiple monitoring strategies

        // Strategy 1: Monitor when terminals are opened
        const terminalOpenDisposable = vscode.window.onDidOpenTerminal(terminal => {
            console.log(`JSON Log Parser: New terminal opened: ${terminal.name}`);
            this.setupTerminalCommands(terminal);
        });

        // Strategy 2: Monitor active terminal changes
        const activeTerminalDisposable = vscode.window.onDidChangeActiveTerminal(terminal => {
            if (terminal) {
                console.log(`JSON Log Parser: Active terminal changed: ${terminal.name}`);
                this.setupTerminalCommands(terminal);
            }
        });

        // Strategy 3: Provide manual parsing commands
        this.setupManualParsingCommands();

        this.disposables.push(terminalOpenDisposable, activeTerminalDisposable);
    }

    private setupTerminalCommands(terminal: vscode.Terminal): void {
        // Show helpful message for terminal monitoring
        vscode.window.showInformationMessage(
            `JSON Log Parser: Monitoring terminal "${terminal.name}". Use "Parse Terminal Output" command to manually parse JSON.`,
            'Test JSON Output',
            'Parse Now'
        ).then(selection => {
            if (selection === 'Test JSON Output') {
                // Send a test command to the terminal
                terminal.sendText('echo "Test JSON: {\\"status\\":\\"success\\",\\"message\\":\\"Hello World\\",\\"timestamp\\":\\"' + new Date().toISOString() + '\\"}"');
            } else if (selection === 'Parse Now') {
                // Trigger manual parsing
                vscode.commands.executeCommand('jsonLogParser.parseTerminalOutput');
            }
        });
    }

    private setupManualParsingCommands(): void {
        // Register command to manually parse terminal output
        const parseTerminalCommand = vscode.commands.registerCommand('jsonLogParser.parseTerminalOutput', async () => {
            const activeTerminal = vscode.window.activeTerminal;
            if (!activeTerminal) {
                vscode.window.showWarningMessage('No active terminal found');
                return;
            }

            // Since we can't directly read terminal content, ask user to copy-paste
            const input = await vscode.window.showInputBox({
                prompt: 'Paste terminal output containing JSON to parse',
                placeHolder: 'Paste your terminal output here...',
                ignoreFocusOut: true
            });

            if (input && input.trim()) {
                const parsedResults = this.jsonParser.parseEscapedJson(input);
                if (parsedResults.length > 0) {
                    this.webviewProvider.addParsedJson(parsedResults, `Terminal: ${activeTerminal.name} (Manual)`);
                    if (this.dedicatedPanel) {
                        this.dedicatedPanel.addParsedJson(parsedResults, `Terminal: ${activeTerminal.name} (Manual)`);
                    }
                    vscode.window.showInformationMessage(`Found ${parsedResults.length} JSON object(s) in terminal output`);
                } else {
                    vscode.window.showInformationMessage('No JSON found in the provided text');
                }
            }
        });

        this.disposables.push(parseTerminalCommand);
    }

    // Add a method to try parsing clipboard content from terminal
    private async tryParseClipboardContent(): Promise<void> {
        try {
            const clipboardText = await vscode.env.clipboard.readText();
            if (clipboardText && this.jsonParser.containsJson(clipboardText)) {
                const parsedResults = this.jsonParser.parseEscapedJson(clipboardText);
                if (parsedResults.length > 0) {
                    this.webviewProvider.addParsedJson(parsedResults, 'Clipboard Content');
                    if (this.dedicatedPanel) {
                        this.dedicatedPanel.addParsedJson(parsedResults, 'Clipboard Content');
                    }
                    vscode.window.showInformationMessage(`Found ${parsedResults.length} JSON object(s) in clipboard`);
                }
            }
        } catch (error) {
            console.log('JSON Log Parser: Failed to read clipboard:', error);
        }
    }

    private handleTerminalData(terminal: vscode.Terminal, data: string): void {
        // Accumulate data for this terminal
        const currentBuffer = this.terminalDataBuffer.get(terminal) || '';
        const newBuffer = currentBuffer + data;
        this.terminalDataBuffer.set(terminal, newBuffer);

        // Clear existing debounce timer
        const existingTimer = this.debounceTimers.get(terminal);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }

        // Set new debounce timer to process data after a short delay
        const timer = setTimeout(() => {
            this.processTerminalBuffer(terminal);
        }, 500); // 500ms debounce

        this.debounceTimers.set(terminal, timer);
    }

    private processTerminalBuffer(terminal: vscode.Terminal): void {
        const buffer = this.terminalDataBuffer.get(terminal);
        if (!buffer) {
            return;
        }

        // Check if buffer contains potential JSON
        if (!this.jsonParser.containsJson(buffer)) {
            this.terminalDataBuffer.set(terminal, '');
            return;
        }

        // Parse JSON from buffer
        const parsedResults = this.jsonParser.parseEscapedJson(buffer);
        
        if (parsedResults.length > 0) {
            // Update both webview and dedicated panel
            this.webviewProvider.addParsedJson(parsedResults, `Terminal: ${terminal.name}`);
            if (this.dedicatedPanel) {
                this.dedicatedPanel.addParsedJson(parsedResults, `Terminal: ${terminal.name}`);
            }

            // Show notification for significant findings
            if (parsedResults.length >= 2) {
                vscode.window.showInformationMessage(
                    `Auto-detected ${parsedResults.length} JSON objects in terminal output`,
                    'Show Dedicated Tab',
                    'Show Output Panel'
                ).then(selection => {
                    if (selection === 'Show Dedicated Tab' && this.dedicatedPanel) {
                        this.dedicatedPanel.showPanel();
                    } else if (selection === 'Show Output Panel') {
                        vscode.commands.executeCommand('workbench.view.extension.jsonLogParser');
                    }
                });
            }
        }

        // Keep only recent data in buffer (last 5000 characters)
        const maxBufferSize = 5000;
        if (buffer.length > maxBufferSize) {
            this.terminalDataBuffer.set(terminal, buffer.slice(-maxBufferSize));
        } else {
            this.terminalDataBuffer.set(terminal, '');
        }
    }

    private cleanupTerminal(terminal: vscode.Terminal): void {
        this.terminalDataBuffer.delete(terminal);
        
        const timer = this.debounceTimers.get(terminal);
        if (timer) {
            clearTimeout(timer);
            this.debounceTimers.delete(terminal);
        }
    }

    public dispose(): void {
        // Clear all timers
        for (const timer of this.debounceTimers.values()) {
            clearTimeout(timer);
        }
        this.debounceTimers.clear();

        // Clear buffers
        this.terminalDataBuffer.clear();

        // Dispose of event listeners
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
