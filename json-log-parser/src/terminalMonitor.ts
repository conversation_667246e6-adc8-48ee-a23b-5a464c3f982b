import * as vscode from 'vscode';
import { JsonParser, ParsedJsonResult } from './jsonParser';
import { DecorationManager } from './decorationManager';
import { JsonWebviewProvider } from './webviewProvider';
import { DedicatedPanelManager } from './dedicatedPanelManager';

export class TerminalMonitor implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private isMonitoring = false;
    private terminalDataBuffer = new Map<vscode.Terminal, string>();
    private debounceTimers = new Map<vscode.Terminal, NodeJS.Timeout>();

    constructor(
        private jsonParser: JsonParser,
        private decorationManager: DecorationManager,
        private webviewProvider: JsonWebviewProvider,
        private dedicatedPanel?: DedicatedPanelManager
    ) {}

    public startMonitoring(): void {
        if (this.isMonitoring) {
            return;
        }

        this.isMonitoring = true;

        // Try to use onDidWriteTerminalData if available
        try {
            // @ts-ignore - This API might not be available in all VS Code versions
            const terminalDataDisposable = vscode.window.onDidWriteTerminalData?.(e => {
                this.handleTerminalData(e.terminal, e.data);
            });

            if (terminalDataDisposable) {
                this.disposables.push(terminalDataDisposable);
                console.log('JSON Log Parser: Using onDidWriteTerminalData API');
            } else {
                console.log('JSON Log Parser: onDidWriteTerminalData not available, using fallback');
                this.setupFallbackMonitoring();
            }
        } catch (error) {
            console.log('JSON Log Parser: onDidWriteTerminalData not available, using fallback');
            this.setupFallbackMonitoring();
        }

        // Monitor terminal close events to clean up
        const terminalCloseDisposable = vscode.window.onDidCloseTerminal(terminal => {
            this.cleanupTerminal(terminal);
        });

        this.disposables.push(terminalCloseDisposable);
    }

    public stopMonitoring(): void {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        this.dispose();
    }

    private setupFallbackMonitoring(): void {
        // Fallback: Monitor when terminals are opened and provide instructions
        const terminalOpenDisposable = vscode.window.onDidOpenTerminal(terminal => {
            console.log(`JSON Log Parser: New terminal opened: ${terminal.name}`);
            vscode.window.showInformationMessage(
                'JSON Log Parser: Terminal monitoring active. Run commands with escaped JSON to see auto-detection.',
                'Test Now'
            ).then(selection => {
                if (selection === 'Test Now') {
                    // Send a test command to the terminal
                    terminal.sendText('echo "Test JSON: {\\"status\\":\\"success\\",\\"message\\":\\"Hello World\\"}"');
                }
            });
        });

        this.disposables.push(terminalOpenDisposable);
    }

    private monitorTerminal(terminal: vscode.Terminal): void {
        console.log(`JSON Log Parser: Monitoring terminal: ${terminal.name}`);
    }

    private handleTerminalData(terminal: vscode.Terminal, data: string): void {
        // Accumulate data for this terminal
        const currentBuffer = this.terminalDataBuffer.get(terminal) || '';
        const newBuffer = currentBuffer + data;
        this.terminalDataBuffer.set(terminal, newBuffer);

        // Clear existing debounce timer
        const existingTimer = this.debounceTimers.get(terminal);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }

        // Set new debounce timer to process data after a short delay
        const timer = setTimeout(() => {
            this.processTerminalBuffer(terminal);
        }, 500); // 500ms debounce

        this.debounceTimers.set(terminal, timer);
    }

    private processTerminalBuffer(terminal: vscode.Terminal): void {
        const buffer = this.terminalDataBuffer.get(terminal);
        if (!buffer) {
            return;
        }

        // Check if buffer contains potential JSON
        if (!this.jsonParser.containsJson(buffer)) {
            this.terminalDataBuffer.set(terminal, '');
            return;
        }

        // Parse JSON from buffer
        const parsedResults = this.jsonParser.parseEscapedJson(buffer);
        
        if (parsedResults.length > 0) {
            // Update both webview and dedicated panel
            this.webviewProvider.addParsedJson(parsedResults, `Terminal: ${terminal.name}`);
            if (this.dedicatedPanel) {
                this.dedicatedPanel.addParsedJson(parsedResults, `Terminal: ${terminal.name}`);
            }

            // Show notification for significant findings
            if (parsedResults.length >= 2) {
                vscode.window.showInformationMessage(
                    `Auto-detected ${parsedResults.length} JSON objects in terminal output`,
                    'Show Dedicated Tab',
                    'Show Output Panel'
                ).then(selection => {
                    if (selection === 'Show Dedicated Tab' && this.dedicatedPanel) {
                        this.dedicatedPanel.showPanel();
                    } else if (selection === 'Show Output Panel') {
                        vscode.commands.executeCommand('workbench.view.extension.jsonLogParser');
                    }
                });
            }
        }

        // Keep only recent data in buffer (last 5000 characters)
        const maxBufferSize = 5000;
        if (buffer.length > maxBufferSize) {
            this.terminalDataBuffer.set(terminal, buffer.slice(-maxBufferSize));
        } else {
            this.terminalDataBuffer.set(terminal, '');
        }
    }

    private cleanupTerminal(terminal: vscode.Terminal): void {
        this.terminalDataBuffer.delete(terminal);
        
        const timer = this.debounceTimers.get(terminal);
        if (timer) {
            clearTimeout(timer);
            this.debounceTimers.delete(terminal);
        }
    }

    public dispose(): void {
        // Clear all timers
        for (const timer of this.debounceTimers.values()) {
            clearTimeout(timer);
        }
        this.debounceTimers.clear();

        // Clear buffers
        this.terminalDataBuffer.clear();

        // Dispose of event listeners
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
