import * as vscode from 'vscode';
import { <PERSON>sonPars<PERSON>, ParsedJsonResult } from './jsonParser';
import { DecorationManager } from './decorationManager';

import { DedicatedPanelManager } from './dedicatedPanelManager';

export class OutputMonitor implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private isMonitoring = false;
    private processedRanges = new Map<vscode.TextDocument, vscode.Range[]>();

    constructor(
        private jsonParser: JsonParser,
        private decorationManager: DecorationManager,
        private dedicatedPanel?: DedicatedPanelManager
    ) {}

    public startMonitoring(): void {
        if (this.isMonitoring) {
            return;
        }

        this.isMonitoring = true;

        // Monitor active text editor changes
        const editorChangeDisposable = vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor && this.isOutputChannel(editor.document)) {
                this.processDocument(editor.document);
            }
        });

        // Monitor document content changes
        const documentChangeDisposable = vscode.workspace.onDidChangeTextDocument(e => {
            if (this.isOutputChannel(e.document)) {
                this.processDocumentChanges(e);
            }
        });

        this.disposables.push(editorChangeDisposable, documentChangeDisposable);

        // Process currently active editor if it's an output channel
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor && this.isOutputChannel(activeEditor.document)) {
            this.processDocument(activeEditor.document);
        }
    }

    public stopMonitoring(): void {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        this.dispose();
    }

    private isOutputChannel(document: vscode.TextDocument): boolean {
        // Check if document is from an output channel
        return document.uri.scheme === 'output' || 
               document.languageId === 'log' ||
               document.fileName.includes('output') ||
               document.fileName.includes('.log');
    }

    private processDocument(document: vscode.TextDocument): void {
        const text = document.getText();
        if (!text || !this.jsonParser.containsJson(text)) {
            return;
        }

        const parsedResults = this.jsonParser.parseEscapedJson(text);
        if (parsedResults.length > 0) {
            // Update both webview and dedicated panel
            this.webviewProvider.addParsedJson(
                parsedResults,
                `Output: ${this.getChannelName(document)}`
            );

            if (this.dedicatedPanel) {
                this.dedicatedPanel.addParsedJson(
                    parsedResults,
                    `Output: ${this.getChannelName(document)}`
                );
            }

            // Apply decorations to the document
            this.decorationManager.applyDecorations(document, parsedResults);

            // Store processed ranges to avoid reprocessing
            const ranges = parsedResults.map(result =>
                new vscode.Range(
                    document.positionAt(result.startIndex),
                    document.positionAt(result.endIndex)
                )
            );
            this.processedRanges.set(document, ranges);
        }
    }

    private processDocumentChanges(e: vscode.TextDocumentChangeEvent): void {
        // Only process new content, not already processed ranges
        const document = e.document;
        const processedRanges = this.processedRanges.get(document) || [];

        for (const change of e.contentChanges) {
            // Check if this change overlaps with already processed ranges
            const changeRange = change.range;
            const isAlreadyProcessed = processedRanges.some(range => 
                range.intersection(changeRange) !== undefined
            );

            if (!isAlreadyProcessed && this.jsonParser.containsJson(change.text)) {
                const parsedResults = this.jsonParser.parseEscapedJson(change.text);
                if (parsedResults.length > 0) {
                    // Adjust indices based on change range start
                    const startOffset = document.offsetAt(changeRange.start);
                    const adjustedResults = parsedResults.map(result => ({
                        ...result,
                        startIndex: result.startIndex + startOffset,
                        endIndex: result.endIndex + startOffset
                    }));

                    this.webviewProvider.addParsedJson(
                        adjustedResults,
                        `Output: ${this.getChannelName(document)}`
                    );

                    this.decorationManager.applyDecorations(document, adjustedResults);

                    // Update processed ranges
                    const newRanges = adjustedResults.map(result => 
                        new vscode.Range(
                            document.positionAt(result.startIndex),
                            document.positionAt(result.endIndex)
                        )
                    );
                    processedRanges.push(...newRanges);
                    this.processedRanges.set(document, processedRanges);
                }
            }
        }
    }

    private getChannelName(document: vscode.TextDocument): string {
        // Extract channel name from URI or filename
        if (document.uri.scheme === 'output') {
            return document.uri.path.split('/').pop() || 'Unknown';
        }
        return document.fileName.split('/').pop() || 'Unknown';
    }

    public dispose(): void {
        this.processedRanges.clear();
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
