import * as vscode from 'vscode';
import { <PERSON>sonPars<PERSON>, ParsedJsonResult } from './jsonParser';
import { JsonWebviewProvider } from './webviewProvider';
import { DedicatedPanelManager } from './dedicatedPanelManager';
import { ConfigurationManager } from './configuration';

export class ClipboardMonitor implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private isMonitoring = false;
    private lastClipboardContent = '';
    private monitoringInterval: NodeJS.Timeout | undefined;

    constructor(
        private jsonParser: JsonParser,
        private webviewProvider: JsonWebviewProvider,
        private config: ConfigurationManager,
        private dedicatedPanel?: DedicatedPanelManager
    ) {}

    public startMonitoring(): void {
        if (this.isMonitoring || !this.config.shouldAutoDetectClipboard()) {
            return;
        }

        this.isMonitoring = true;
        console.log('JSON Log Parser: Starting clipboard monitoring');

        // Poll clipboard every 2 seconds
        this.monitoringInterval = setInterval(async () => {
            await this.checkClipboard();
        }, 2000);

        // Also check when window gets focus
        const focusDisposable = vscode.window.onDidChangeWindowState(state => {
            if (state.focused) {
                this.checkClipboard();
            }
        });

        this.disposables.push(focusDisposable);
    }

    public stopMonitoring(): void {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        console.log('JSON Log Parser: Stopping clipboard monitoring');

        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
        }

        this.dispose();
    }

    private async checkClipboard(): Promise<void> {
        try {
            const clipboardText = await vscode.env.clipboard.readText();
            
            // Skip if clipboard hasn't changed or is empty
            if (!clipboardText || clipboardText === this.lastClipboardContent) {
                return;
            }

            this.lastClipboardContent = clipboardText;

            // Quick check if clipboard contains potential JSON
            if (!this.jsonParser.containsJson(clipboardText)) {
                return;
            }

            // Parse JSON from clipboard
            const parsedResults = this.jsonParser.parseEscapedJson(clipboardText);
            
            if (parsedResults.length > 0) {
                console.log(`JSON Log Parser: Auto-detected ${parsedResults.length} JSON objects in clipboard`);
                
                // Update displays
                this.webviewProvider.addParsedJson(parsedResults, 'Clipboard (Auto-detected)');
                if (this.dedicatedPanel) {
                    this.dedicatedPanel.addParsedJson(parsedResults, 'Clipboard (Auto-detected)');
                }

                // Show subtle notification
                vscode.window.showInformationMessage(
                    `📋 Auto-detected ${parsedResults.length} JSON object(s) from clipboard`,
                    'Show Results'
                ).then(selection => {
                    if (selection === 'Show Results' && this.dedicatedPanel) {
                        this.dedicatedPanel.showPanel();
                    }
                });
            }
        } catch (error) {
            // Silently fail - clipboard access might be restricted
            console.log('JSON Log Parser: Clipboard access failed:', error);
        }
    }

    public dispose(): void {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
