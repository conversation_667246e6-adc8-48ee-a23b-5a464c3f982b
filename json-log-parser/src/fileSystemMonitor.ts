import * as vscode from 'vscode';
import * as path from 'path';
import { JsonParser, ParsedJsonResult } from './jsonParser';

import { DedicatedPanelManager } from './dedicatedPanelManager';
import { ConfigurationManager } from './configuration';

export class FileSystemMonitor implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private isMonitoring = false;
    private fileWatchers: vscode.FileSystemWatcher[] = [];
    private processedFiles = new Map<string, number>(); // file path -> last modified time

    constructor(
        private jsonParser: JsonParser,
        private config: ConfigurationManager,
        private dedicatedPanel?: DedicatedPanelManager
    ) {}

    public startMonitoring(): void {
        if (this.isMonitoring || !this.config.shouldAutoDetectFiles()) {
            return;
        }

        this.isMonitoring = true;
        console.log('JSON Log Parser: Starting file system monitoring');

        this.setupLogFileWatchers();
        this.scanExistingLogFiles();
    }

    public stopMonitoring(): void {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        console.log('JSON Log Parser: Stopping file system monitoring');
        this.dispose();
    }

    private setupLogFileWatchers(): void {
        // Watch for common log file patterns
        const logPatterns = [
            '**/*.log',
            '**/*.out',
            '**/*.err',
            '**/logs/**/*',
            '**/*log*.txt',
            '**/*.json.log'
        ];

        logPatterns.forEach(pattern => {
            const watcher = vscode.workspace.createFileSystemWatcher(pattern);
            
            // Watch for file changes
            watcher.onDidChange(uri => {
                this.handleFileChange(uri);
            });

            // Watch for new files
            watcher.onDidCreate(uri => {
                this.handleFileChange(uri);
            });

            this.fileWatchers.push(watcher);
            this.disposables.push(watcher);
        });
    }

    private async scanExistingLogFiles(): Promise<void> {
        try {
            // Find existing log files in workspace
            const logFiles = await vscode.workspace.findFiles(
                '**/*.{log,out,err,txt}',
                '**/node_modules/**',
                50 // Limit to 50 files
            );

            for (const fileUri of logFiles) {
                await this.processLogFile(fileUri);
            }
        } catch (error) {
            console.log('JSON Log Parser: Error scanning existing log files:', error);
        }
    }

    private async handleFileChange(uri: vscode.Uri): Promise<void> {
        // Debounce file changes
        setTimeout(() => {
            this.processLogFile(uri);
        }, 1000);
    }

    private async processLogFile(uri: vscode.Uri): Promise<void> {
        try {
            const filePath = uri.fsPath;
            const fileName = path.basename(filePath);
            
            // Skip if file is too large (> 1MB)
            const stat = await vscode.workspace.fs.stat(uri);
            if (stat.size > 1024 * 1024) {
                return;
            }

            // Check if file was already processed recently
            const lastModified = stat.mtime;
            const lastProcessed = this.processedFiles.get(filePath);
            if (lastProcessed && lastProcessed >= lastModified) {
                return;
            }

            // Read file content
            const fileContent = await vscode.workspace.fs.readFile(uri);
            const text = Buffer.from(fileContent).toString('utf8');

            // Quick check for JSON content
            if (!this.jsonParser.containsJson(text)) {
                return;
            }

            // Parse JSON from file
            const parsedResults = this.jsonParser.parseEscapedJson(text);
            
            if (parsedResults.length > 0) {
                console.log(`JSON Log Parser: Auto-detected ${parsedResults.length} JSON objects in ${fileName}`);
                
                // Update displays
                this.webviewProvider.addParsedJson(parsedResults, `File: ${fileName} (Auto-detected)`);
                if (this.dedicatedPanel) {
                    this.dedicatedPanel.addParsedJson(parsedResults, `File: ${fileName} (Auto-detected)`);
                }

                // Show notification for significant findings
                if (parsedResults.length >= 3) {
                    vscode.window.showInformationMessage(
                        `📁 Auto-detected ${parsedResults.length} JSON objects in ${fileName}`,
                        'Show Results'
                    ).then(selection => {
                        if (selection === 'Show Results' && this.dedicatedPanel) {
                            this.dedicatedPanel.showPanel();
                        }
                    });
                }

                // Mark file as processed
                this.processedFiles.set(filePath, lastModified);
            }
        } catch (error) {
            console.log('JSON Log Parser: Error processing log file:', error);
        }
    }

    public dispose(): void {
        this.fileWatchers.forEach(watcher => watcher.dispose());
        this.fileWatchers = [];
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
        this.processedFiles.clear();
    }
}
