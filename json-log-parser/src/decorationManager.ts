import * as vscode from 'vscode';
import { ParsedJsonResult } from './jsonParser';
import { ConfigurationManager } from './configuration';

export class DecorationManager implements vscode.Disposable {
    private decorationType: vscode.TextEditorDecorationType;
    private activeDecorations = new Map<vscode.TextDocument, vscode.DecorationOptions[]>();

    constructor(private config: ConfigurationManager) {
        this.decorationType = this.createDecorationType();
        
        // Listen for configuration changes to update decoration style
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('jsonLogParser.highlightColor')) {
                this.updateDecorationType();
            }
        });
    }

    private createDecorationType(): vscode.TextEditorDecorationType {
        return vscode.window.createTextEditorDecorationType({
            backgroundColor: this.config.getHighlightColor() + '20', // 20% opacity
            border: `1px solid ${this.config.getHighlightColor()}`,
            borderRadius: '3px',
            overviewRulerColor: this.config.getHighlightColor(),
            overviewRulerLane: vscode.OverviewRulerLane.Right,
            after: {
                contentText: ' 📋 JSON',
                color: this.config.getHighlightColor(),
                fontWeight: 'bold',
                margin: '0 0 0 10px'
            }
        });
    }

    private updateDecorationType(): void {
        this.decorationType.dispose();
        this.decorationType = this.createDecorationType();
        
        // Reapply decorations to all active documents
        for (const [document, decorations] of this.activeDecorations) {
            const editor = vscode.window.visibleTextEditors.find(e => e.document === document);
            if (editor) {
                editor.setDecorations(this.decorationType, decorations);
            }
        }
    }

    public applyDecorations(document: vscode.TextDocument, results: ParsedJsonResult[]): void {
        const decorations: vscode.DecorationOptions[] = results.map(result => {
            const startPos = document.positionAt(result.startIndex);
            const endPos = document.positionAt(result.endIndex);
            const range = new vscode.Range(startPos, endPos);

            return {
                range,
                hoverMessage: this.createHoverMessage(result)
            };
        });

        // Store decorations for this document
        this.activeDecorations.set(document, decorations);

        // Apply decorations to visible editors
        const editors = vscode.window.visibleTextEditors.filter(e => e.document === document);
        editors.forEach(editor => {
            editor.setDecorations(this.decorationType, decorations);
        });
    }

    private createHoverMessage(result: ParsedJsonResult): vscode.MarkdownString {
        const markdown = new vscode.MarkdownString();
        markdown.isTrusted = true;
        
        markdown.appendMarkdown('**Parsed JSON:**\n\n');
        markdown.appendCodeblock(result.formatted, 'json');
        
        markdown.appendMarkdown('\n**Original:**\n\n');
        markdown.appendCodeblock(result.original, 'text');
        
        // Add copy button (this would need additional implementation)
        markdown.appendMarkdown('\n[Copy Formatted JSON](command:jsonLogParser.copyFormatted)');
        
        return markdown;
    }

    public clearDecorations(document?: vscode.TextDocument): void {
        if (document) {
            // Clear decorations for specific document
            this.activeDecorations.delete(document);
            const editors = vscode.window.visibleTextEditors.filter(e => e.document === document);
            editors.forEach(editor => {
                editor.setDecorations(this.decorationType, []);
            });
        } else {
            // Clear all decorations
            this.activeDecorations.clear();
            vscode.window.visibleTextEditors.forEach(editor => {
                editor.setDecorations(this.decorationType, []);
            });
        }
    }

    public dispose(): void {
        this.decorationType.dispose();
        this.activeDecorations.clear();
    }
}
