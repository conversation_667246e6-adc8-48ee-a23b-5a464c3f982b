import * as vscode from 'vscode';
import { JsonParser, ParsedJsonResult } from './jsonParser';
import { DecorationManager } from './decorationManager';
import { JsonWebviewProvider } from './webviewProvider';
import { DedicatedPanelManager } from './dedicatedPanelManager';
import { ConfigurationManager } from './configuration';

export class DocumentMonitor implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private isMonitoring = false;
    private processedDocuments = new Map<vscode.TextDocument, number>();
    private debounceTimer: NodeJS.Timeout | undefined;

    constructor(
        private jsonParser: <PERSON>sonParser,
        private decorationManager: DecorationManager,
        private webviewProvider: JsonWebviewProvider,
        private config: ConfigurationManager,
        private dedicatedPanel?: DedicatedPanelManager
    ) {}

    public startMonitoring(): void {
        if (this.isMonitoring || !this.config.shouldAutoDetectInFiles()) {
            return;
        }

        this.isMonitoring = true;

        // Monitor active text editor changes
        const editorChangeDisposable = vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor && this.shouldMonitorDocument(editor.document)) {
                this.scheduleDocumentScan(editor.document);
            }
        });

        // Monitor document content changes
        const documentChangeDisposable = vscode.workspace.onDidChangeTextDocument(e => {
            if (this.shouldMonitorDocument(e.document)) {
                this.scheduleDocumentScan(e.document);
            }
        });

        this.disposables.push(editorChangeDisposable, documentChangeDisposable);

        // Process currently active editor
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor && this.shouldMonitorDocument(activeEditor.document)) {
            this.scheduleDocumentScan(activeEditor.document);
        }
    }

    public stopMonitoring(): void {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        this.dispose();
    }

    private shouldMonitorDocument(document: vscode.TextDocument): boolean {
        // Monitor log files, JSON files, and text files that might contain escaped JSON
        const supportedSchemes = ['file', 'untitled'];
        const supportedLanguages = ['log', 'json', 'javascript', 'typescript', 'python', 'plaintext'];
        const logFileExtensions = ['.log', '.txt', '.out', '.err'];
        
        if (!supportedSchemes.includes(document.uri.scheme)) {
            return false;
        }

        // Check language ID
        if (supportedLanguages.includes(document.languageId)) {
            return true;
        }

        // Check file extension
        const fileName = document.fileName.toLowerCase();
        if (logFileExtensions.some(ext => fileName.endsWith(ext))) {
            return true;
        }

        // Check if filename contains 'log'
        if (fileName.includes('log')) {
            return true;
        }

        return false;
    }

    private scheduleDocumentScan(document: vscode.TextDocument): void {
        // Clear existing timer
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        // Schedule new scan with debounce
        this.debounceTimer = setTimeout(() => {
            this.scanDocument(document);
        }, 1000); // 1 second debounce
    }

    private scanDocument(document: vscode.TextDocument): void {
        const text = document.getText();
        
        // Skip if document is too large or empty
        if (!text || text.length > 50000) {
            return;
        }

        // Check if we've already processed this version of the document
        const currentVersion = document.version;
        const lastProcessedVersion = this.processedDocuments.get(document);
        if (lastProcessedVersion === currentVersion) {
            return;
        }

        // Quick check if document contains potential JSON
        if (!this.jsonParser.containsJson(text)) {
            return;
        }

        // Parse JSON from document
        const parsedResults = this.jsonParser.parseEscapedJson(text);
        
        if (parsedResults.length > 0) {
            const fileName = this.getDocumentName(document);
            
            // Update both displays
            this.webviewProvider.addParsedJson(parsedResults, `File: ${fileName}`);
            if (this.dedicatedPanel) {
                this.dedicatedPanel.addParsedJson(parsedResults, `File: ${fileName}`);
            }

            // Apply decorations to the document
            this.decorationManager.applyDecorations(document, parsedResults);

            // Show notification for auto-detected JSON
            if (parsedResults.length >= 2) {
                vscode.window.showInformationMessage(
                    `Auto-detected ${parsedResults.length} JSON objects in ${fileName}`,
                    'Show Dedicated Tab',
                    'Show Output Panel'
                ).then(selection => {
                    if (selection === 'Show Dedicated Tab' && this.dedicatedPanel) {
                        this.dedicatedPanel.showPanel();
                    } else if (selection === 'Show Output Panel') {
                        vscode.commands.executeCommand('workbench.view.extension.jsonLogParser');
                    }
                });
            }

            // Mark document as processed
            this.processedDocuments.set(document, currentVersion);
        }
    }

    private getDocumentName(document: vscode.TextDocument): string {
        if (document.uri.scheme === 'untitled') {
            return 'Untitled';
        }
        
        const path = document.uri.fsPath;
        const fileName = path.split('/').pop() || path.split('\\').pop() || 'Unknown';
        return fileName;
    }

    public dispose(): void {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        this.processedDocuments.clear();
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
