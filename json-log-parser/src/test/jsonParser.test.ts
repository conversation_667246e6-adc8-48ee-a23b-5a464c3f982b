import * as assert from 'assert';
import { JsonParser } from '../jsonParser';
import { ConfigurationManager } from '../configuration';

// Mock configuration for testing
class MockConfigurationManager extends ConfigurationManager {
    public isEnabled(): boolean { return true; }
    public isAutoDetectEnabled(): boolean { return true; }
    public getHighlightColor(): string { return '#4CAF50'; }
    public getMaxLogSize(): number { return 10000; }
    public shouldShowInSidebar(): boolean { return true; }
}

describe('JsonParser Test Suite', () => {
    let parser: JsonParser;

    beforeEach(() => {
        const config = new MockConfigurationManager();
        parser = new JsonParser(config);
    });

    it('should parse double-escaped JSON', () => {
        const input = 'Log entry: "{\\"name\\":\\"John\\",\\"age\\":30}"';
        const results = parser.parseEscapedJson(input);
        
        assert.strictEqual(results.length, 1);
        assert.strictEqual(results[0].parsed.name, 'John');
        assert.strictEqual(results[0].parsed.age, 30);
        assert.strictEqual(results[0].isValid, true);
    });

    it('should parse single-escaped JSON', () => {
        const input = 'API Response: {"status":"success","data":{"id":123}}';
        const results = parser.parseEscapedJson(input);
        
        assert.strictEqual(results.length, 1);
        assert.strictEqual(results[0].parsed.status, 'success');
        assert.strictEqual(results[0].parsed.data.id, 123);
    });

    it('should parse JSON array', () => {
        const input = 'Items: [{"id":1,"name":"Item1"},{"id":2,"name":"Item2"}]';
        const results = parser.parseEscapedJson(input);
        
        assert.strictEqual(results.length, 1);
        assert.strictEqual(Array.isArray(results[0].parsed), true);
        assert.strictEqual(results[0].parsed.length, 2);
        assert.strictEqual(results[0].parsed[0].name, 'Item1');
    });

    it('should handle invalid JSON gracefully', () => {
        const input = 'Invalid: {"name":"John",age:30}'; // Missing quotes around age
        const results = parser.parseEscapedJson(input);
        
        // Should not crash and return empty results
        assert.strictEqual(results.length, 0);
    });

    it('should detect JSON presence', () => {
        const validInput = 'Log: {"status":"ok"}';
        const invalidInput = 'Just a regular log message';
        
        assert.strictEqual(parser.containsJson(validInput), true);
        assert.strictEqual(parser.containsJson(invalidInput), false);
    });

    it('should handle multiple JSON objects', () => {
        const input = 'First: {"a":1} Second: {"b":2}';
        const results = parser.parseEscapedJson(input);
        
        assert.strictEqual(results.length, 2);
        assert.strictEqual(results[0].parsed.a, 1);
        assert.strictEqual(results[1].parsed.b, 2);
    });

    it('should parse OpenAI API log example', () => {
        const input = 'OpenAI Request: "{\\"model\\":\\"gpt-4\\",\\"messages\\":[{\\"role\\":\\"user\\",\\"content\\":\\"Hello\\"}]}"';
        const results = parser.parseEscapedJson(input);
        
        assert.strictEqual(results.length, 1);
        assert.strictEqual(results[0].parsed.model, 'gpt-4');
        assert.strictEqual(results[0].parsed.messages[0].role, 'user');
        assert.strictEqual(results[0].parsed.messages[0].content, 'Hello');
    });
});
