import * as path from 'path';
import * as fs from 'fs';

export function run(): Promise<void> {
    const Mocha = require('mocha');

    // Create the mocha test
    const mocha = new Mocha({
        ui: 'bdd',
        color: true
    });

    const testsRoot = path.resolve(__dirname, '..');

    return new Promise((c, e) => {
        try {
            // Simple approach: directly add known test files
            const testFiles = [
                path.resolve(testsRoot, 'jsonParser.test.js')
            ];

            // Add files to the test suite if they exist
            testFiles.forEach((file: string) => {
                if (fs.existsSync(file)) {
                    mocha.addFile(file);
                }
            });

            // Run the mocha test
            mocha.run((failures: number) => {
                if (failures > 0) {
                    e(new Error(`${failures} tests failed.`));
                } else {
                    c();
                }
            });
        } catch (err) {
            console.error(err);
            e(err);
        }
    });
}
