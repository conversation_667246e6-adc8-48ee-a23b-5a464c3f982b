"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.OutputMonitor = void 0;
const vscode = __importStar(require("vscode"));
class OutputMonitor {
    constructor(jsonParser, decorationManager, dedicatedPanel) {
        this.jsonParser = jsonParser;
        this.decorationManager = decorationManager;
        this.dedicatedPanel = dedicatedPanel;
        this.disposables = [];
        this.isMonitoring = false;
        this.processedRanges = new Map();
    }
    startMonitoring() {
        if (this.isMonitoring) {
            return;
        }
        this.isMonitoring = true;
        // Monitor active text editor changes
        const editorChangeDisposable = vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor && this.isOutputChannel(editor.document)) {
                this.processDocument(editor.document);
            }
        });
        // Monitor document content changes
        const documentChangeDisposable = vscode.workspace.onDidChangeTextDocument(e => {
            if (this.isOutputChannel(e.document)) {
                this.processDocumentChanges(e);
            }
        });
        this.disposables.push(editorChangeDisposable, documentChangeDisposable);
        // Process currently active editor if it's an output channel
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor && this.isOutputChannel(activeEditor.document)) {
            this.processDocument(activeEditor.document);
        }
    }
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        this.isMonitoring = false;
        this.dispose();
    }
    isOutputChannel(document) {
        // Check if document is from an output channel
        return document.uri.scheme === 'output' ||
            document.languageId === 'log' ||
            document.fileName.includes('output') ||
            document.fileName.includes('.log');
    }
    processDocument(document) {
        const text = document.getText();
        if (!text || !this.jsonParser.containsJson(text)) {
            return;
        }
        const parsedResults = this.jsonParser.parseEscapedJson(text);
        if (parsedResults.length > 0) {
            // Update dedicated panel only
            if (this.dedicatedPanel) {
                this.dedicatedPanel.addParsedJson(parsedResults, `Output: ${this.getChannelName(document)}`);
            }
            // Apply decorations to the document
            this.decorationManager.applyDecorations(document, parsedResults);
            // Store processed ranges to avoid reprocessing
            const ranges = parsedResults.map(result => new vscode.Range(document.positionAt(result.startIndex), document.positionAt(result.endIndex)));
            this.processedRanges.set(document, ranges);
        }
    }
    processDocumentChanges(e) {
        // Only process new content, not already processed ranges
        const document = e.document;
        const processedRanges = this.processedRanges.get(document) || [];
        for (const change of e.contentChanges) {
            // Check if this change overlaps with already processed ranges
            const changeRange = change.range;
            const isAlreadyProcessed = processedRanges.some(range => range.intersection(changeRange) !== undefined);
            if (!isAlreadyProcessed && this.jsonParser.containsJson(change.text)) {
                const parsedResults = this.jsonParser.parseEscapedJson(change.text);
                if (parsedResults.length > 0) {
                    // Adjust indices based on change range start
                    const startOffset = document.offsetAt(changeRange.start);
                    const adjustedResults = parsedResults.map(result => ({
                        ...result,
                        startIndex: result.startIndex + startOffset,
                        endIndex: result.endIndex + startOffset
                    }));
                    if (this.dedicatedPanel) {
                        this.dedicatedPanel.addParsedJson(adjustedResults, `Output: ${this.getChannelName(document)}`);
                    }
                    this.decorationManager.applyDecorations(document, adjustedResults);
                    // Update processed ranges
                    const newRanges = adjustedResults.map(result => new vscode.Range(document.positionAt(result.startIndex), document.positionAt(result.endIndex)));
                    processedRanges.push(...newRanges);
                    this.processedRanges.set(document, processedRanges);
                }
            }
        }
    }
    getChannelName(document) {
        // Extract channel name from URI or filename
        if (document.uri.scheme === 'output') {
            return document.uri.path.split('/').pop() || 'Unknown';
        }
        return document.fileName.split('/').pop() || 'Unknown';
    }
    dispose() {
        this.processedRanges.clear();
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.OutputMonitor = OutputMonitor;
//# sourceMappingURL=outputMonitor.js.map