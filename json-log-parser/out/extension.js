"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const jsonParser_1 = require("./jsonParser");
const terminalMonitor_1 = require("./terminalMonitor");
const outputMonitor_1 = require("./outputMonitor");
const documentMonitor_1 = require("./documentMonitor");
const decorationManager_1 = require("./decorationManager");
const webviewProvider_1 = require("./webviewProvider");
const configuration_1 = require("./configuration");
const dedicatedPanelManager_1 = require("./dedicatedPanelManager");
const clipboardMonitor_1 = require("./clipboardMonitor");
const fileSystemMonitor_1 = require("./fileSystemMonitor");
function activate(context) {
    console.log('JSON Log Parser extension is now active!');
    // Create output channel for displaying results
    const outputChannel = vscode.window.createOutputChannel('JSON Parser Results');
    // Initialize core components
    const config = new configuration_1.ConfigurationManager();
    const jsonParser = new jsonParser_1.JsonParser(config);
    const decorationManager = new decorationManager_1.DecorationManager(config);
    const webviewProvider = new webviewProvider_1.JsonWebviewProvider(context.extensionUri);
    const dedicatedPanel = new dedicatedPanelManager_1.DedicatedPanelManager(context.extensionUri);
    // Initialize all monitors with dedicated panel reference
    const terminalMonitor = new terminalMonitor_1.TerminalMonitor(jsonParser, decorationManager, webviewProvider, dedicatedPanel);
    const outputMonitor = new outputMonitor_1.OutputMonitor(jsonParser, decorationManager, webviewProvider, dedicatedPanel);
    const documentMonitor = new documentMonitor_1.DocumentMonitor(jsonParser, decorationManager, webviewProvider, config, dedicatedPanel);
    const clipboardMonitor = new clipboardMonitor_1.ClipboardMonitor(jsonParser, webviewProvider, config, dedicatedPanel);
    const fileSystemMonitor = new fileSystemMonitor_1.FileSystemMonitor(jsonParser, webviewProvider, config, dedicatedPanel);
    // Register webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('jsonLogParser.parsedJsonView', webviewProvider), outputChannel);
    // Register commands
    const parseSelectionCommand = vscode.commands.registerCommand('jsonLogParser.parseSelection', () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }
        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        if (!selectedText) {
            vscode.window.showWarningMessage('No text selected');
            return;
        }
        console.log(`JSON Log Parser: Parsing selected text: ${selectedText.substring(0, 100)}...`);
        const parsedJson = jsonParser.parseEscapedJson(selectedText);
        console.log(`JSON Log Parser: Found ${parsedJson.length} JSON objects`);
        if (parsedJson.length > 0) {
            // Update both display methods
            webviewProvider.updateContent(parsedJson);
            dedicatedPanel.addParsedJson(parsedJson, 'Manual Selection');
            // Display results in output channel (secondary method)
            outputChannel.clear();
            outputChannel.appendLine('🔍 JSON Parser Results');
            outputChannel.appendLine('='.repeat(50));
            outputChannel.appendLine(`Found ${parsedJson.length} JSON object(s)\n`);
            parsedJson.forEach((result, index) => {
                outputChannel.appendLine(`📋 JSON Object ${index + 1}:`);
                outputChannel.appendLine('─'.repeat(30));
                outputChannel.appendLine('Original:');
                outputChannel.appendLine(result.original);
                outputChannel.appendLine('\nFormatted:');
                outputChannel.appendLine(result.formatted);
                outputChannel.appendLine('\n' + '='.repeat(50) + '\n');
            });
            // Show notification with both options
            vscode.window.showInformationMessage(`Found ${parsedJson.length} JSON object(s) - Check the dedicated "JSON Parser" tab or "JSON Parser Results" in Output panel`, 'Show Dedicated Tab', 'Show Output Panel').then(selection => {
                if (selection === 'Show Dedicated Tab') {
                    dedicatedPanel.showPanel();
                }
                else if (selection === 'Show Output Panel') {
                    outputChannel.show(true);
                }
            });
        }
        else {
            vscode.window.showInformationMessage('No escaped JSON found in selection');
        }
    });
    const toggleParsingCommand = vscode.commands.registerCommand('jsonLogParser.toggleParsing', () => {
        const currentState = config.isEnabled();
        config.setEnabled(!currentState);
        vscode.window.showInformationMessage(`JSON parsing ${!currentState ? 'enabled' : 'disabled'}`);
    });
    const clearCacheCommand = vscode.commands.registerCommand('jsonLogParser.clearCache', () => {
        webviewProvider.clearContent();
        vscode.window.showInformationMessage('Parsed JSON cache cleared');
    });
    const showResultsCommand = vscode.commands.registerCommand('jsonLogParser.showResults', () => {
        // Show the output channel with results
        outputChannel.show(true);
    });
    const openDedicatedPanelCommand = vscode.commands.registerCommand('jsonLogParser.openDedicatedPanel', () => {
        // Show the dedicated panel
        console.log('JSON Log Parser: openDedicatedPanel command called');
        dedicatedPanel.showPanel();
    });
    const testPanelCommand = vscode.commands.registerCommand('jsonLogParser.testPanel', () => {
        // Test the panel with sample data
        console.log('JSON Log Parser: testPanel command called');
        const testResults = [{
                original: '{"test":"value","number":123}',
                parsed: { test: "value", number: 123 },
                formatted: '{\n  "test": "value",\n  "number": 123\n}',
                startIndex: 0,
                endIndex: 25,
                isValid: true
            }];
        dedicatedPanel.addParsedJson(testResults, 'Test Command');
    });
    const parseClipboardCommand = vscode.commands.registerCommand('jsonLogParser.parseClipboard', async () => {
        try {
            const clipboardText = await vscode.env.clipboard.readText();
            if (!clipboardText || !clipboardText.trim()) {
                vscode.window.showWarningMessage('Clipboard is empty');
                return;
            }
            const parsedResults = jsonParser.parseEscapedJson(clipboardText);
            if (parsedResults.length > 0) {
                webviewProvider.addParsedJson(parsedResults, 'Clipboard Content');
                dedicatedPanel.addParsedJson(parsedResults, 'Clipboard Content');
                vscode.window.showInformationMessage(`Found ${parsedResults.length} JSON object(s) in clipboard`, 'Show Dedicated Tab').then(selection => {
                    if (selection === 'Show Dedicated Tab') {
                        dedicatedPanel.showPanel();
                    }
                });
            }
            else {
                vscode.window.showInformationMessage('No JSON found in clipboard content');
            }
        }
        catch (error) {
            vscode.window.showErrorMessage('Failed to read clipboard content');
        }
    });
    const simulateTerminalJsonCommand = vscode.commands.registerCommand('jsonLogParser.simulateTerminalJson', () => {
        // Simulate terminal JSON output for testing
        const sampleJsonOutputs = [
            'API Request: "{\\"method\\":\\"POST\\",\\"url\\":\\"https://api.example.com\\",\\"data\\":{\\"user_id\\":123,\\"action\\":\\"login\\"}}"',
            'Response received: {"status":200,"data":{"token":"abc123","expires_in":3600,"user":{"id":123,"name":"John Doe","email":"<EMAIL>"}}}',
            'Error occurred: \'{"error":"validation_failed","details":{"field":"email","message":"Invalid email format"},"timestamp":"2025-06-21T13:45:00Z"}\'',
            'Log entry: [{"level":"info","message":"User authenticated","metadata":{"ip":"***********","user_agent":"Mozilla/5.0"}}]'
        ];
        sampleJsonOutputs.forEach((output, index) => {
            const parsedResults = jsonParser.parseEscapedJson(output);
            if (parsedResults.length > 0) {
                webviewProvider.addParsedJson(parsedResults, `Simulated Terminal Output ${index + 1}`);
                dedicatedPanel.addParsedJson(parsedResults, `Simulated Terminal Output ${index + 1}`);
            }
        });
        vscode.window.showInformationMessage(`Simulated ${sampleJsonOutputs.length} terminal outputs with JSON`, 'Show Dedicated Tab').then(selection => {
            if (selection === 'Show Dedicated Tab') {
                dedicatedPanel.showPanel();
            }
        });
    });
    // Add subscriptions
    context.subscriptions.push(parseSelectionCommand, toggleParsingCommand, clearCacheCommand, showResultsCommand, openDedicatedPanelCommand, testPanelCommand, parseClipboardCommand, simulateTerminalJsonCommand, terminalMonitor, outputMonitor, documentMonitor, clipboardMonitor, fileSystemMonitor, decorationManager, dedicatedPanel);
    // Start monitoring if enabled
    if (config.isEnabled()) {
        terminalMonitor.startMonitoring();
        outputMonitor.startMonitoring();
        documentMonitor.startMonitoring();
        clipboardMonitor.startMonitoring();
        fileSystemMonitor.startMonitoring();
        // Show welcome message with automation info
        vscode.window.showInformationMessage('🚀 JSON Log Parser: Auto-monitoring enabled for clipboard, files, and terminals!', 'Show Settings', 'Test Now').then(selection => {
            if (selection === 'Show Settings') {
                vscode.commands.executeCommand('workbench.action.openSettings', 'jsonLogParser');
            }
            else if (selection === 'Test Now') {
                vscode.commands.executeCommand('jsonLogParser.simulateTerminalJson');
            }
        });
    }
    // Listen for configuration changes
    context.subscriptions.push(vscode.workspace.onDidChangeConfiguration(e => {
        if (e.affectsConfiguration('jsonLogParser')) {
            config.reload();
            if (config.isEnabled()) {
                terminalMonitor.startMonitoring();
                outputMonitor.startMonitoring();
                documentMonitor.startMonitoring();
                clipboardMonitor.startMonitoring();
                fileSystemMonitor.startMonitoring();
            }
            else {
                terminalMonitor.stopMonitoring();
                outputMonitor.stopMonitoring();
                documentMonitor.stopMonitoring();
                clipboardMonitor.stopMonitoring();
                fileSystemMonitor.stopMonitoring();
            }
        }
    }));
}
function deactivate() {
    console.log('JSON Log Parser extension is now deactivated');
}
//# sourceMappingURL=extension.js.map