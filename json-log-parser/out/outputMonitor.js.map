{"version": 3, "file": "outputMonitor.js", "sourceRoot": "", "sources": ["../src/outputMonitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAMjC,MAAa,aAAa;IAKtB,YACY,UAAsB,EACtB,iBAAoC,EACpC,eAAoC,EACpC,cAAsC;QAHtC,eAAU,GAAV,UAAU,CAAY;QACtB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,oBAAe,GAAf,eAAe,CAAqB;QACpC,mBAAc,GAAd,cAAc,CAAwB;QAR1C,gBAAW,GAAwB,EAAE,CAAC;QACtC,iBAAY,GAAG,KAAK,CAAC;QACrB,oBAAe,GAAG,IAAI,GAAG,EAAuC,CAAC;IAOtE,CAAC;IAEG,eAAe;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,qCAAqC;QACrC,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;YAC9E,IAAI,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClD,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE;YAC1E,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;QAExE,4DAA4D;QAC5D,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,YAAY,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,eAAe,CAAC,QAA6B;QACjD,8CAA8C;QAC9C,OAAO,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ;YAChC,QAAQ,CAAC,UAAU,KAAK,KAAK;YAC7B,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;IAEO,eAAe,CAAC,QAA6B;QACjD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO;QACX,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC7D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,0CAA0C;YAC1C,IAAI,CAAC,eAAe,CAAC,aAAa,CAC9B,aAAa,EACb,WAAW,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAC7C,CAAC;YAEF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAC7B,aAAa,EACb,WAAW,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAC7C,CAAC;YACN,CAAC;YAED,oCAAoC;YACpC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAEjE,+CAA+C;YAC/C,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACtC,IAAI,MAAM,CAAC,KAAK,CACZ,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,EACtC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CACvC,CACJ,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,CAAiC;QAC5D,yDAAyD;QACzD,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;QAC5B,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEjE,KAAK,MAAM,MAAM,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;YACpC,8DAA8D;YAC9D,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;YACjC,MAAM,kBAAkB,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACpD,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,SAAS,CAChD,CAAC;YAEF,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnE,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,6CAA6C;oBAC7C,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;oBACzD,MAAM,eAAe,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;wBACjD,GAAG,MAAM;wBACT,UAAU,EAAE,MAAM,CAAC,UAAU,GAAG,WAAW;wBAC3C,QAAQ,EAAE,MAAM,CAAC,QAAQ,GAAG,WAAW;qBAC1C,CAAC,CAAC,CAAC;oBAEJ,IAAI,CAAC,eAAe,CAAC,aAAa,CAC9B,eAAe,EACf,WAAW,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAC7C,CAAC;oBAEF,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;oBAEnE,0BAA0B;oBAC1B,MAAM,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAC3C,IAAI,MAAM,CAAC,KAAK,CACZ,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,EACtC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CACvC,CACJ,CAAC;oBACF,eAAe,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;oBACnC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;gBACxD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,QAA6B;QAChD,4CAA4C;QAC5C,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,SAAS,CAAC;QAC3D,CAAC;QACD,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,SAAS,CAAC;IAC3D,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AAvJD,sCAuJC"}