"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JsonParser = void 0;
class JsonParser {
    constructor(config) {
        // Regex patterns for different types of escaped JSON
        this.patterns = [
            // Double-escaped JSON in strings: "{\\"key\\":\\"value\\"}"
            /"(\{(?:\\.|[^"\\])*\})"/g,
            // JSON in single quotes with escaped double quotes
            /'(\{(?:\\.|[^'\\])*\})'/g,
            // JSON arrays
            /\[(?:[^\[\]]|"(?:\\.|[^"\\])*")*\]/g,
            // Single-escaped JSON: {"key":"value"} - more comprehensive pattern
            /\{(?:[^{}"]|"(?:[^"\\]|\\.)*"|\{(?:[^{}"]|"(?:[^"\\]|\\.)*")*\})*\}/g
        ];
        this.config = config;
    }
    /**
     * Parse escaped JSON from text content
     */
    parseEscapedJson(text) {
        if (!text || text.length > this.config.getMaxLogSize()) {
            return [];
        }
        const results = [];
        for (const pattern of this.patterns) {
            pattern.lastIndex = 0; // Reset regex state
            let match;
            while ((match = pattern.exec(text)) !== null) {
                const matchedText = match[0];
                const startIndex = match.index;
                const endIndex = startIndex + matchedText.length;
                // Try to parse the matched text
                const parseResult = this.tryParseJson(matchedText);
                if (parseResult) {
                    results.push({
                        original: matchedText,
                        parsed: parseResult.parsed,
                        formatted: parseResult.formatted,
                        startIndex,
                        endIndex,
                        isValid: true
                    });
                }
            }
        }
        // Remove duplicates and overlapping matches
        return this.removeDuplicates(results);
    }
    /**
     * Try to parse a potential JSON string
     */
    tryParseJson(text) {
        try {
            // Clean up the text - remove outer quotes and unescape
            let cleanText = text.trim();
            // Handle double-quoted strings
            if (cleanText.startsWith('"') && cleanText.endsWith('"')) {
                cleanText = cleanText.slice(1, -1);
            }
            // Handle single-quoted strings
            if (cleanText.startsWith("'") && cleanText.endsWith("'")) {
                cleanText = cleanText.slice(1, -1);
            }
            // Unescape the JSON
            cleanText = this.unescapeJson(cleanText);
            // Try to parse
            const parsed = JSON.parse(cleanText);
            // Only accept objects and arrays, not primitives
            if (typeof parsed === 'object' && parsed !== null) {
                const formatted = JSON.stringify(parsed, null, 2);
                return { parsed, formatted };
            }
            return null;
        }
        catch (error) {
            return null;
        }
    }
    /**
     * Unescape JSON string
     */
    unescapeJson(text) {
        return text
            .replace(/\\"/g, '"')
            .replace(/\\'/g, "'")
            .replace(/\\\\/g, '\\')
            .replace(/\\n/g, '\n')
            .replace(/\\r/g, '\r')
            .replace(/\\t/g, '\t');
    }
    /**
     * Remove duplicate and overlapping results
     */
    removeDuplicates(results) {
        if (results.length <= 1) {
            return results;
        }
        // Sort by start index
        results.sort((a, b) => a.startIndex - b.startIndex);
        const filtered = [];
        let lastEndIndex = -1;
        for (const result of results) {
            // Skip if this result overlaps with the previous one
            if (result.startIndex >= lastEndIndex) {
                filtered.push(result);
                lastEndIndex = result.endIndex;
            }
        }
        return filtered;
    }
    /**
     * Check if text contains potential JSON
     */
    containsJson(text) {
        if (!text || text.length > this.config.getMaxLogSize()) {
            return false;
        }
        // Quick check for JSON-like patterns
        return /[\{\[].*[\}\]]/.test(text) &&
            (text.includes('"') || text.includes("'"));
    }
    /**
     * Format JSON with syntax highlighting markers
     */
    formatWithHighlighting(json) {
        try {
            return JSON.stringify(json, null, 2);
        }
        catch (error) {
            return 'Invalid JSON';
        }
    }
}
exports.JsonParser = JsonParser;
//# sourceMappingURL=jsonParser.js.map