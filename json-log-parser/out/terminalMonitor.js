"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalMonitor = void 0;
const vscode = __importStar(require("vscode"));
class TerminalMonitor {
    constructor(jsonParser, decorationManager, webviewProvider, dedicatedPanel) {
        this.jsonParser = jsonParser;
        this.decorationManager = decorationManager;
        this.webviewProvider = webviewProvider;
        this.dedicatedPanel = dedicatedPanel;
        this.disposables = [];
        this.isMonitoring = false;
        this.terminalDataBuffer = new Map();
        this.debounceTimers = new Map();
    }
    startMonitoring() {
        if (this.isMonitoring) {
            return;
        }
        this.isMonitoring = true;
        // Monitor terminal data writes (using alternative approach for compatibility)
        const terminalDataDisposable = vscode.window.onDidOpenTerminal(terminal => {
            // For now, we'll monitor active terminal changes instead
            // This is a fallback approach since onDidWriteTerminalData may not be available
            this.monitorTerminal(terminal);
        });
        // Monitor terminal close events to clean up
        const terminalCloseDisposable = vscode.window.onDidCloseTerminal(terminal => {
            this.cleanupTerminal(terminal);
        });
        this.disposables.push(terminalDataDisposable, terminalCloseDisposable);
    }
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        this.isMonitoring = false;
        this.dispose();
    }
    monitorTerminal(terminal) {
        // Since we can't directly monitor terminal data writes in all VS Code versions,
        // we'll use a polling approach to check terminal content
        // This is a simplified approach for demonstration
        console.log(`Monitoring terminal: ${terminal.name}`);
    }
    handleTerminalData(terminal, data) {
        // Accumulate data for this terminal
        const currentBuffer = this.terminalDataBuffer.get(terminal) || '';
        const newBuffer = currentBuffer + data;
        this.terminalDataBuffer.set(terminal, newBuffer);
        // Clear existing debounce timer
        const existingTimer = this.debounceTimers.get(terminal);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }
        // Set new debounce timer to process data after a short delay
        const timer = setTimeout(() => {
            this.processTerminalBuffer(terminal);
        }, 500); // 500ms debounce
        this.debounceTimers.set(terminal, timer);
    }
    processTerminalBuffer(terminal) {
        const buffer = this.terminalDataBuffer.get(terminal);
        if (!buffer) {
            return;
        }
        // Check if buffer contains potential JSON
        if (!this.jsonParser.containsJson(buffer)) {
            this.terminalDataBuffer.set(terminal, '');
            return;
        }
        // Parse JSON from buffer
        const parsedResults = this.jsonParser.parseEscapedJson(buffer);
        if (parsedResults.length > 0) {
            // Update both webview and dedicated panel
            this.webviewProvider.addParsedJson(parsedResults, `Terminal: ${terminal.name}`);
            if (this.dedicatedPanel) {
                this.dedicatedPanel.addParsedJson(parsedResults, `Terminal: ${terminal.name}`);
            }
            // Show notification for significant findings
            if (parsedResults.length >= 2) {
                vscode.window.showInformationMessage(`Auto-detected ${parsedResults.length} JSON objects in terminal output`, 'Show Dedicated Tab', 'Show Output Panel').then(selection => {
                    if (selection === 'Show Dedicated Tab' && this.dedicatedPanel) {
                        this.dedicatedPanel.showPanel();
                    }
                    else if (selection === 'Show Output Panel') {
                        vscode.commands.executeCommand('workbench.view.extension.jsonLogParser');
                    }
                });
            }
        }
        // Keep only recent data in buffer (last 5000 characters)
        const maxBufferSize = 5000;
        if (buffer.length > maxBufferSize) {
            this.terminalDataBuffer.set(terminal, buffer.slice(-maxBufferSize));
        }
        else {
            this.terminalDataBuffer.set(terminal, '');
        }
    }
    cleanupTerminal(terminal) {
        this.terminalDataBuffer.delete(terminal);
        const timer = this.debounceTimers.get(terminal);
        if (timer) {
            clearTimeout(timer);
            this.debounceTimers.delete(terminal);
        }
    }
    dispose() {
        // Clear all timers
        for (const timer of this.debounceTimers.values()) {
            clearTimeout(timer);
        }
        this.debounceTimers.clear();
        // Clear buffers
        this.terminalDataBuffer.clear();
        // Dispose of event listeners
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.TerminalMonitor = TerminalMonitor;
//# sourceMappingURL=terminalMonitor.js.map