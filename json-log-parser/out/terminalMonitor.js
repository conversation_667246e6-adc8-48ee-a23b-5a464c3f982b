"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalMonitor = void 0;
const vscode = __importStar(require("vscode"));
class TerminalMonitor {
    constructor(jsonParser, decorationManager, webviewProvider, dedicatedPanel) {
        this.jsonParser = jsonParser;
        this.decorationManager = decorationManager;
        this.webviewProvider = webviewProvider;
        this.dedicatedPanel = dedicatedPanel;
        this.disposables = [];
        this.isMonitoring = false;
        this.terminalDataBuffer = new Map();
        this.debounceTimers = new Map();
    }
    startMonitoring() {
        if (this.isMonitoring) {
            return;
        }
        this.isMonitoring = true;
        // Try multiple approaches for terminal monitoring
        let terminalDataDisposable;
        // Method 1: Try onDidWriteTerminalData (VS Code 1.72+)
        try {
            if ('onDidWriteTerminalData' in vscode.window) {
                // @ts-ignore - This API might not be available in all VS Code versions
                terminalDataDisposable = vscode.window.onDidWriteTerminalData?.(e => {
                    this.handleTerminalData(e.terminal, e.data);
                });
                if (terminalDataDisposable) {
                    this.disposables.push(terminalDataDisposable);
                    console.log('JSON Log Parser: Using onDidWriteTerminalData API');
                }
            }
        }
        catch (error) {
            console.log('JSON Log Parser: onDidWriteTerminalData failed:', error);
        }
        // Method 2: Enhanced fallback with better terminal integration
        if (!terminalDataDisposable) {
            console.log('JSON Log Parser: Using enhanced fallback monitoring');
            this.setupEnhancedFallbackMonitoring();
        }
        // Monitor terminal close events to clean up
        const terminalCloseDisposable = vscode.window.onDidCloseTerminal(terminal => {
            this.cleanupTerminal(terminal);
        });
        this.disposables.push(terminalCloseDisposable);
    }
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        this.isMonitoring = false;
        this.dispose();
    }
    setupEnhancedFallbackMonitoring() {
        // Enhanced fallback: Multiple monitoring strategies
        // Strategy 1: Monitor when terminals are opened
        const terminalOpenDisposable = vscode.window.onDidOpenTerminal(terminal => {
            console.log(`JSON Log Parser: New terminal opened: ${terminal.name}`);
            this.setupTerminalCommands(terminal);
        });
        // Strategy 2: Monitor active terminal changes
        const activeTerminalDisposable = vscode.window.onDidChangeActiveTerminal(terminal => {
            if (terminal) {
                console.log(`JSON Log Parser: Active terminal changed: ${terminal.name}`);
                this.setupTerminalCommands(terminal);
            }
        });
        // Strategy 3: Provide manual parsing commands
        this.setupManualParsingCommands();
        this.disposables.push(terminalOpenDisposable, activeTerminalDisposable);
    }
    setupTerminalCommands(terminal) {
        // Show helpful message for terminal monitoring
        vscode.window.showInformationMessage(`JSON Log Parser: Monitoring terminal "${terminal.name}". Use "Parse Terminal Output" command to manually parse JSON.`, 'Test JSON Output', 'Parse Now').then(selection => {
            if (selection === 'Test JSON Output') {
                // Send a test command to the terminal
                terminal.sendText('echo "Test JSON: {\\"status\\":\\"success\\",\\"message\\":\\"Hello World\\",\\"timestamp\\":\\"' + new Date().toISOString() + '\\"}"');
            }
            else if (selection === 'Parse Now') {
                // Trigger manual parsing
                vscode.commands.executeCommand('jsonLogParser.parseTerminalOutput');
            }
        });
    }
    setupManualParsingCommands() {
        // Register command to manually parse terminal output
        const parseTerminalCommand = vscode.commands.registerCommand('jsonLogParser.parseTerminalOutput', async () => {
            const activeTerminal = vscode.window.activeTerminal;
            if (!activeTerminal) {
                vscode.window.showWarningMessage('No active terminal found');
                return;
            }
            // Since we can't directly read terminal content, ask user to copy-paste
            const input = await vscode.window.showInputBox({
                prompt: 'Paste terminal output containing JSON to parse',
                placeHolder: 'Paste your terminal output here...',
                ignoreFocusOut: true
            });
            if (input && input.trim()) {
                const parsedResults = this.jsonParser.parseEscapedJson(input);
                if (parsedResults.length > 0) {
                    this.webviewProvider.addParsedJson(parsedResults, `Terminal: ${activeTerminal.name} (Manual)`);
                    if (this.dedicatedPanel) {
                        this.dedicatedPanel.addParsedJson(parsedResults, `Terminal: ${activeTerminal.name} (Manual)`);
                    }
                    vscode.window.showInformationMessage(`Found ${parsedResults.length} JSON object(s) in terminal output`);
                }
                else {
                    vscode.window.showInformationMessage('No JSON found in the provided text');
                }
            }
        });
        this.disposables.push(parseTerminalCommand);
    }
    // Add a method to try parsing clipboard content from terminal
    async tryParseClipboardContent() {
        try {
            const clipboardText = await vscode.env.clipboard.readText();
            if (clipboardText && this.jsonParser.containsJson(clipboardText)) {
                const parsedResults = this.jsonParser.parseEscapedJson(clipboardText);
                if (parsedResults.length > 0) {
                    this.webviewProvider.addParsedJson(parsedResults, 'Clipboard Content');
                    if (this.dedicatedPanel) {
                        this.dedicatedPanel.addParsedJson(parsedResults, 'Clipboard Content');
                    }
                    vscode.window.showInformationMessage(`Found ${parsedResults.length} JSON object(s) in clipboard`);
                }
            }
        }
        catch (error) {
            console.log('JSON Log Parser: Failed to read clipboard:', error);
        }
    }
    handleTerminalData(terminal, data) {
        // Accumulate data for this terminal
        const currentBuffer = this.terminalDataBuffer.get(terminal) || '';
        const newBuffer = currentBuffer + data;
        this.terminalDataBuffer.set(terminal, newBuffer);
        // Clear existing debounce timer
        const existingTimer = this.debounceTimers.get(terminal);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }
        // Set new debounce timer to process data after a short delay
        const timer = setTimeout(() => {
            this.processTerminalBuffer(terminal);
        }, 500); // 500ms debounce
        this.debounceTimers.set(terminal, timer);
    }
    processTerminalBuffer(terminal) {
        const buffer = this.terminalDataBuffer.get(terminal);
        if (!buffer) {
            return;
        }
        // Check if buffer contains potential JSON
        if (!this.jsonParser.containsJson(buffer)) {
            this.terminalDataBuffer.set(terminal, '');
            return;
        }
        // Parse JSON from buffer
        const parsedResults = this.jsonParser.parseEscapedJson(buffer);
        if (parsedResults.length > 0) {
            // Update both webview and dedicated panel
            this.webviewProvider.addParsedJson(parsedResults, `Terminal: ${terminal.name}`);
            if (this.dedicatedPanel) {
                this.dedicatedPanel.addParsedJson(parsedResults, `Terminal: ${terminal.name}`);
            }
            // Show notification for significant findings
            if (parsedResults.length >= 2) {
                vscode.window.showInformationMessage(`Auto-detected ${parsedResults.length} JSON objects in terminal output`, 'Show Dedicated Tab', 'Show Output Panel').then(selection => {
                    if (selection === 'Show Dedicated Tab' && this.dedicatedPanel) {
                        this.dedicatedPanel.showPanel();
                    }
                    else if (selection === 'Show Output Panel') {
                        vscode.commands.executeCommand('workbench.view.extension.jsonLogParser');
                    }
                });
            }
        }
        // Keep only recent data in buffer (last 5000 characters)
        const maxBufferSize = 5000;
        if (buffer.length > maxBufferSize) {
            this.terminalDataBuffer.set(terminal, buffer.slice(-maxBufferSize));
        }
        else {
            this.terminalDataBuffer.set(terminal, '');
        }
    }
    cleanupTerminal(terminal) {
        this.terminalDataBuffer.delete(terminal);
        const timer = this.debounceTimers.get(terminal);
        if (timer) {
            clearTimeout(timer);
            this.debounceTimers.delete(terminal);
        }
    }
    dispose() {
        // Clear all timers
        for (const timer of this.debounceTimers.values()) {
            clearTimeout(timer);
        }
        this.debounceTimers.clear();
        // Clear buffers
        this.terminalDataBuffer.clear();
        // Dispose of event listeners
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.TerminalMonitor = TerminalMonitor;
//# sourceMappingURL=terminalMonitor.js.map