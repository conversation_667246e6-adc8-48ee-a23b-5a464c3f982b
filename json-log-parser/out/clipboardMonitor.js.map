{"version": 3, "file": "clipboardMonitor.js", "sourceRoot": "", "sources": ["../src/clipboardMonitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAMjC,MAAa,gBAAgB;IAMzB,YACY,UAAsB,EACtB,eAAoC,EACpC,MAA4B,EAC5B,cAAsC;QAHtC,eAAU,GAAV,UAAU,CAAY;QACtB,oBAAe,GAAf,eAAe,CAAqB;QACpC,WAAM,GAAN,MAAM,CAAsB;QAC5B,mBAAc,GAAd,cAAc,CAAwB;QAT1C,gBAAW,GAAwB,EAAE,CAAC;QACtC,iBAAY,GAAG,KAAK,CAAC;QACrB,yBAAoB,GAAG,EAAE,CAAC;IAQ/B,CAAC;IAEG,eAAe;QAClB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE,EAAE,CAAC;YAChE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAE9D,iCAAiC;QACjC,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC7C,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,oCAAoC;QACpC,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE;YACjE,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAChB,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1B,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC3C,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAE5D,+CAA+C;YAC/C,IAAI,CAAC,aAAa,IAAI,aAAa,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAChE,OAAO;YACX,CAAC;YAED,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC;YAE1C,mDAAmD;YACnD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC/C,OAAO;YACX,CAAC;YAED,4BAA4B;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEtE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,kCAAkC,aAAa,CAAC,MAAM,4BAA4B,CAAC,CAAC;gBAEhG,kBAAkB;gBAClB,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,aAAa,EAAE,2BAA2B,CAAC,CAAC;gBAC/E,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,EAAE,2BAA2B,CAAC,CAAC;gBAClF,CAAC;gBAED,2BAA2B;gBAC3B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,oBAAoB,aAAa,CAAC,MAAM,gCAAgC,EACxE,cAAc,CACjB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACf,IAAI,SAAS,KAAK,cAAc,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBACtD,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;oBACpC,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,uDAAuD;YACvD,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AAvGD,4CAuGC"}