{"version": 3, "file": "configuration.js", "sourceRoot": "", "sources": ["../src/configuration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,MAAa,oBAAoB;IAGtB,SAAS;QACZ,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAU,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAEM,UAAU,CAAC,OAAgB;QAC9B,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACnF,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAU,YAAY,EAAE,IAAI,CAAC,CAAC;IAC7D,CAAC;IAEM,iBAAiB;QACpB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAS,gBAAgB,EAAE,SAAS,CAAC,CAAC;IACrE,CAAC;IAEM,aAAa;QAChB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAS,YAAY,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAIM,uBAAuB;QAC1B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAU,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACpE,CAAC;IAEM,yBAAyB;QAC5B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAU,qBAAqB,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;IAEM,qBAAqB;QACxB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAU,iBAAiB,EAAE,IAAI,CAAC,CAAC;IAClE,CAAC;IAEM,2BAA2B;QAC9B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAS,0BAA0B,EAAE,IAAI,CAAC,CAAC;IAC1E,CAAC;IAEM,MAAM;QACT,qDAAqD;QACrD,sDAAsD;IAC1D,CAAC;IAEO,SAAS;QACb,OAAO,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC3E,CAAC;;AAhDL,oDAiDC;AAhD2B,4BAAO,GAAG,eAAe,CAAC"}