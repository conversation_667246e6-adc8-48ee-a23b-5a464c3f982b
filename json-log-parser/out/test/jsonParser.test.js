"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const jsonParser_1 = require("../jsonParser");
const configuration_1 = require("../configuration");
// Mock configuration for testing
class MockConfigurationManager extends configuration_1.ConfigurationManager {
    isEnabled() { return true; }
    isAutoDetectEnabled() { return true; }
    getHighlightColor() { return '#4CAF50'; }
    getMaxLogSize() { return 10000; }
    shouldShowInSidebar() { return true; }
}
describe('JsonParser Test Suite', () => {
    let parser;
    beforeEach(() => {
        const config = new MockConfigurationManager();
        parser = new jsonParser_1.JsonParser(config);
    });
    it('should parse double-escaped JSON', () => {
        const input = 'Log entry: "{\\"name\\":\\"John\\",\\"age\\":30}"';
        const results = parser.parseEscapedJson(input);
        assert.strictEqual(results.length, 1);
        assert.strictEqual(results[0].parsed.name, 'John');
        assert.strictEqual(results[0].parsed.age, 30);
        assert.strictEqual(results[0].isValid, true);
    });
    it('should parse single-escaped JSON', () => {
        const input = 'API Response: {"status":"success","data":{"id":123}}';
        const results = parser.parseEscapedJson(input);
        assert.strictEqual(results.length, 1);
        assert.strictEqual(results[0].parsed.status, 'success');
        assert.strictEqual(results[0].parsed.data.id, 123);
    });
    it('should parse JSON array', () => {
        const input = 'Items: [{"id":1,"name":"Item1"},{"id":2,"name":"Item2"}]';
        const results = parser.parseEscapedJson(input);
        assert.strictEqual(results.length, 1);
        assert.strictEqual(Array.isArray(results[0].parsed), true);
        assert.strictEqual(results[0].parsed.length, 2);
        assert.strictEqual(results[0].parsed[0].name, 'Item1');
    });
    it('should handle invalid JSON gracefully', () => {
        const input = 'Invalid: {"name":"John",age:30}'; // Missing quotes around age
        const results = parser.parseEscapedJson(input);
        // Should not crash and return empty results
        assert.strictEqual(results.length, 0);
    });
    it('should detect JSON presence', () => {
        const validInput = 'Log: {"status":"ok"}';
        const invalidInput = 'Just a regular log message';
        assert.strictEqual(parser.containsJson(validInput), true);
        assert.strictEqual(parser.containsJson(invalidInput), false);
    });
    it('should handle multiple JSON objects', () => {
        const input = 'First: {"a":1} Second: {"b":2}';
        const results = parser.parseEscapedJson(input);
        assert.strictEqual(results.length, 2);
        assert.strictEqual(results[0].parsed.a, 1);
        assert.strictEqual(results[1].parsed.b, 2);
    });
    it('should parse OpenAI API log example', () => {
        const input = 'OpenAI Request: "{\\"model\\":\\"gpt-4\\",\\"messages\\":[{\\"role\\":\\"user\\",\\"content\\":\\"Hello\\"}]}"';
        const results = parser.parseEscapedJson(input);
        assert.strictEqual(results.length, 1);
        assert.strictEqual(results[0].parsed.model, 'gpt-4');
        assert.strictEqual(results[0].parsed.messages[0].role, 'user');
        assert.strictEqual(results[0].parsed.messages[0].content, 'Hello');
    });
});
//# sourceMappingURL=jsonParser.test.js.map