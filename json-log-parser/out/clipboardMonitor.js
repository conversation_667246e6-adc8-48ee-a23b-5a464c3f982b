"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClipboardMonitor = void 0;
const vscode = __importStar(require("vscode"));
class ClipboardMonitor {
    constructor(jsonParser, webviewProvider, config, dedicatedPanel) {
        this.jsonParser = jsonParser;
        this.webviewProvider = webviewProvider;
        this.config = config;
        this.dedicatedPanel = dedicatedPanel;
        this.disposables = [];
        this.isMonitoring = false;
        this.lastClipboardContent = '';
    }
    startMonitoring() {
        if (this.isMonitoring || !this.config.shouldAutoDetectClipboard()) {
            return;
        }
        this.isMonitoring = true;
        console.log('JSON Log Parser: Starting clipboard monitoring');
        // Poll clipboard every 2 seconds
        this.monitoringInterval = setInterval(async () => {
            await this.checkClipboard();
        }, 2000);
        // Also check when window gets focus
        const focusDisposable = vscode.window.onDidChangeWindowState(state => {
            if (state.focused) {
                this.checkClipboard();
            }
        });
        this.disposables.push(focusDisposable);
    }
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        this.isMonitoring = false;
        console.log('JSON Log Parser: Stopping clipboard monitoring');
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
        }
        this.dispose();
    }
    async checkClipboard() {
        try {
            const clipboardText = await vscode.env.clipboard.readText();
            // Skip if clipboard hasn't changed or is empty
            if (!clipboardText || clipboardText === this.lastClipboardContent) {
                return;
            }
            this.lastClipboardContent = clipboardText;
            // Quick check if clipboard contains potential JSON
            if (!this.jsonParser.containsJson(clipboardText)) {
                return;
            }
            // Parse JSON from clipboard
            const parsedResults = this.jsonParser.parseEscapedJson(clipboardText);
            if (parsedResults.length > 0) {
                console.log(`JSON Log Parser: Auto-detected ${parsedResults.length} JSON objects in clipboard`);
                // Update displays
                this.webviewProvider.addParsedJson(parsedResults, 'Clipboard (Auto-detected)');
                if (this.dedicatedPanel) {
                    this.dedicatedPanel.addParsedJson(parsedResults, 'Clipboard (Auto-detected)');
                }
                // Show subtle notification
                vscode.window.showInformationMessage(`📋 Auto-detected ${parsedResults.length} JSON object(s) from clipboard`, 'Show Results').then(selection => {
                    if (selection === 'Show Results' && this.dedicatedPanel) {
                        this.dedicatedPanel.showPanel();
                    }
                });
            }
        }
        catch (error) {
            // Silently fail - clipboard access might be restricted
            console.log('JSON Log Parser: Clipboard access failed:', error);
        }
    }
    dispose() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.ClipboardMonitor = ClipboardMonitor;
//# sourceMappingURL=clipboardMonitor.js.map