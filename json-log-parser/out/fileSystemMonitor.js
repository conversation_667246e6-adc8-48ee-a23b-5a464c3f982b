"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSystemMonitor = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class FileSystemMonitor {
    constructor(jsonParser, webviewProvider, config, dedicatedPanel) {
        this.jsonParser = jsonParser;
        this.webviewProvider = webviewProvider;
        this.config = config;
        this.dedicatedPanel = dedicatedPanel;
        this.disposables = [];
        this.isMonitoring = false;
        this.fileWatchers = [];
        this.processedFiles = new Map(); // file path -> last modified time
    }
    startMonitoring() {
        if (this.isMonitoring || !this.config.shouldAutoDetectFiles()) {
            return;
        }
        this.isMonitoring = true;
        console.log('JSON Log Parser: Starting file system monitoring');
        this.setupLogFileWatchers();
        this.scanExistingLogFiles();
    }
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        this.isMonitoring = false;
        console.log('JSON Log Parser: Stopping file system monitoring');
        this.dispose();
    }
    setupLogFileWatchers() {
        // Watch for common log file patterns
        const logPatterns = [
            '**/*.log',
            '**/*.out',
            '**/*.err',
            '**/logs/**/*',
            '**/*log*.txt',
            '**/*.json.log'
        ];
        logPatterns.forEach(pattern => {
            const watcher = vscode.workspace.createFileSystemWatcher(pattern);
            // Watch for file changes
            watcher.onDidChange(uri => {
                this.handleFileChange(uri);
            });
            // Watch for new files
            watcher.onDidCreate(uri => {
                this.handleFileChange(uri);
            });
            this.fileWatchers.push(watcher);
            this.disposables.push(watcher);
        });
    }
    async scanExistingLogFiles() {
        try {
            // Find existing log files in workspace
            const logFiles = await vscode.workspace.findFiles('**/*.{log,out,err,txt}', '**/node_modules/**', 50 // Limit to 50 files
            );
            for (const fileUri of logFiles) {
                await this.processLogFile(fileUri);
            }
        }
        catch (error) {
            console.log('JSON Log Parser: Error scanning existing log files:', error);
        }
    }
    async handleFileChange(uri) {
        // Debounce file changes
        setTimeout(() => {
            this.processLogFile(uri);
        }, 1000);
    }
    async processLogFile(uri) {
        try {
            const filePath = uri.fsPath;
            const fileName = path.basename(filePath);
            // Skip if file is too large (> 1MB)
            const stat = await vscode.workspace.fs.stat(uri);
            if (stat.size > 1024 * 1024) {
                return;
            }
            // Check if file was already processed recently
            const lastModified = stat.mtime;
            const lastProcessed = this.processedFiles.get(filePath);
            if (lastProcessed && lastProcessed >= lastModified) {
                return;
            }
            // Read file content
            const fileContent = await vscode.workspace.fs.readFile(uri);
            const text = Buffer.from(fileContent).toString('utf8');
            // Quick check for JSON content
            if (!this.jsonParser.containsJson(text)) {
                return;
            }
            // Parse JSON from file
            const parsedResults = this.jsonParser.parseEscapedJson(text);
            if (parsedResults.length > 0) {
                console.log(`JSON Log Parser: Auto-detected ${parsedResults.length} JSON objects in ${fileName}`);
                // Update displays
                this.webviewProvider.addParsedJson(parsedResults, `File: ${fileName} (Auto-detected)`);
                if (this.dedicatedPanel) {
                    this.dedicatedPanel.addParsedJson(parsedResults, `File: ${fileName} (Auto-detected)`);
                }
                // Show notification for significant findings
                if (parsedResults.length >= 3) {
                    vscode.window.showInformationMessage(`📁 Auto-detected ${parsedResults.length} JSON objects in ${fileName}`, 'Show Results').then(selection => {
                        if (selection === 'Show Results' && this.dedicatedPanel) {
                            this.dedicatedPanel.showPanel();
                        }
                    });
                }
                // Mark file as processed
                this.processedFiles.set(filePath, lastModified);
            }
        }
        catch (error) {
            console.log('JSON Log Parser: Error processing log file:', error);
        }
    }
    dispose() {
        this.fileWatchers.forEach(watcher => watcher.dispose());
        this.fileWatchers = [];
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
        this.processedFiles.clear();
    }
}
exports.FileSystemMonitor = FileSystemMonitor;
//# sourceMappingURL=fileSystemMonitor.js.map