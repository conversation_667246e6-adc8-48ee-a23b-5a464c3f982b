{"version": 3, "file": "dedicatedPanelManager.js", "sourceRoot": "", "sources": ["../src/dedicatedPanelManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,MAAa,qBAAqB;IAU9B,YAAoB,YAAwB;QAAxB,iBAAY,GAAZ,YAAY,CAAY;QARpC,SAAI,GAKP,EAAE,CAAC;QACA,YAAO,GAAG,GAAG,CAAC;IAEyB,CAAC;IAEzC,SAAS;QACZ,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,0CAA0C;YAC1C,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YACzD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC5C,OAAO;QACX,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEnD,mBAAmB;QACnB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACzC,mBAAmB,EACnB,gBAAgB,EAChB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;SAC1C,CACJ,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAE3D,uBAAuB;QACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEnD,mCAAmC;QACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAClC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,OAAO;oBACR,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;oBAChE,IAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,MAAM;gBACV,KAAK,MAAM;oBACP,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC7C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;oBAC5D,MAAM;gBACV,KAAK,OAAO;oBACR,IAAI,CAAC,SAAS,EAAE,CAAC;oBACjB,MAAM;YACd,CAAC;QACL,CAAC,CACJ,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,oDAAoD;QACpD,UAAU,CAAC,GAAG,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAEM,aAAa,CAAC,OAA2B,EAAE,MAAc;QAC5D,OAAO,CAAC,GAAG,CAAC,2BAA2B,OAAO,CAAC,MAAM,iBAAiB,MAAM,EAAE,CAAC,CAAC;QAEhF,MAAM,KAAK,GAAG;YACV,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM;YACN,OAAO;SACV,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAEzB,iCAAiC;QACjC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAEpE,kCAAkC;QAClC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YACxD,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QACpE,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAEM,SAAS;QACZ,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,mDAAmD,IAAI,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;YACxF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,IAAI,CAAC,IAAI;aAClB,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAsSP,CAAC;IACL,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;IACL,CAAC;CACJ;AAvaD,sDAuaC"}