"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DedicatedPanelManager = void 0;
const vscode = __importStar(require("vscode"));
class DedicatedPanelManager {
    constructor(extensionUri) {
        this.extensionUri = extensionUri;
        this.logs = [];
        this.maxLogs = 100;
    }
    showPanel() {
        console.log('JSON Log Parser: showPanel called');
        if (this.panel) {
            // If panel already exists, just reveal it
            console.log('JSON Log Parser: Revealing existing panel');
            this.panel.reveal(vscode.ViewColumn.Beside);
            return;
        }
        console.log('JSON Log Parser: Creating new panel');
        // Create new panel
        this.panel = vscode.window.createWebviewPanel('jsonParserResults', '📋 JSON Parser', vscode.ViewColumn.Beside, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [this.extensionUri]
        });
        console.log('JSON Log Parser: Panel created successfully');
        // Set the HTML content
        this.panel.webview.html = this.getWebviewContent();
        // Handle messages from the webview
        this.panel.webview.onDidReceiveMessage(message => {
            switch (message.type) {
                case 'ready':
                    console.log('JSON Log Parser: Webview ready, updating content');
                    this.updateWebview();
                    break;
                case 'copy':
                    vscode.env.clipboard.writeText(message.text);
                    vscode.window.showInformationMessage('Copied to clipboard');
                    break;
                case 'clear':
                    this.clearLogs();
                    break;
            }
        });
        // Handle panel disposal
        this.panel.onDidDispose(() => {
            this.panel = undefined;
        });
        // Wait for webview to be ready, then update content
        setTimeout(() => {
            console.log('JSON Log Parser: Delayed webview update');
            this.updateWebview();
        }, 100);
    }
    addParsedJson(results, source) {
        console.log(`JSON Log Parser: Adding ${results.length} results from ${source}`);
        const entry = {
            id: Date.now().toString(),
            timestamp: new Date(),
            source,
            results
        };
        this.logs.unshift(entry);
        // Keep only the most recent logs
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(0, this.maxLogs);
        }
        console.log(`JSON Log Parser: Total logs now: ${this.logs.length}`);
        // Update webview if panel is open
        if (this.panel) {
            console.log('JSON Log Parser: Updating existing panel');
            this.updateWebview();
        }
        else {
            console.log('JSON Log Parser: Panel not open, will show panel');
        }
        // Auto-show panel when new results are added
        this.showPanel();
    }
    clearLogs() {
        this.logs = [];
        this.updateWebview();
    }
    updateWebview() {
        if (this.panel) {
            console.log(`JSON Log Parser: Sending update to webview with ${this.logs.length} logs`);
            this.panel.webview.postMessage({
                type: 'update',
                logs: this.logs
            });
        }
        else {
            console.log('JSON Log Parser: Cannot update webview - panel is null');
        }
    }
    getWebviewContent() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Parser Results</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 0;
            line-height: 1.4;
        }
        
        .header {
            background-color: var(--vscode-titleBar-activeBackground);
            color: var(--vscode-titleBar-activeForeground);
            padding: 12px 16px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            font-family: inherit;
        }
        
        .btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .btn-secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        
        .btn-secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        
        .content {
            padding: 16px;
            max-height: calc(100vh - 60px);
            overflow-y: auto;
        }
        
        .log-entry {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 6px;
            margin-bottom: 16px;
            overflow: hidden;
            background-color: var(--vscode-editor-background);
        }
        
        .log-header {
            background-color: var(--vscode-editor-lineHighlightBackground);
            padding: 10px 16px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .log-source {
            font-weight: 600;
            color: var(--vscode-textLink-foreground);
            font-size: 14px;
        }
        
        .log-timestamp {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            font-family: var(--vscode-editor-font-family);
        }
        
        .json-result {
            padding: 16px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        
        .json-result:last-child {
            border-bottom: none;
        }
        
        .json-label {
            font-size: 12px;
            font-weight: 600;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .json-content {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 12px;
            font-family: var(--vscode-editor-font-family);
            font-size: 13px;
            white-space: pre-wrap;
            overflow-x: auto;
            word-break: break-all;
        }
        
        .json-formatted {
            color: var(--vscode-editor-foreground);
        }
        
        .json-original {
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }
        
        .actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }
        
        .empty-state {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            padding: 60px 20px;
            background-color: var(--vscode-editor-background);
            border-radius: 8px;
            border: 2px dashed var(--vscode-panel-border);
        }
        
        .empty-state h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            color: var(--vscode-foreground);
        }
        
        .empty-state p {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .stats {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <h1>📋 JSON Parser Results</h1>
            <span class="stats" id="stats">No results yet</span>
        </div>
        <div class="header-actions">
            <button class="btn btn-secondary" onclick="clearAll()">Clear All</button>
        </div>
    </div>
    
    <div class="content">
        <div id="content">
            <div class="empty-state">
                <h3>🔍 Ready to Parse JSON</h3>
                <p>Select text containing escaped JSON and use the "Parse Selected JSON" command.</p>
                <p>Results will appear here in this dedicated tab.</p>
                <p><strong>This tab sits alongside Terminal, Output, and Problems tabs for easy access.</strong></p>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        console.log('JSON Parser Webview: Script loaded');

        // Notify extension that webview is ready
        vscode.postMessage({
            type: 'ready'
        });
        
        window.addEventListener('message', event => {
            const message = event.data;
            console.log('JSON Parser Webview: Received message', message);
            switch (message.type) {
                case 'update':
                    console.log('JSON Parser Webview: Updating content with', message.logs.length, 'logs');
                    updateContent(message.logs);
                    break;
            }
        });
        
        function updateContent(logs) {
            console.log('JSON Parser Webview: updateContent called with', logs);
            const content = document.getElementById('content');
            const stats = document.getElementById('stats');

            if (!logs || logs.length === 0) {
                console.log('JSON Parser Webview: No logs, showing empty state');
                content.innerHTML = \`
                    <div class="empty-state">
                        <h3>🔍 Ready to Parse JSON</h3>
                        <p>Select text containing escaped JSON and use the "Parse Selected JSON" command.</p>
                        <p>Results will appear here in this dedicated tab.</p>
                        <p><strong>This tab sits alongside Terminal, Output, and Problems tabs for easy access.</strong></p>
                    </div>
                \`;
                stats.textContent = 'No results yet';
                return;
            }

            console.log('JSON Parser Webview: Rendering', logs.length, 'logs');
            
            const totalResults = logs.reduce((sum, log) => sum + log.results.length, 0);
            stats.textContent = \`\${logs.length} session(s), \${totalResults} JSON object(s)\`;
            
            content.innerHTML = logs.map(log => \`
                <div class="log-entry">
                    <div class="log-header">
                        <span class="log-source">\${escapeHtml(log.source)}</span>
                        <span class="log-timestamp">\${new Date(log.timestamp).toLocaleString()}</span>
                    </div>
                    \${log.results.map((result, index) => \`
                        <div class="json-result">
                            <div class="json-label">📋 JSON Object \${index + 1}</div>
                            
                            <div class="json-label">✨ Formatted JSON:</div>
                            <div class="json-content json-formatted">\${escapeHtml(result.formatted)}</div>
                            
                            <div class="json-label">📝 Original Text:</div>
                            <div class="json-content json-original">\${escapeHtml(result.original)}</div>
                            
                            <div class="actions">
                                <button class="btn" data-formatted="\${escapeHtml(result.formatted)}" onclick="copyFormattedText(this)">Copy Formatted</button>
                                <button class="btn btn-secondary" data-original="\${escapeHtml(result.original)}" onclick="copyOriginalText(this)">Copy Original</button>
                            </div>
                        </div>
                    \`).join('')}
                </div>
            \`).join('');
        }
        
        function copyFormattedText(button) {
            const text = button.getAttribute('data-formatted');
            vscode.postMessage({
                type: 'copy',
                text: text
            });
        }

        function copyOriginalText(button) {
            const text = button.getAttribute('data-original');
            vscode.postMessage({
                type: 'copy',
                text: text
            });
        }
        
        function clearAll() {
            vscode.postMessage({
                type: 'clear'
            });
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>`;
    }
    dispose() {
        if (this.panel) {
            this.panel.dispose();
        }
    }
}
exports.DedicatedPanelManager = DedicatedPanelManager;
//# sourceMappingURL=dedicatedPanelManager.js.map