{"version": 3, "file": "documentMonitor.js", "sourceRoot": "", "sources": ["../src/documentMonitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAOjC,MAAa,eAAe;IAMxB,YACY,UAAsB,EACtB,iBAAoC,EACpC,MAA4B,EAC5B,cAAsC;QAHtC,eAAU,GAAV,UAAU,CAAY;QACtB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,WAAM,GAAN,MAAM,CAAsB;QAC5B,mBAAc,GAAd,cAAc,CAAwB;QAT1C,gBAAW,GAAwB,EAAE,CAAC;QACtC,iBAAY,GAAG,KAAK,CAAC;QACrB,uBAAkB,GAAG,IAAI,GAAG,EAA+B,CAAC;IAQjE,CAAC;IAEG,eAAe;QAClB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,EAAE,CAAC;YAC9D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,qCAAqC;QACrC,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;YAC9E,IAAI,MAAM,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC/C,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE;YAC1E,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;QAExE,kCAAkC;QAClC,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,YAAY,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpE,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,qBAAqB,CAAC,QAA6B;QACvD,gFAAgF;QAChF,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC9C,MAAM,kBAAkB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QAC9F,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAE3D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,oBAAoB;QACpB,IAAI,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,uBAAuB;QACvB,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QACjD,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACxD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,mCAAmC;QACnC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,oBAAoB,CAAC,QAA6B;QACtD,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrC,CAAC;QAED,kCAAkC;QAClC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;YACjC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,oBAAoB;IAClC,CAAC;IAEO,YAAY,CAAC,QAA6B;QAC9C,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;YAC/B,OAAO;QACX,CAAC;QAED,gEAAgE;QAChE,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC;QACxC,MAAM,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,oBAAoB,KAAK,cAAc,EAAE,CAAC;YAC1C,OAAO;QACX,CAAC;QAED,kDAAkD;QAClD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,OAAO;QACX,CAAC;QAED,2BAA2B;QAC3B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAE7D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEhD,8BAA8B;YAC9B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,QAAQ,EAAE,CAAC,CAAC;YAC1E,CAAC;YAED,oCAAoC;YACpC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAEjE,2CAA2C;YAC3C,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,iBAAiB,aAAa,CAAC,MAAM,oBAAoB,QAAQ,EAAE,EACnE,oBAAoB,EACpB,mBAAmB,CACtB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACf,IAAI,SAAS,KAAK,oBAAoB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBAC5D,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;oBACpC,CAAC;yBAAM,IAAI,SAAS,KAAK,mBAAmB,EAAE,CAAC;wBAC3C,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wCAAwC,CAAC,CAAC;oBAC7E,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;YAED,6BAA6B;YAC7B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,QAA6B;QACjD,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACrC,OAAO,UAAU,CAAC;QACtB,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,SAAS,CAAC;QAC9E,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AAtKD,0CAsKC"}