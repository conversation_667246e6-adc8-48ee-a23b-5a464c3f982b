{"version": 3, "file": "fileSystemMonitor.js", "sourceRoot": "", "sources": ["../src/fileSystemMonitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAM7B,MAAa,iBAAiB;IAM1B,YACY,UAAsB,EACtB,eAAoC,EACpC,MAA4B,EAC5B,cAAsC;QAHtC,eAAU,GAAV,UAAU,CAAY;QACtB,oBAAe,GAAf,eAAe,CAAqB;QACpC,WAAM,GAAN,MAAM,CAAsB;QAC5B,mBAAc,GAAd,cAAc,CAAwB;QAT1C,gBAAW,GAAwB,EAAE,CAAC;QACtC,iBAAY,GAAG,KAAK,CAAC;QACrB,iBAAY,GAA+B,EAAE,CAAC;QAC9C,mBAAc,GAAG,IAAI,GAAG,EAAkB,CAAC,CAAC,kCAAkC;IAOnF,CAAC;IAEG,eAAe;QAClB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAC5D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAEhE,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,oBAAoB;QACxB,qCAAqC;QACrC,MAAM,WAAW,GAAG;YAChB,UAAU;YACV,UAAU;YACV,UAAU;YACV,cAAc;YACd,cAAc;YACd,eAAe;SAClB,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC1B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAElE,yBAAyB;YACzB,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,sBAAsB;YACtB,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;gBACtB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,IAAI,CAAC;YACD,uCAAuC;YACvC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAC7C,wBAAwB,EACxB,oBAAoB,EACpB,EAAE,CAAC,oBAAoB;aAC1B,CAAC;YAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,qDAAqD,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAe;QAC1C,wBAAwB;QACxB,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,GAAe;QACxC,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEzC,oCAAoC;YACpC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;gBAC1B,OAAO;YACX,CAAC;YAED,+CAA+C;YAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,aAAa,IAAI,aAAa,IAAI,YAAY,EAAE,CAAC;gBACjD,OAAO;YACX,CAAC;YAED,oBAAoB;YACpB,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEvD,+BAA+B;YAC/B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,OAAO;YACX,CAAC;YAED,uBAAuB;YACvB,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAE7D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,kCAAkC,aAAa,CAAC,MAAM,oBAAoB,QAAQ,EAAE,CAAC,CAAC;gBAElG,kBAAkB;gBAClB,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,QAAQ,kBAAkB,CAAC,CAAC;gBACvF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,EAAE,SAAS,QAAQ,kBAAkB,CAAC,CAAC;gBAC1F,CAAC;gBAED,6CAA6C;gBAC7C,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,oBAAoB,aAAa,CAAC,MAAM,oBAAoB,QAAQ,EAAE,EACtE,cAAc,CACjB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;wBACf,IAAI,SAAS,KAAK,cAAc,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;4BACtD,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;wBACpC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC;gBAED,yBAAyB;gBACzB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YACpD,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;CACJ;AA1JD,8CA0JC"}