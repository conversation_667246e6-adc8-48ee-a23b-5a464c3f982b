{"version": 3, "file": "terminalMonitor.js", "sourceRoot": "", "sources": ["../src/terminalMonitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAMjC,MAAa,eAAe;IAMxB,YACY,UAAsB,EACtB,iBAAoC,EACpC,eAAoC,EACpC,cAAsC;QAHtC,eAAU,GAAV,UAAU,CAAY;QACtB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,oBAAe,GAAf,eAAe,CAAqB;QACpC,mBAAc,GAAd,cAAc,CAAwB;QAT1C,gBAAW,GAAwB,EAAE,CAAC;QACtC,iBAAY,GAAG,KAAK,CAAC;QACrB,uBAAkB,GAAG,IAAI,GAAG,EAA2B,CAAC;QACxD,mBAAc,GAAG,IAAI,GAAG,EAAmC,CAAC;IAOjE,CAAC;IAEG,eAAe;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,8EAA8E;QAC9E,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;YACtE,yDAAyD;YACzD,gFAAgF;YAChF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,4CAA4C;QAC5C,MAAM,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACxE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,EAAE,uBAAuB,CAAC,CAAC;IAC3E,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,eAAe,CAAC,QAAyB;QAC7C,gFAAgF;QAChF,yDAAyD;QACzD,kDAAkD;QAClD,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;IACzD,CAAC;IAEO,kBAAkB,CAAC,QAAyB,EAAE,IAAY;QAC9D,oCAAoC;QACpC,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAClE,MAAM,SAAS,GAAG,aAAa,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEjD,gCAAgC;QAChC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,aAAa,EAAE,CAAC;YAChB,YAAY,CAAC,aAAa,CAAC,CAAC;QAChC,CAAC;QAED,6DAA6D;QAC7D,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC1B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,iBAAiB;QAE1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAEO,qBAAqB,CAAC,QAAyB;QACnD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAC1C,OAAO;QACX,CAAC;QAED,yBAAyB;QACzB,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAE/D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,0CAA0C;YAC1C,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAChF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACnF,CAAC;YAED,6CAA6C;YAC7C,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,iBAAiB,aAAa,CAAC,MAAM,kCAAkC,EACvE,oBAAoB,EACpB,mBAAmB,CACtB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACf,IAAI,SAAS,KAAK,oBAAoB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBAC5D,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;oBACpC,CAAC;yBAAM,IAAI,SAAS,KAAK,mBAAmB,EAAE,CAAC;wBAC3C,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wCAAwC,CAAC,CAAC;oBAC7E,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,yDAAyD;QACzD,MAAM,aAAa,GAAG,IAAI,CAAC;QAC3B,IAAI,MAAM,CAAC,MAAM,GAAG,aAAa,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,QAAyB;QAC7C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEzC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,KAAK,EAAE,CAAC;YACR,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAEM,OAAO;QACV,mBAAmB;QACnB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,YAAY,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,gBAAgB;QAChB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEhC,6BAA6B;QAC7B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AA9ID,0CA8IC"}