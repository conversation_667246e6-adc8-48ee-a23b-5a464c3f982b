{"version": 3, "file": "terminalMonitor.js", "sourceRoot": "", "sources": ["../src/terminalMonitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAMjC,MAAa,eAAe;IAMxB,YACY,UAAsB,EACtB,iBAAoC,EACpC,eAAoC,EACpC,cAAsC;QAHtC,eAAU,GAAV,UAAU,CAAY;QACtB,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,oBAAe,GAAf,eAAe,CAAqB;QACpC,mBAAc,GAAd,cAAc,CAAwB;QAT1C,gBAAW,GAAwB,EAAE,CAAC;QACtC,iBAAY,GAAG,KAAK,CAAC;QACrB,uBAAkB,GAAG,IAAI,GAAG,EAA2B,CAAC;QACxD,mBAAc,GAAG,IAAI,GAAG,EAAmC,CAAC;IAOjE,CAAC;IAEG,eAAe;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,kDAAkD;QAClD,IAAI,sBAAqD,CAAC;QAE1D,uDAAuD;QACvD,IAAI,CAAC;YACD,IAAI,wBAAwB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC5C,uEAAuE;gBACvE,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,EAAE;oBAChE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;gBAEH,IAAI,sBAAsB,EAAE,CAAC;oBACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;oBAC9C,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;gBACrE,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;QAED,+DAA+D;QAC/D,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YACnE,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAC3C,CAAC;QAED,4CAA4C;QAC5C,MAAM,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACxE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACnD,CAAC;IAEM,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,+BAA+B;QACnC,oDAAoD;QAEpD,gDAAgD;QAChD,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;YACtE,OAAO,CAAC,GAAG,CAAC,yCAAyC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACtE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,8CAA8C;QAC9C,MAAM,wBAAwB,GAAG,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAAC,QAAQ,CAAC,EAAE;YAChF,IAAI,QAAQ,EAAE,CAAC;gBACX,OAAO,CAAC,GAAG,CAAC,6CAA6C,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC1E,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,8CAA8C;QAC9C,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;IAC5E,CAAC;IAEO,qBAAqB,CAAC,QAAyB;QACnD,6CAA6C;QAC7C,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEpD,wCAAwC;QACxC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,mHAAmH,EACnH,WAAW,EACX,UAAU,CACb,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,IAAI,SAAS,KAAK,WAAW,EAAE,CAAC;gBAC5B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;iBAAM,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;gBAClC,6DAA6D;gBAC7D,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBAC3C,QAAQ,CAAC,QAAQ,CAAC,sEAAsE,SAAS,2DAA2D,CAAC,CAAC;YAClK,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB,CAAC,QAAyB;QAC9C,MAAM,IAAI,GAAG;;;;;;;;;;;;;;;;;SAiBZ,CAAC;QAEF,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;YAC9B,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,UAAU;SACvB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,0BAA0B;QAC9B,qDAAqD;QACrD,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACzG,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YACpD,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;gBAC7D,OAAO;YACX,CAAC;YAED,wEAAwE;YACxE,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC3C,MAAM,EAAE,gDAAgD;gBACxD,WAAW,EAAE,oCAAoC;gBACjD,cAAc,EAAE,IAAI;aACvB,CAAC,CAAC;YAEH,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;gBACxB,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAC9D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,cAAc,CAAC,IAAI,WAAW,CAAC,CAAC;oBAC/F,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,cAAc,CAAC,IAAI,WAAW,CAAC,CAAC;oBAClG,CAAC;oBACD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,aAAa,CAAC,MAAM,oCAAoC,CAAC,CAAC;gBAC5G,CAAC;qBAAM,CAAC;oBACJ,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;gBAC/E,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAChD,CAAC;IAED,8DAA8D;IACtD,KAAK,CAAC,wBAAwB;QAClC,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC5D,IAAI,aAAa,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC/D,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBACtE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;oBACvE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;oBAC1E,CAAC;oBACD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,aAAa,CAAC,MAAM,8BAA8B,CAAC,CAAC;gBACtG,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,QAAyB,EAAE,IAAY;QAC9D,oCAAoC;QACpC,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAClE,MAAM,SAAS,GAAG,aAAa,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEjD,gCAAgC;QAChC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,aAAa,EAAE,CAAC;YAChB,YAAY,CAAC,aAAa,CAAC,CAAC;QAChC,CAAC;QAED,6DAA6D;QAC7D,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YAC1B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,iBAAiB;QAE1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAEO,qBAAqB,CAAC,QAAyB;QACnD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAC1C,OAAO;QACX,CAAC;QAED,yBAAyB;QACzB,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAE/D,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,0CAA0C;YAC1C,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAChF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YACnF,CAAC;YAED,6CAA6C;YAC7C,IAAI,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,iBAAiB,aAAa,CAAC,MAAM,kCAAkC,EACvE,oBAAoB,EACpB,mBAAmB,CACtB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACf,IAAI,SAAS,KAAK,oBAAoB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBAC5D,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;oBACpC,CAAC;yBAAM,IAAI,SAAS,KAAK,mBAAmB,EAAE,CAAC;wBAC3C,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,wCAAwC,CAAC,CAAC;oBAC7E,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,yDAAyD;QACzD,MAAM,aAAa,GAAG,IAAI,CAAC;QAC3B,IAAI,MAAM,CAAC,MAAM,GAAG,aAAa,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,QAAyB;QAC7C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEzC,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,KAAK,EAAE,CAAC;YACR,YAAY,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAEM,OAAO;QACV,mBAAmB;QACnB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,YAAY,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,gBAAgB;QAChB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEhC,6BAA6B;QAC7B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AArRD,0CAqRC"}