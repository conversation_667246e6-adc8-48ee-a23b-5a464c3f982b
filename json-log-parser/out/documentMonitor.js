"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentMonitor = void 0;
const vscode = __importStar(require("vscode"));
class DocumentMonitor {
    constructor(jsonParser, decorationManager, webviewProvider, config, dedicatedPanel) {
        this.jsonParser = jsonParser;
        this.decorationManager = decorationManager;
        this.webviewProvider = webviewProvider;
        this.config = config;
        this.dedicatedPanel = dedicatedPanel;
        this.disposables = [];
        this.isMonitoring = false;
        this.processedDocuments = new Map();
    }
    startMonitoring() {
        if (this.isMonitoring || !this.config.shouldAutoDetectInFiles()) {
            return;
        }
        this.isMonitoring = true;
        // Monitor active text editor changes
        const editorChangeDisposable = vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor && this.shouldMonitorDocument(editor.document)) {
                this.scheduleDocumentScan(editor.document);
            }
        });
        // Monitor document content changes
        const documentChangeDisposable = vscode.workspace.onDidChangeTextDocument(e => {
            if (this.shouldMonitorDocument(e.document)) {
                this.scheduleDocumentScan(e.document);
            }
        });
        this.disposables.push(editorChangeDisposable, documentChangeDisposable);
        // Process currently active editor
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor && this.shouldMonitorDocument(activeEditor.document)) {
            this.scheduleDocumentScan(activeEditor.document);
        }
    }
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        this.isMonitoring = false;
        this.dispose();
    }
    shouldMonitorDocument(document) {
        // Monitor log files, JSON files, and text files that might contain escaped JSON
        const supportedSchemes = ['file', 'untitled'];
        const supportedLanguages = ['log', 'json', 'javascript', 'typescript', 'python', 'plaintext'];
        const logFileExtensions = ['.log', '.txt', '.out', '.err'];
        if (!supportedSchemes.includes(document.uri.scheme)) {
            return false;
        }
        // Check language ID
        if (supportedLanguages.includes(document.languageId)) {
            return true;
        }
        // Check file extension
        const fileName = document.fileName.toLowerCase();
        if (logFileExtensions.some(ext => fileName.endsWith(ext))) {
            return true;
        }
        // Check if filename contains 'log'
        if (fileName.includes('log')) {
            return true;
        }
        return false;
    }
    scheduleDocumentScan(document) {
        // Clear existing timer
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        // Schedule new scan with debounce
        this.debounceTimer = setTimeout(() => {
            this.scanDocument(document);
        }, 1000); // 1 second debounce
    }
    scanDocument(document) {
        const text = document.getText();
        // Skip if document is too large or empty
        if (!text || text.length > 50000) {
            return;
        }
        // Check if we've already processed this version of the document
        const currentVersion = document.version;
        const lastProcessedVersion = this.processedDocuments.get(document);
        if (lastProcessedVersion === currentVersion) {
            return;
        }
        // Quick check if document contains potential JSON
        if (!this.jsonParser.containsJson(text)) {
            return;
        }
        // Parse JSON from document
        const parsedResults = this.jsonParser.parseEscapedJson(text);
        if (parsedResults.length > 0) {
            const fileName = this.getDocumentName(document);
            // Update both displays
            this.webviewProvider.addParsedJson(parsedResults, `File: ${fileName}`);
            if (this.dedicatedPanel) {
                this.dedicatedPanel.addParsedJson(parsedResults, `File: ${fileName}`);
            }
            // Apply decorations to the document
            this.decorationManager.applyDecorations(document, parsedResults);
            // Show notification for auto-detected JSON
            if (parsedResults.length >= 2) {
                vscode.window.showInformationMessage(`Auto-detected ${parsedResults.length} JSON objects in ${fileName}`, 'Show Dedicated Tab', 'Show Output Panel').then(selection => {
                    if (selection === 'Show Dedicated Tab' && this.dedicatedPanel) {
                        this.dedicatedPanel.showPanel();
                    }
                    else if (selection === 'Show Output Panel') {
                        vscode.commands.executeCommand('workbench.view.extension.jsonLogParser');
                    }
                });
            }
            // Mark document as processed
            this.processedDocuments.set(document, currentVersion);
        }
    }
    getDocumentName(document) {
        if (document.uri.scheme === 'untitled') {
            return 'Untitled';
        }
        const path = document.uri.fsPath;
        const fileName = path.split('/').pop() || path.split('\\').pop() || 'Unknown';
        return fileName;
    }
    dispose() {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        this.processedDocuments.clear();
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.DocumentMonitor = DocumentMonitor;
//# sourceMappingURL=documentMonitor.js.map