{"version": 3, "file": "jsonParser.js", "sourceRoot": "", "sources": ["../src/jsonParser.ts"], "names": [], "mappings": ";;;AAWA,MAAa,UAAU;IAenB,YAAY,MAA4B;QAZxC,qDAAqD;QACpC,aAAQ,GAAG;YACxB,4DAA4D;YAC5D,0BAA0B;YAC1B,mDAAmD;YACnD,0BAA0B;YAC1B,cAAc;YACd,qCAAqC;YACrC,oEAAoE;YACpE,sEAAsE;SACzE,CAAC;QAGE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,IAAY;QAChC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,CAAC;YACrD,OAAO,EAAE,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,oBAAoB;YAC3C,IAAI,KAAK,CAAC;YAEV,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC3C,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC/B,MAAM,QAAQ,GAAG,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC;gBAEjD,gCAAgC;gBAChC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBACnD,IAAI,WAAW,EAAE,CAAC;oBACd,OAAO,CAAC,IAAI,CAAC;wBACT,QAAQ,EAAE,WAAW;wBACrB,MAAM,EAAE,WAAW,CAAC,MAAM;wBAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;wBAChC,UAAU;wBACV,QAAQ;wBACR,OAAO,EAAE,IAAI;qBAChB,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;QACL,CAAC;QAED,4CAA4C;QAC5C,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,IAAY;QAC7B,IAAI,CAAC;YACD,uDAAuD;YACvD,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAE5B,+BAA+B;YAC/B,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvD,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;YAED,+BAA+B;YAC/B,IAAI,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvD,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;YAED,oBAAoB;YACpB,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAEzC,eAAe;YACf,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAErC,iDAAiD;YACjD,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gBAChD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBAClD,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YACjC,CAAC;YAED,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,IAAY;QAC7B,OAAO,IAAI;aACN,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;aACtB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;aACrB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;aACrB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAA2B;QAChD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC;QACnB,CAAC;QAED,sBAAsB;QACtB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAEpD,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;QAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,qDAAqD;YACrD,IAAI,MAAM,CAAC,UAAU,IAAI,YAAY,EAAE,CAAC;gBACpC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACtB,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC;YACnC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,IAAY;QAC5B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,CAAC;YACrD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,qCAAqC;QACrC,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3B,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,IAAS;QACnC,IAAI,CAAC;YACD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,cAAc,CAAC;QAC1B,CAAC;IACL,CAAC;CACJ;AA1JD,gCA0JC"}