"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecorationManager = void 0;
const vscode = __importStar(require("vscode"));
class DecorationManager {
    constructor(config) {
        this.config = config;
        this.activeDecorations = new Map();
        this.decorationType = this.createDecorationType();
        // Listen for configuration changes to update decoration style
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('jsonLogParser.highlightColor')) {
                this.updateDecorationType();
            }
        });
    }
    createDecorationType() {
        return vscode.window.createTextEditorDecorationType({
            backgroundColor: this.config.getHighlightColor() + '20', // 20% opacity
            border: `1px solid ${this.config.getHighlightColor()}`,
            borderRadius: '3px',
            overviewRulerColor: this.config.getHighlightColor(),
            overviewRulerLane: vscode.OverviewRulerLane.Right,
            after: {
                contentText: ' 📋 JSON',
                color: this.config.getHighlightColor(),
                fontWeight: 'bold',
                margin: '0 0 0 10px'
            }
        });
    }
    updateDecorationType() {
        this.decorationType.dispose();
        this.decorationType = this.createDecorationType();
        // Reapply decorations to all active documents
        for (const [document, decorations] of this.activeDecorations) {
            const editor = vscode.window.visibleTextEditors.find(e => e.document === document);
            if (editor) {
                editor.setDecorations(this.decorationType, decorations);
            }
        }
    }
    applyDecorations(document, results) {
        const decorations = results.map(result => {
            const startPos = document.positionAt(result.startIndex);
            const endPos = document.positionAt(result.endIndex);
            const range = new vscode.Range(startPos, endPos);
            return {
                range,
                hoverMessage: this.createHoverMessage(result)
            };
        });
        // Store decorations for this document
        this.activeDecorations.set(document, decorations);
        // Apply decorations to visible editors
        const editors = vscode.window.visibleTextEditors.filter(e => e.document === document);
        editors.forEach(editor => {
            editor.setDecorations(this.decorationType, decorations);
        });
    }
    createHoverMessage(result) {
        const markdown = new vscode.MarkdownString();
        markdown.isTrusted = true;
        markdown.appendMarkdown('**Parsed JSON:**\n\n');
        markdown.appendCodeblock(result.formatted, 'json');
        markdown.appendMarkdown('\n**Original:**\n\n');
        markdown.appendCodeblock(result.original, 'text');
        // Add copy button (this would need additional implementation)
        markdown.appendMarkdown('\n[Copy Formatted JSON](command:jsonLogParser.copyFormatted)');
        return markdown;
    }
    clearDecorations(document) {
        if (document) {
            // Clear decorations for specific document
            this.activeDecorations.delete(document);
            const editors = vscode.window.visibleTextEditors.filter(e => e.document === document);
            editors.forEach(editor => {
                editor.setDecorations(this.decorationType, []);
            });
        }
        else {
            // Clear all decorations
            this.activeDecorations.clear();
            vscode.window.visibleTextEditors.forEach(editor => {
                editor.setDecorations(this.decorationType, []);
            });
        }
    }
    dispose() {
        this.decorationType.dispose();
        this.activeDecorations.clear();
    }
}
exports.DecorationManager = DecorationManager;
//# sourceMappingURL=decorationManager.js.map