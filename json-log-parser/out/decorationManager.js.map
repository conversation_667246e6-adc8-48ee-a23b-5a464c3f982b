{"version": 3, "file": "decorationManager.js", "sourceRoot": "", "sources": ["../src/decorationManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,MAAa,iBAAiB;IAI1B,YAAoB,MAA4B;QAA5B,WAAM,GAAN,MAAM,CAAsB;QAFxC,sBAAiB,GAAG,IAAI,GAAG,EAAmD,CAAC;QAGnF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAElD,8DAA8D;QAC9D,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC1C,IAAI,CAAC,CAAC,oBAAoB,CAAC,8BAA8B,CAAC,EAAE,CAAC;gBACzD,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAChC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB;QACxB,OAAO,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;YAChD,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,GAAG,IAAI,EAAE,cAAc;YACvE,MAAM,EAAE,aAAa,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,EAAE;YACtD,YAAY,EAAE,KAAK;YACnB,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YACnD,iBAAiB,EAAE,MAAM,CAAC,iBAAiB,CAAC,KAAK;YACjD,KAAK,EAAE;gBACH,WAAW,EAAE,UAAU;gBACvB,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;gBACtC,UAAU,EAAE,MAAM;gBAClB,MAAM,EAAE,YAAY;aACvB;SACJ,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAElD,8CAA8C;QAC9C,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;YACnF,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;IACL,CAAC;IAEM,gBAAgB,CAAC,QAA6B,EAAE,OAA2B;QAC9E,MAAM,WAAW,GAA+B,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACjE,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAEjD,OAAO;gBACH,KAAK;gBACL,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;aAChD,CAAC;QACN,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QAElD,uCAAuC;QACvC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QACtF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACrB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB,CAAC,MAAwB;QAC/C,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC7C,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAE1B,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;QAChD,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAEnD,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAC/C,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAElD,8DAA8D;QAC9D,QAAQ,CAAC,cAAc,CAAC,8DAA8D,CAAC,CAAC;QAExF,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEM,gBAAgB,CAAC,QAA8B;QAClD,IAAI,QAAQ,EAAE,CAAC;YACX,0CAA0C;YAC1C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;YACtF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACrB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACJ,wBAAwB;YACxB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC9C,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;CACJ;AAvGD,8CAuGC"}