2025-06-21 10:30:15 [INFO] Application started successfully
2025-06-21 10:30:16 [DEBUG] Loading configuration from config.json
2025-06-21 10:30:17 [INFO] API Request received: "{\"method\":\"POST\",\"endpoint\":\"/api/users\",\"payload\":{\"name\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"age\":30}}"
2025-06-21 10:30:18 [INFO] Database query executed successfully
2025-06-21 10:30:19 [INFO] API Response sent: "{\"status\":\"success\",\"data\":{\"id\":12345,\"name\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"created_at\":\"2025-06-21T10:30:19Z\"}}"
2025-06-21 10:30:20 [DEBUG] Cache updated with new user data
2025-06-21 10:30:25 [INFO] OpenAI API call initiated
2025-06-21 10:30:26 [DEBUG] OpenAI Request: "{\"model\":\"gpt-4\",\"messages\":[{\"role\":\"system\",\"content\":\"You are a helpful assistant.\"},{\"role\":\"user\",\"content\":\"Explain JSON parsing in simple terms.\"}],\"temperature\":0.7,\"max_tokens\":150}"
2025-06-21 10:30:28 [INFO] OpenAI Response received: "{\"id\":\"chatcmpl-123456\",\"object\":\"chat.completion\",\"created\":1703097628,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"JSON parsing is the process of converting JSON text into a data structure that your program can work with. Think of it like translating a written recipe into ingredients and steps your kitchen robot can understand.\"},\"finish_reason\":\"stop\"}],\"usage\":{\"prompt_tokens\":45,\"completion_tokens\":38,\"total_tokens\":83}}"
2025-06-21 10:30:29 [INFO] Response processed and cached
2025-06-21 10:30:30 [ERROR] Validation error occurred: "{\"error\":\"VALIDATION_FAILED\",\"details\":{\"field\":\"email\",\"message\":\"Invalid email format\",\"code\":400}}"
2025-06-21 10:30:31 [INFO] Error logged to monitoring system
2025-06-21 10:30:35 [INFO] Batch processing started
2025-06-21 10:30:36 [DEBUG] Processing batch items: [{"id":1,"type":"user","data":{"name":"Alice"}},{"id":2,"type":"user","data":{"name":"Bob"}},{"id":3,"type":"user","data":{"name":"Charlie"}}]
2025-06-21 10:30:37 [INFO] Batch processing completed successfully
2025-06-21 10:30:40 [INFO] Application metrics: "{\"requests_processed\":156,\"average_response_time\":\"245ms\",\"active_connections\":23,\"memory_usage\":\"512MB\",\"cpu_usage\":\"15%\"}"
2025-06-21 10:30:45 [INFO] Scheduled task executed
2025-06-21 10:30:50 [INFO] Application shutdown initiated
