2025-06-21 13:45:00 [INFO] API Request started
2025-06-21 13:45:01 [DEBUG] Request payload: "{\\"method\\":\\"POST\\",\\"url\\":\\"https://api.openai.com/v1/chat/completions\\",\\"headers\\":{\\"Authorization\\":\\"Bearer sk-xxx\\",\\"Content-Type\\":\\"application/json\\"}}"
2025-06-21 13:45:01 [DEBUG] Request body: "{\\"model\\":\\"gpt-4\\",\\"messages\\":[{\\"role\\":\\"user\\",\\"content\\":\\"Hello, how are you?\\"}],\\"temperature\\":0.7,\\"max_tokens\\":150}"
2025-06-21 13:45:03 [INFO] API Response received
2025-06-21 13:45:03 [DEBUG] Response: {"status":200,"data":{"id":"chatcmpl-123","object":"chat.completion","created":1703123456,"model":"gpt-4","choices":[{"index":0,"message":{"role":"assistant","content":"Hello! I'm doing well, thank you for asking. How can I assist you today?"},"finish_reason":"stop"}],"usage":{"prompt_tokens":12,"completion_tokens":18,"total_tokens":30}}}
2025-06-21 13:45:03 [INFO] Processing complete
2025-06-21 13:45:04 [ERROR] Database error: '{"error":"connection_timeout","details":{"host":"localhost","port":5432,"timeout_ms":5000},"timestamp":"2025-06-21T13:45:04.123Z"}'
2025-06-21 13:45:05 [WARN] Retry attempt: [{"attempt":1,"max_retries":3,"delay_ms":1000}]
2025-06-21 13:45:06 [INFO] User action logged: "{\\"user_id\\":12345,\\"action\\":\\"chat_completion\\",\\"metadata\\":{\\"model\\":\\"gpt-4\\",\\"tokens_used\\":30,\\"cost\\":0.0006}}"
