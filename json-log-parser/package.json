{"name": "json-log-parser", "displayName": "JSON Log Parser", "description": "Automatically detect, parse, and display escaped JSON strings in terminal output, log files, and clipboard content", "version": "1.0.0", "publisher": "your-publisher-name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/json-log-parser.git"}, "bugs": {"url": "https://github.com/your-username/json-log-parser/issues"}, "homepage": "https://github.com/your-username/json-log-parser#readme", "engines": {"vscode": "^1.84.0"}, "categories": ["Other", "Debuggers", "Formatters"], "keywords": ["json", "logs", "parser", "console", "api", "openai", "terminal", "clipboard", "automation", "debugging", "development"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"configuration": {"title": "JSON Log Parser", "properties": {"jsonLogParser.enabled": {"type": "boolean", "default": true, "description": "Enable JSON log parsing"}, "jsonLogParser.autoDetect": {"type": "boolean", "default": true, "description": "Automatically detect escaped JSON in terminal output"}, "jsonLogParser.highlightColor": {"type": "string", "default": "#4CAF50", "description": "Color for highlighting detected JSON"}, "jsonLogParser.maxLogSize": {"type": "number", "default": 10000, "description": "Maximum characters to process in a single log entry"}, "jsonLogParser.showInSidebar": {"type": "boolean", "default": true, "description": "Show parsed JSON in sidebar panel"}, "jsonLogParser.autoDetectInFiles": {"type": "boolean", "default": true, "description": "Automatically detect escaped JSON in open files"}, "jsonLogParser.autoDetectClipboard": {"type": "boolean", "default": true, "description": "Automatically detect JSON when clipboard content changes"}, "jsonLogParser.autoDetectFiles": {"type": "boolean", "default": true, "description": "Automatically monitor log files in workspace for JSON content"}}}, "commands": [{"command": "jsonLogParser.parseSelection", "title": "Parse Selected JSON", "category": "JSON Log Parser"}, {"command": "jsonLogParser.toggleParsing", "title": "Toggle JSON Parsing", "category": "JSON Log Parser"}, {"command": "jsonLogParser.clearCache", "title": "Clear Parsed JSO<PERSON> Cache", "category": "JSON Log Parser"}, {"command": "jsonLogParser.showResults", "title": "Show Parsed JSON Results", "category": "JSON Log Parser"}, {"command": "jsonLogParser.openDedicatedPanel", "title": "Open JSON Parser <PERSON>b", "category": "JSON Log Parser", "icon": "$(json)"}, {"command": "jsonLogParser.parseTerminalOutput", "title": "Parse Terminal Output", "category": "JSON Log Parser"}, {"command": "jsonLogParser.parseClipboard", "title": "Parse J<PERSON> from Clipboard", "category": "JSON Log Parser"}, {"command": "jsonLogParser.simulateTerminalJson", "title": "Simulate Terminal JSON Output", "category": "JSON Log Parser"}], "views": {"panel": [{"id": "jsonLogParser.parsedJsonView", "name": "JSON Parser Results", "when": "jsonLogParser.enabled"}]}, "menus": {"editor/context": [{"command": "jsonLogParser.parseSelection", "when": "editorHasSelection", "group": "1_modification"}], "commandPalette": [{"command": "jsonLogParser.parseSelection", "when": "editorHasSelection"}, {"command": "jsonLogParser.openDedicatedPanel", "title": "JSON Log Parser: Open Dedicated Tab"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "test-compile": "tsc -p ./"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.6", "@types/node": "18.x", "@types/vscode": "^1.84.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vscode/test-electron": "^2.3.8", "eslint": "^8.56.0", "glob": "^8.1.0", "mocha": "^10.2.0", "typescript": "^5.3.3"}, "dependencies": {"highlight.js": "^11.9.0"}}