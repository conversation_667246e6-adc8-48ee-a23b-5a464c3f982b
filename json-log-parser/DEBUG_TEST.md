# 🔧 调试测试指南

## 问题诊断

从截图可以看到：
- ✅ 专用标签页成功创建
- ✅ JSON解析功能正常（左侧显示结果）
- ❌ 专用标签页没有显示数据

## 调试步骤

### 1. 重新启动扩展
1. 在VS Code中按 `F5` 重新启动扩展开发主机
2. 等待扩展加载完成

### 2. 打开开发者工具
1. 在扩展开发主机中，按 `Cmd+Shift+I` (Mac) 或 `Ctrl+Shift+I` (Windows)
2. 查看Console标签页中的日志

### 3. 测试手动解析
1. 选择以下测试文本：
```
API Response: "{\"status\":\"success\",\"data\":{\"id\":123,\"name\":\"John Doe\"}}"
```

2. 右键选择 "Parse Selected JSON"
3. 查看Console中的调试信息：
   - 应该看到 "JSON Log Parser: Parsing selected text..."
   - 应该看到 "JSON Log Parser: Found X JSON objects"
   - 应该看到 "JSON Log Parser: Adding X results..."
   - 应该看到 "JSON Log Parser Webview: Script loaded"
   - 应该看到 "JSON Log Parser Webview: Received message"

### 4. 检查专用标签页
1. 专用标签页应该自动打开
2. 如果没有显示数据，检查Console中是否有JavaScript错误
3. 查找以 "JSON Parser Webview:" 开头的日志

### 5. 手动打开专用标签页
1. 按 `Cmd+Shift+P` (Mac) 或 `Ctrl+Shift+P` (Windows)
2. 输入 "JSON Log Parser: Open Dedicated Tab"
3. 执行命令

## 预期的Console日志

正常情况下应该看到：
```
JSON Log Parser: Parsing selected text: API Response: "{\"status\"...
JSON Log Parser: Found 1 JSON objects
JSON Log Parser: Adding 1 results from Manual Selection
JSON Log Parser: Total logs now: 1
JSON Log Parser: Panel not open, will show panel
JSON Log Parser: showPanel called
JSON Log Parser: Creating new panel
JSON Log Parser: Panel created successfully
JSON Log Parser: Sending update to webview with 1 logs
JSON Parser Webview: Script loaded
JSON Parser Webview: Received message {type: 'update', logs: [...]}
JSON Parser Webview: updateContent called with [...]
JSON Parser Webview: Rendering 1 logs
```

## 如果仍然有问题

如果专用标签页仍然不显示数据：

1. **检查JavaScript错误**：在开发者工具的Console中查看是否有红色错误信息

2. **检查webview内容**：在专用标签页中右键选择"检查元素"，查看webview的内容

3. **重新编译**：
   ```bash
   npm run compile
   ```

4. **完全重启**：关闭VS Code扩展开发主机，重新按F5启动

## 测试数据

以下是一些测试用的转义JSON字符串：

### 简单JSON
```
Log: "{\"message\":\"Hello World\",\"timestamp\":\"2024-01-01\"}"
```

### 复杂JSON
```
API Response: "{\"status\":\"success\",\"data\":{\"users\":[{\"id\":1,\"name\":\"Alice\"},{\"id\":2,\"name\":\"Bob\"}],\"total\":2}}"
```

### OpenAI格式
```
OpenAI Request: "{\"model\":\"gpt-4\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello, how are you?\"}],\"temperature\":0.7}"
```
