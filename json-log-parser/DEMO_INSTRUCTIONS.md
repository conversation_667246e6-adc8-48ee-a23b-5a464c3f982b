# 🚀 JSON Log Parser 演示指南

## 快速测试步骤

### 1. 启动扩展
```bash
cd json-log-parser
npm run compile
code .
# 在VS Code中按 F5 启动扩展开发主机
```

### 2. 测试专用标签页功能

1. **打开演示文件**：
   - 在新窗口中打开 `demo.md` 文件

2. **选择并解析JSON**：
   - 选择这段文本：
     ```
     API Response: "{\"status\":\"success\",\"data\":{\"id\":123,\"name\":\"<PERSON> Doe\"}}"
     ```
   - 右键选择 **"Parse Selected JSON"**

3. **查看结果**：
   - 会弹出通知消息：`Found 1 JSON object(s) - Check the dedicated "JSON Parser" tab or "JSON Parser Results" in Output panel`
   - 点击 **"Show Dedicated Tab"** 
   - 专用的 **"📋 JSON Parser"** 标签页会自动打开
   - 这个标签页位于底部面板，与Terminal、Output等并列

### 3. 测试多种显示方式

**方法A：专用标签页（推荐）**
- 自动打开的专用标签页
- 美观的界面设计
- 完整的交互功能

**方法B：输出面板（备用）**
- 点击通知中的 **"Show Output Panel"**
- 或者手动打开：底部面板 → OUTPUT → 选择 "JSON Parser Results"

### 4. 测试不同的JSON格式

尝试解析这些不同格式的JSON：

**双转义JSON**：
```
Log: "{\"user\":{\"id\":123,\"name\":\"John\"},\"action\":\"login\"}"
```

**OpenAI API格式**：
```
Request: "{\"model\":\"gpt-4\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}]}"
```

**JSON数组**：
```
Items: [{"id":1,"name":"Item1"},{"id":2,"name":"Item2"}]
```

### 5. 测试自动检测功能

**测试文件自动检测**：
1. 打开 `test-auto-detection.log` 文件
2. 扩展会自动检测文件中的转义JSON
3. 如果检测到2个或更多JSON对象，会显示通知
4. 专用标签页会自动显示检测到的JSON

**测试终端自动检测**：
1. 在终端中运行：
   ```bash
   echo "API Response: \"{\\\"status\\\":\\\"success\\\",\\\"data\\\":{\\\"id\\\":123}}\""
   echo "Error occurred: \"{\\\"error\\\":\\\"VALIDATION_FAILED\\\",\\\"code\\\":400}\""
   ```
2. 扩展会自动检测终端输出中的JSON
3. 结果会自动显示在专用标签页中

### 6. 测试命令面板

- 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
- 输入 **"JSON Log Parser: Open Dedicated Tab"**
- 直接打开专用标签页

## 🎯 预期结果

### 专用标签页应该显示：

```
📋 JSON Parser Results                    [Clear All]
1 session(s), 1 JSON object(s)

┌─────────────────────────────────────────────────────┐
│ Manual Selection                    12/20/2025 8:00 PM │
├─────────────────────────────────────────────────────┤
│ 📋 JSON Object 1                                    │
│                                                     │
│ ✨ Formatted JSON:                                  │
│ {                                                   │
│   "status": "success",                              │
│   "data": {                                         │
│     "id": 123,                                      │
│     "name": "John Doe"                              │
│   }                                                 │
│ }                                                   │
│                                                     │
│ 📝 Original Text:                                   │
│ "{\"status\":\"success\",\"data\":{\"id\":123,\"name\":\"John Doe\"}}" │
│                                                     │
│ [Copy Formatted] [Copy Original]                    │
└─────────────────────────────────────────────────────┘
```

### 关键特性：

✅ **专用标签页** - 与Terminal、Output等并列显示
✅ **双重显示** - 专用标签页 + 输出面板
✅ **智能通知** - 提供两种查看选项
✅ **美观界面** - 卡片式设计，易于阅读
✅ **完整功能** - 复制、清除、统计等
✅ **易于发现** - 明显的标签页位置
✅ **自动检测** - 监控文件、终端和输出面板
✅ **智能过滤** - 只监控相关文件类型（.log, .txt, .json等）
✅ **性能优化** - 防抖处理，避免重复检测

## 🔧 故障排除

如果专用标签页没有出现：

1. **检查编译**：确保 `npm run compile` 成功
2. **重新加载**：在扩展开发主机中按 `Ctrl+R` 重新加载
3. **手动打开**：使用命令面板 → "JSON Log Parser: Open Dedicated Tab"
4. **查看控制台**：按 `F12` 查看是否有错误信息

## 🎉 成功标志

当您看到以下情况时，说明功能正常工作：

- ✅ 解析JSON后自动弹出通知
- ✅ 通知提供两个选项：专用标签页和输出面板
- ✅ 专用标签页出现在底部面板，与其他标签页并列
- ✅ 标签页显示美观的JSON格式和交互按钮
- ✅ 输出面板也同时显示结果作为备用选项
