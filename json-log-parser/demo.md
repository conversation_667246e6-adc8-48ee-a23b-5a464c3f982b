# JSON Log Parser 演示

这个文件包含一些示例，用于测试JSON Log Parser扩展的功能。

## 测试步骤

1. 在VS Code中按 `F5` 启动扩展开发主机
2. 在新窗口中打开这个文件
3. 选择下面的任何一个JSON字符串
4. 右键选择 "Parse Selected JSON" 或使用命令面板

## 示例1: 双转义JSON（常见于日志）

```
API Response: "{\"status\":\"success\",\"data\":{\"id\":123,\"name\":\"<PERSON>\"}}"
```

## 示例2: OpenAI API请求日志

```
OpenAI Request: "{\"model\":\"gpt-4\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello, how are you?\"}],\"temperature\":0.7}"
```

## 示例3: 单转义JSON

```
Log entry: {"timestamp":"2025-06-20T12:00:00Z","level":"info","message":"User login successful","user":{"id":456,"email":"<EMAIL>"}}
```

## 示例4: JSON数组

```
Items: [{"id":1,"name":"Item1","price":29.99},{"id":2,"name":"Item2","price":39.99}]
```

## 示例5: 复杂嵌套JSON

```
Complex data: "{\"response\":{\"choices\":[{\"message\":{\"role\":\"assistant\",\"content\":\"Hello! I'm doing well, thank you for asking.\"},\"finish_reason\":\"stop\"}],\"usage\":{\"prompt_tokens\":15,\"completion_tokens\":12,\"total_tokens\":27}}}"
```

## 终端测试

你也可以在终端中运行这些命令来测试自动检测功能：

```bash
echo "API Response: \"{\\\"status\\\":\\\"success\\\",\\\"data\\\":{\\\"id\\\":123}}\""
echo "OpenAI Request: \"{\\\"model\\\":\\\"gpt-4\\\",\\\"messages\\\":[{\\\"role\\\":\\\"user\\\",\\\"content\\\":\\\"Hello\\\"}]}\""
```

## 预期行为

- 扩展应该自动检测并高亮显示JSON字符串
- 在侧边栏的"Parsed JSON Logs"面板中显示格式化的JSON
- 提供复制原始和格式化JSON的功能
- 显示悬停提示，包含格式化的JSON预览

## 配置选项

在VS Code设置中搜索"JSON Log Parser"来配置：

- 启用/禁用自动检测
- 更改高亮颜色
- 设置最大日志大小
- 控制侧边栏显示
