# 🚀 快速测试指南

## 立即测试步骤

### 1. 重新启动扩展
1. 在VS Code中按 `F5` 重新启动扩展开发主机
2. 等待扩展加载完成

### 2. 打开开发者工具
1. 在扩展开发主机中，按 `Cmd+Shift+I` (Mac) 或 `Ctrl+Shift+I` (Windows)
2. 切换到 Console 标签页

### 3. 测试专用面板
1. 按 `Cmd+Shift+P` (Mac) 或 `Ctrl+Shift+P` (Windows)
2. 输入 "JSON Log Parser: Open Dedicated Tab"
3. 执行命令
4. 应该看到专用标签页打开

### 4. 测试JSON解析
选择以下文本并右键选择 "Parse Selected JSON"：

```
API Response: "{\"status\":\"success\",\"data\":{\"id\":123,\"name\":\"John Doe\"}}"
```

### 5. 查看Console日志
在开发者工具的Console中，应该看到类似这样的日志：

```
JSON Log Parser: Parsing selected text: API Response: "{\"status\"...
JSON Log Parser: Found 1 JSON objects
JSON Log Parser: Adding 1 results from Manual Selection
JSON Log Parser: showPanel called
JSON Log Parser: Creating new panel
JSON Log Parser Webview: Script loaded
JSON Log Parser Webview: Received message
JSON Log Parser Webview: updateContent called with [...]
```

### 6. 检查专用标签页
- 专用标签页应该显示解析后的JSON
- 如果仍然显示 "Ready to Parse JSON"，说明数据传递有问题

## 如果仍然有问题

### 检查JavaScript错误
在Console中查看是否有红色的错误信息，特别是：
- SyntaxError
- ReferenceError
- TypeError

### 检查webview
1. 在专用标签页中右键
2. 选择 "检查元素" 或 "Inspect"
3. 查看webview的Console是否有错误

### 重新编译
```bash
cd json-log-parser
npm run compile
```

## 测试用例

### 简单JSON
```
Log: "{\"message\":\"Hello World\"}"
```

### 复杂JSON
```
Response: "{\"users\":[{\"id\":1,\"name\":\"Alice\"}],\"total\":1}"
```

### OpenAI格式
```
Request: "{\"model\":\"gpt-4\",\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}]}"
```

## 预期结果

成功时，专用标签页应该显示：
- 📋 JSON Parser Results (标题)
- 解析后的格式化JSON
- 原始转义文本
- Copy按钮

如果看到这些内容，说明功能正常工作！
