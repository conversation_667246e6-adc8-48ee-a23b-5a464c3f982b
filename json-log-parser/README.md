# JSON Log Parser

A powerful Visual Studio Code extension that automatically detects, parses, and displays escaped JSON strings from multiple sources including terminal output, log files, and clipboard content.

## ✨ Features

- **🚀 Automatic Detection**: Automatically detects escaped JSON in terminal output, log files, and clipboard
- **⚡ Real-time Processing**: Processes JSON as it appears with intelligent monitoring
- **🎨 Beautiful Display**: Clean, syntax-highlighted JSON formatting in a dedicated tab
- **📋 Smart Clipboard Monitoring**: Auto-detects JSON when you copy terminal output
- **📁 File System Monitoring**: Watches log files for JSON content changes
- **🔧 Easy Copy**: One-click copying of both original and formatted JSON
- **⚙️ Configurable**: Customizable auto-detection settings

## Supported JSON Patterns

The extension can detect and parse various escaped JSON patterns:

- Double-escaped JSON: `"{\\"key\\":\\"value\\"}"`
- Single-escaped JSON: `{"key":"value"}`
- JSON in single quotes: `'{"key":"value"}'`
- JSON arrays: `[{"item1": "value1"}, {"item2": "value2"}]`

## 🎯 Use Cases

- **API Development**: Parse OpenAI API request/response logs automatically
- **Debugging**: View formatted JSON from application logs in real-time
- **Log Analysis**: Extract and analyze JSON data from various log sources
- **Terminal Workflows**: Auto-detect JSON in command outputs
- **Development Tools**: Work with stringified JSON from various tools and frameworks

## 📦 Installation

### From VS Code Marketplace
1. Open VS Code
2. Go to Extensions (`Ctrl+Shift+X` / `Cmd+Shift+X`)
3. Search for "JSON Log Parser"
4. Click Install

### From VSIX
1. Download the `.vsix` file
2. Open VS Code
3. Run `Extensions: Install from VSIX...` from Command Palette
4. Select the downloaded file

## 🚀 Usage

### Automatic Detection (Zero Configuration!)

The extension automatically monitors:
- **Terminal Output**: Detects JSON in command outputs
- **Log Files**: Watches `.log`, `.out`, `.err` files in your workspace
- **Clipboard**: Auto-detects when you copy JSON content

When escaped JSON is detected, it will:
- Parse and format the JSON automatically
- Display results in a dedicated "JSON Parser" tab
- Show notifications for significant findings

### Manual Parsing

1. **Select & Parse**: Select text containing escaped JSON → Right-click → "Parse Selected JSON"
2. **Clipboard Parse**: Copy JSON content → Use "Parse JSON from Clipboard" command
3. **Command Palette**: `Ctrl+Shift+P` → "JSON Log Parser: Parse Selected JSON"

### Configuration

Access settings via File > Preferences > Settings, then search for "JSON Log Parser":

- `jsonLogParser.enabled`: Enable/disable the extension
- `jsonLogParser.autoDetect`: Automatically detect JSON in terminal output
- `jsonLogParser.highlightColor`: Color for highlighting detected JSON
- `jsonLogParser.maxLogSize`: Maximum characters to process in a single log entry
- `jsonLogParser.showInSidebar`: Show parsed JSON in sidebar panel

## Commands

- `JSON Log Parser: Parse Selected JSON` - Parse selected text for JSON
- `JSON Log Parser: Toggle JSON Parsing` - Enable/disable parsing
- `JSON Log Parser: Clear Parsed JSON Cache` - Clear the parsed JSON history

## Development

### Building from Source

1. Clone the repository
2. Run `npm install`
3. Run `npm run compile`
4. Press F5 to open a new Extension Development Host window

### Testing

Run `npm test` to execute the test suite.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT License - see LICENSE file for details.

## Changelog

### 0.0.1

- Initial release
- Basic JSON detection and parsing
- Terminal and output channel monitoring
- Webview display with syntax highlighting
- Configuration options
