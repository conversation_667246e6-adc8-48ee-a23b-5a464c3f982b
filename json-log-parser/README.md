# JSON Log Parser

A Visual Studio Code extension that automatically detects, parses, and displays escaped JSON strings in console output and logs.

## Features

- **Automatic Detection**: Automatically detects escaped JSON in terminal output and log files
- **Real-time Parsing**: Processes JSON as it appears in the console
- **Syntax Highlighting**: Beautiful JSON formatting with syntax highlighting
- **Multiple Views**: Toggle between original escaped strings and formatted JSON
- **Copy Functionality**: Easy copying of both original and formatted JSON
- **Configurable**: Customizable highlighting colors and behavior

## Supported JSON Patterns

The extension can detect and parse various escaped JSON patterns:

- Double-escaped JSON: `"{\\"key\\":\\"value\\"}"`
- Single-escaped JSON: `{"key":"value"}`
- <PERSON><PERSON><PERSON> in single quotes: `'{"key":"value"}'`
- JSON arrays: `[{"item1": "value1"}, {"item2": "value2"}]`

## Use Cases

- **API Development**: Parse OpenAI API request/response logs
- **Debugging**: View formatted JSON from application logs
- **Log Analysis**: Extract and analyze JSON data from various log sources
- **Development Tools**: Work with stringified JSON from various tools and frameworks

## Installation

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "JSON Log Parser"
4. Click Install

## Usage

### Automatic Detection

The extension automatically monitors:
- VS Code integrated terminal output
- Output panels from various extensions
- Log files opened in the editor

When escaped JSON is detected, it will:
- Highlight the JSON in the original text
- Display parsed JSON in the sidebar panel
- Show hover information with formatted JSON

### Manual Parsing

1. Select text containing escaped JSON
2. Right-click and choose "Parse Selected JSON"
3. Or use Command Palette: `JSON Log Parser: Parse Selected JSON`

### Configuration

Access settings via File > Preferences > Settings, then search for "JSON Log Parser":

- `jsonLogParser.enabled`: Enable/disable the extension
- `jsonLogParser.autoDetect`: Automatically detect JSON in terminal output
- `jsonLogParser.highlightColor`: Color for highlighting detected JSON
- `jsonLogParser.maxLogSize`: Maximum characters to process in a single log entry
- `jsonLogParser.showInSidebar`: Show parsed JSON in sidebar panel

## Commands

- `JSON Log Parser: Parse Selected JSON` - Parse selected text for JSON
- `JSON Log Parser: Toggle JSON Parsing` - Enable/disable parsing
- `JSON Log Parser: Clear Parsed JSON Cache` - Clear the parsed JSON history

## Development

### Building from Source

1. Clone the repository
2. Run `npm install`
3. Run `npm run compile`
4. Press F5 to open a new Extension Development Host window

### Testing

Run `npm test` to execute the test suite.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT License - see LICENSE file for details.

## Changelog

### 0.0.1

- Initial release
- Basic JSON detection and parsing
- Terminal and output channel monitoring
- Webview display with syntax highlighting
- Configuration options
