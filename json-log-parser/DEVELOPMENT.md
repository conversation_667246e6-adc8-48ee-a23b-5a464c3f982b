# 开发指南

## 快速开始

1. **安装依赖**：
   ```bash
   npm install
   ```

2. **编译项目**：
   ```bash
   npm run compile
   ```

3. **运行测试**：
   ```bash
   npm test
   ```

4. **启动开发模式**：
   - 在VS Code中打开项目
   - 按 `F5` 启动扩展开发主机
   - 在新窗口中测试扩展功能

## 项目结构

```
src/
├── extension.ts          # 扩展入口点
├── jsonParser.ts         # 核心JSON解析逻辑
├── terminalMonitor.ts    # 终端输出监控
├── outputMonitor.ts      # 输出面板监控
├── decorationManager.ts  # 文本装饰管理
├── webviewProvider.ts    # 侧边栏显示
├── configuration.ts      # 配置管理
└── test/
    ├── jsonParser.test.ts    # 单元测试
    ├── runTest.ts           # 测试运行器
    └── suite/
        └── index.ts         # 测试套件
```

## 核心功能

### JSON检测模式

扩展支持以下JSON模式：

1. **双转义JSON**: `"{\\"key\\":\\"value\\"}"`
2. **单转义JSON**: `{"key":"value"}`
3. **单引号JSON**: `'{"key":"value"}'`
4. **JSON数组**: `[{"item1": "value1"}]`

### 监控机制

- **终端监控**: 实时监控VS Code集成终端的输出
- **输出面板监控**: 监控各种扩展的输出面板
- **手动解析**: 支持选择文本手动解析

### 性能优化

- 防抖处理：避免高频率处理
- 大小限制：限制处理的日志大小
- 缓存管理：LRU缓存已处理的结果
- 重复检测：避免重复处理相同内容

## 开发技巧

### 调试

1. 在扩展开发主机中按 `Ctrl+Shift+I` 打开开发者工具
2. 查看控制台输出和错误信息
3. 使用 `console.log` 进行调试

### 测试

- 使用 `demo.md` 文件测试各种JSON格式
- 在终端中运行示例命令测试自动检测
- 修改配置测试不同设置

### 发布准备

1. 更新版本号在 `package.json`
2. 更新 `CHANGELOG.md`
3. 运行完整测试套件
4. 使用 `vsce package` 打包扩展

## 常见问题

### 编译错误

如果遇到TypeScript编译错误：
```bash
npm run compile
```

### 测试失败

如果测试失败，检查：
1. 依赖是否正确安装
2. TypeScript是否编译成功
3. VS Code版本是否兼容

### 扩展不工作

1. 检查扩展是否正确激活
2. 查看开发者工具中的错误信息
3. 确认配置设置是否正确

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 添加测试
4. 确保所有测试通过
5. 提交Pull Request

## API参考

### JsonParser类

主要方法：
- `parseEscapedJson(text: string): ParsedJsonResult[]`
- `containsJson(text: string): boolean`
- `formatWithHighlighting(json: any): string`

### 配置选项

- `jsonLogParser.enabled`: 启用/禁用扩展
- `jsonLogParser.autoDetect`: 自动检测功能
- `jsonLogParser.highlightColor`: 高亮颜色
- `jsonLogParser.maxLogSize`: 最大日志大小
- `jsonLogParser.showInSidebar`: 侧边栏显示
